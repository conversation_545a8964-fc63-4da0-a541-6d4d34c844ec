import React, { useEffect, useState, memo } from "react";
import {
  Bar,
  CartesianGrid,
  BarChart,
  XAxis,
  YAxis,
} from "recharts";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import apiService from "@/services/api.service";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

/**
 * ARiL-Chart Komponente
 * 
 * Zeigt die ARiL-Daten als gestapelte Balken:
 * 
 * Balken 1 (Cutting Lager - gestapelt):
 * - Cutting Lager Kunde (unten)
 * - Cutting Lager Rest (oben)
 * 
 * Balken 2 (Positionen und Umlagerungen - gestapelt):
 * - WaTa Positionen (unten)
 * - Umlagerungen (oben)
 * 
 * Balken 3 (Lager Cutting - e<PERSON>zeln):
 * - Lager Cutting
 * 
 * @param data Optional: <PERSON><PERSON><PERSON> mit den ARiL-Daten
 */

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

// Definition des Datentyps für die ARiL-Daten
interface ArilDataPoint {
  Datum: string;
  waTaPositionen: number;
  cuttingLagerKunde: number;
  cuttingLagerRest: number;
  Umlagerungen: number;
  lagerCutting: number;
}

interface ArilDataChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: ArilDataPoint[];
  dateRange?: DateRange;
}

// Footer-Komponente für die ARiL-Chart - Verwendet nur Daten aus der Datenbank
export function ArilDataChartFooter() {
  const { t } = useTranslation();
  
  return (
    <div className="w-full">
      <div className="flex justify-between w-full">
        <div className="text-muted-foreground text-xs">
          {t("updated")}: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}

export const ArilDataChart = memo(function ArilDataChart({ data: propData, dateRange }: ArilDataChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<ArilDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Hilfsfunktion zum Parsen von Datums-Strings im Format YYYY-MM-DD oder DD.MM.YYYY
  const parseDate = (dateStr: string): Date => {
    // Versuche zuerst YYYY-MM-DD Format (Datenbank-Standard)
    if (dateStr.includes('-')) {
      return new Date(dateStr);
    }
    // Fallback für DD.MM.YYYY Format
    const parts = dateStr.split(".");
    if (parts.length < 3) return new Date(0); // Ungültiges Datum zurückgeben
    return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
  };

  // Konfiguration für das Diagramm - Gestapelte Balken
  const chartConfig = {
    // Balken 1: Cutting Lager (Kunde + Rest)
    cuttingLagerKunde: {
      label: "Cutting Lager Kunde",
      color: "var(--chart-1)",
    },
    cuttingLagerRest: {
      label: "Cutting Lager Rest", 
      color: "var(--chart-2)",
    },
    // Balken 2: Positionen und Umlagerungen
    waTaPositionen: {
      label: "WaTa Positionen",
      color: "var(--chart-3)",
    },
    Umlagerungen: {
      label: "Umlagerungen",
      color: "var(--chart-4)",
    },
    // Balken 3: Lager Cutting (einzeln)
    lagerCutting: {
      label: "Lager Cutting",
      color: "var(--chart-5)",
    },
  };

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Konvertiere DateRange zu String-Format für API
      const startDate = dateRange?.from ? 
        `${dateRange.from.getFullYear()}-${String(dateRange.from.getMonth() + 1).padStart(2, '0')}-${String(dateRange.from.getDate()).padStart(2, '0')}` 
        : undefined;
      const endDate = dateRange?.to ? 
        `${dateRange.to.getFullYear()}-${String(dateRange.to.getMonth() + 1).padStart(2, '0')}-${String(dateRange.to.getDate()).padStart(2, '0')}` 
        : undefined;

      const result = await apiService.getArilData(startDate, endDate) as ArilDataPoint[];

      if (result && Array.isArray(result)) {
        const processedData = result.map((item: any) => ({
          Datum: item.Datum || item.datum || '',
          waTaPositionen: Number(item.waTaPositionen) || 0,
          cuttingLagerKunde: Number(item.cuttingLagerKunde) || 0,
          cuttingLagerRest: Number(item.cuttingLagerRest) || 0,
          Umlagerungen: Number(item.Umlagerungen) || 0,
          lagerCutting: Number(item.lagerCutting) || 0,
        }));



        let filteredData = processedData;
        if (dateRange?.from || dateRange?.to) {
          filteredData = processedData.filter((item: ArilDataPoint) => {
            try {
              const itemDate = parseDate(item.Datum);
              if (isNaN(itemDate.getTime())) return true; // Ignoriere ungültige Daten

              const from = dateRange.from;
              const to = dateRange.to;

              if (from && itemDate < from) return false;
              if (to && itemDate > to) return false;
              
              return true;
            } catch (e) {

              return true;
            }
          });
        }

        const sortedData = filteredData.sort((a, b) => {
          try {
            const dateA = parseDate(a.Datum);
            const dateB = parseDate(b.Datum);
            return dateA.getTime() - dateB.getTime();
          } catch (e) {
            return 0;
          }
        });

        setChartData(sortedData);
      } else {
        setChartData([]);
        setError("Keine gültigen Daten erhalten");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Fehler beim Laden der ARiL-Daten");
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [dateRange]);

  // Berechne Durchschnittswerte für den Footer, wenn Daten vorhanden sind
  const avgCuttingLager = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.cuttingLagerKunde + item.cuttingLagerRest, 0) / chartData.length : 0;
  const avgPositionenUmlagerungen = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.Umlagerungen, 0) / chartData.length : 0;
  const avgLagerCutting = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.lagerCutting + item.waTaPositionen, 0) / chartData.length : 0;
  const totalCuttingLager = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.cuttingLagerKunde + item.cuttingLagerRest, 0) : 0;
  const totalPositionenUmlagerungen = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.Umlagerungen, 0) : 0;
  const totalLagerCutting = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.lagerCutting+ item.waTaPositionen, 0) : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <Loader2 className="h-12 w-12 animate-spin" />
        <p className="mt-4 font-bold">Lade Daten...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold text-red-500">{error}</p>
        <Button onClick={loadData} variant="outline">Erneut versuchen</Button>
      </div>
    );
  }

  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-105 border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>Bewegungen ARiL</CardTitle>
        <CardDescription>
          Vergleich der Bewegungen: Einlagerung, Auslagerung, Umlagerungen
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="w-full h-60"
        >
          <BarChart data={chartData} margin={{ top: 5, right: 12, left: 12, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="Datum" 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              height={40}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {
                try {
                  // value ist hier der Datums-String
                  const date = parseDate(value);
                  if (isNaN(date.getTime())) return value; // Fallback für ungültige Daten
                  // Formatiere das Datum als TT.MM
                  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                } catch (e) {
                  return value;
                }
              }}
            />
            <YAxis 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={1}
              tick={{ fontSize: 12 }}
              width={35}
              // Label für die Y-Achse
              label={{ 
                value: "Anzahl", 
                angle: -90, 
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12 },
                offset: -5
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelClassName="font-bold"
                  labelFormatter={(label) => {
                    // Formatiere das Datum im Tooltip als DD.MM.YYYY
                    try {
                      // label ist hier der Datums-String
                      const date = parseDate(label);
                      if (isNaN(date.getTime())) return `Datum: ${label}`;
                      return `Datum: ${date.toLocaleDateString('de-DE')}`;
                    } catch (e) {
                      return `Datum: ${label}`;
                    }
                  }}
                />
              }
            />
            <ChartLegend content={({ payload }) => (
              <ChartLegendContent 
                payload={payload} 
                className="p-2 rounded-md"
              />
            )} />
            
            {/* Balken 1: Cutting Lager (gestapelt) - Kunde, Rest */}
            <Bar 
              dataKey="cuttingLagerKunde" 
              name="Cutting Lager Kunde" 
              fill="var(--color-cuttingLagerKunde)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="cuttingLager"
            />
            <Bar 
              dataKey="cuttingLagerRest" 
              name="Cutting Lager Rest" 
              fill="var(--color-cuttingLagerRest)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="cuttingLager"
            />
            
            {/* Balken 2: Positionen und Umlagerungen (gestapelt) */}
            <Bar 
              dataKey="waTaPositionen" 
              name="WaTa Positionen" 
              fill="var(--color-waTaPositionen)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="positionenUmlagerungen"
            />
            <Bar 
              dataKey="Umlagerungen" 
              name="Umlagerungen" 
              fill="var(--color-Umlagerungen)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="positionenUmlagerungen"
            />
            
            {/* Balken 3: Lager Cutting (einzeln) */}
            <Bar 
              dataKey="lagerCutting" 
              name="Lager Cutting" 
              fill="var(--color-lagerCutting)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="lagerCutting"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt - Einlagerungen: {avgCuttingLager.toFixed(0)} | Umlagerungen: {avgPositionenUmlagerungen.toFixed(0)} | Auslagerungen: {avgLagerCutting.toFixed(0)}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt - Einlagerungen: {totalCuttingLager.toLocaleString()} | Umlagerungen: {totalPositionenUmlagerungen.toLocaleString()} | Auslagerungen: {totalLagerCutting.toLocaleString()} | ARiL-Datenbank-basiert
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});