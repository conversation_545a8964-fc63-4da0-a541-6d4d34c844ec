import { useEffect, useState, memo } from "react";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import apiService from "@/services/api.service";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, MoreHorizontal, TrendingUp } from "lucide-react";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useDialog } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MaschinenHeatmapChart } from "./MaschinenHeatmapChart";

/**
 * Hilfsfunktion: Extrahiert die Maschinennummer aus dem Maschinennamen
 * Beispiel: "M5-R-H1" -> 5
 */
const extractMachineNumber = (machineName: string): number => {
  const match = machineName.match(/^M(\d+)/);
  return match ? parseInt(match[1], 10) : 0;
};

/**
 * Hilfsfunktion: Bestimmt den Maschinentyp basierend auf dem Buchstaben
 * Beispiel: "M5-R-H1" -> "Trommel-Ring"
 */
const getMachineType = (machineName: string): string => {
  const match = machineName.match(/^M\d+-([A-Z]+)-/);
  if (!match) return "Unbekannt";

  const typeCode = match[1];
  switch (typeCode) {
    case "R":
      return "Trommel-Ring";
    case "T":
      return "Trommel-Trommel";
    case "RR":
      return "Ring-Ring";
    default:
      return "Unbekannt";
  }
};

/**
 * Hilfsfunktion: Formatiert den Maschinennamen für die Anzeige
 * Beispiel: "M5-R-H1" -> "Maschine 5"
 */
const formatMachineName = (machineName: string): string => {
  const machineNumber = extractMachineNumber(machineName);
  return `Maschine ${machineNumber}`;
};

// Definition der kombinierten Datenzeile für die Tabelle
interface MaschinenDataRow {
  id: string;                    // Eindeutige ID (machine + datum)
  machine: string;               // Maschinenname (z.B. "M5-R-H1")
  maschinenNummer: number;       // Maschinennummer (z.B. 5 aus "M5-R-H1")
  maschinenBezeichnung: string;  // Formatierter Name (z.B. "Maschine 5")
  maschinenTypBezeichnung: string; // Maschinentyp (z.B. "Trommel-Ring")
  datum: string;                 // Datum als String
  formattedDate: string;         // Formatiertes Datum (DD.MM)
  schnitte: number;              // Anzahl Schnitte an diesem Tag
  sollSchnitte: number;          // Soll-Schnitte pro Stunde
  tagesSchnitte: number;         // Tatsächliche Tages-Schnitte
  istSchnitteProStunde: number;  // Ist-Schnitte pro Stunde
  effizienzProzent: number;      // Effizienz in Prozent
  maschinenTyp: 'H1' | 'H3';    // Maschinentyp zur Kategorisierung
}

// Originale Datentypen aus den anderen Charts
interface SchnitteDataPoint {
  id?: number;
  Datum: string;
  [key: string]: number | string | null | undefined;
}

interface EfficiencyDataPoint {
  Datum: string;
  Machine: string;
  sollSchnitte: number;
  tagesSchnitte: number;
  istSchnitteProStunde: number;
  effizienzProzent: number;
}

/**
 * MaschinenDataTable Komponente
 * 
 * Kombiniert Schnitte-Daten und Effizienz-Daten in einer übersichtlichen Tabelle.
 * Nutzt shadcn/ui Data Table mit TanStack Table für erweiterte Funktionalität.
 */

interface MaschinenDataTableProps {
  dateRange?: DateRange;
}

// Definition aller Maschinen in logischer Reihenfolge
const allMachines = [
  // H1-Maschinen
  "M5-R-H1", "M6-T-H1", "M7-R-H1", "M8-T-H1", "M9-R-H1",
  "M10-T-H1", "M11-R-H1", "M12-T-H1", "M13-R-H1", "M14-T-H1",
  "M15-R-H1", "M16-T-H1", "M17-R-H1", "M18-T-H1", "M19-T-H1",
  "M20-T-H1", "M21-R-H1", "M23-T-H1", "M25-RR-H1", "M26-T-H1",
  // H3-Maschinen
  "M1-T-H3", "M2-T-H3", "M3-R-H3", "M4-T-H3",
  "M22-T-H3", "M24-T-H3", "M27-R-H3"
];

// Spaltendefinitionen für die Data Table
const columns: ColumnDef<MaschinenDataRow>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Alle auswählen"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Zeile auswählen"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "maschinenBezeichnung",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 justify-start"
        >
          Maschine
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const maschinenBezeichnung = row.getValue("maschinenBezeichnung") as string;
      const maschinenTyp = row.getValue("maschinenTyp") as string;
      const isH3 = maschinenTyp === "H3";
      return (
        <div className={`font-medium ${isH3 ? 'text-red-600' : 'text-blue-600'}`}>
          {maschinenBezeichnung}
        </div>
      );
    },
  },
  {
    accessorKey: "maschinenTypBezeichnung",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 justify-center w-full"
        >
          Maschinentyp
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const maschinenTypBezeichnung = row.getValue("maschinenTypBezeichnung") as string;
      const maschinenTyp = row.getValue("maschinenTyp") as string;
      const isH3 = maschinenTyp === "H3";
      return (
        <div className={`text-center font-medium ${isH3 ? 'text-red-600' : 'text-blue-600'}`}>
          {maschinenTypBezeichnung}
        </div>
      );
    },
  },
  {
    accessorKey: "formattedDate",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 justify-center w-full"
        >
          Datum
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => <div className="text-center">{row.getValue("formattedDate")}</div>,
  },
  {
    accessorKey: "schnitte",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 justify-end w-full"
        >
          Schnitte
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const schnitte = row.getValue("schnitte") as number;
      return (
        <div className="text-right font-medium">
          {schnitte.toLocaleString()}
        </div>
      );
    },
  },
  {
    accessorKey: "effizienzProzent",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 justify-end w-full"
        >
          Effizienz (%)
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const effizienz = row.getValue("effizienzProzent") as number;
      const colorClass = effizienz >= 90 ? 'text-green-600' :
        effizienz >= 70 ? 'text-yellow-600' : 'text-red-600';

      return (
        <div className={`text-right font-medium ${colorClass}`}>
          {effizienz.toFixed(1)}%
        </div>
      );
    },
  },
  {
    accessorKey: "istSchnitteProStunde",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 justify-end w-full"
        >
          Schnitte/h
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const schnitteProStunde = row.getValue("istSchnitteProStunde") as number;
      return (
        <div className="text-right">
          {schnitteProStunde.toFixed(1)}
        </div>
      );
    },
  },
  {
    accessorKey: "maschinenTyp",
    header: "Typ",
    cell: ({ row }) => {
      const typ = row.getValue("maschinenTyp") as string;
      return (
        <div className={`text-center font-medium ${typ === 'H3' ? 'text-shadow-cyan-800' : 'text-blue-600'
          }`}>
          {typ}
        </div>
      );
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const maschinenData = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Menü öffnen</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="bg-white border border-gray-300 shadow-lg rounded-md min-w-[150px] z-50"
          >
            <DropdownMenuLabel className="font-semibold text-gray-900 px-3 py-2">
              Aktionen
            </DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(maschinenData.id)}
              className="hover:bg-gray-100 text-gray-700 px-3 py-2 cursor-pointer"
            >
              ID kopieren
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-200" />
            <DropdownMenuItem className="hover:bg-gray-100 text-gray-700 px-3 py-2 cursor-pointer">
              Details anzeigen
            </DropdownMenuItem>
            <DropdownMenuItem className="hover:bg-gray-100 text-gray-700 px-3 py-2 cursor-pointer">
              Daten exportieren
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export const MaschinenDataTable = memo(function MaschinenDataTable({ dateRange }: MaschinenDataTableProps) {
  const [data, setData] = useState<MaschinenDataRow[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const { openDialog } = useDialog();

  useEffect(() => {
    loadCombinedData();
  }, [dateRange]);

  const loadCombinedData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Daten über apiService laden

      // Parallel beide Datenquellen laden
      const [schnitteResult, effizienzResult] = await Promise.all([
        apiService.getSchnitteData(),
        apiService.getMaschinenEfficiency()
      ]);

      if (!Array.isArray(schnitteResult) || !Array.isArray(effizienzResult)) {
        throw new Error('Ungültige Datenstruktur erhalten');
      }

      // Daten verarbeiten und kombinieren
      const combinedData = combineData(
        schnitteResult as SchnitteDataPoint[],
        effizienzResult as EfficiencyDataPoint[]
      );

      // Nach Datumsbereich filtern falls angegeben
      let filteredData = combinedData;
      if (dateRange && dateRange.from && dateRange.to) {
        filteredData = combinedData.filter(item => {
          try {
            const itemDate = new Date(item.datum);
            return isWithinInterval(itemDate, {
              start: dateRange.from as Date,
              end: dateRange.to as Date
            });
          } catch {
            return true;
          }
        });
      }

      setData(filteredData);

    } catch (err) {
      setData([]);
      setError('Fehler beim Laden der Daten: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Funktion zum Kombinieren der Schnitte- und Effizienz-Daten
  const combineData = (
    schnitteData: SchnitteDataPoint[],
    effizienzData: EfficiencyDataPoint[]
  ): MaschinenDataRow[] => {
    const combinedRows: MaschinenDataRow[] = [];

    // Für jeden Datenpunkt in den Schnitte-Daten
    schnitteData.forEach((schnitteRow) => {
      if (!schnitteRow.Datum) return;

      const dateObj = new Date(schnitteRow.Datum);
      const formattedDate = dateObj.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });

      // Für jede Maschine einen Datensatz erstellen
      allMachines.forEach((machine) => {
        // Maschinennamen in der Datenbank haben Bindestriche, aber in der API Unterstriche
        const machineKey = machine.replace(/-/g, "_");
        const schnitte = (schnitteRow as Record<string, number | string | null | undefined>)[machineKey] as number || 0;

        // Entsprechende Effizienz-Daten finden
        const effizienzRow = effizienzData.find(e =>
          e.Datum === schnitteRow.Datum && e.Machine === machine
        );

        // Daten aus der Effizienz-Zeile extrahieren
        const tagesSchnitte = effizienzRow?.tagesSchnitte || 0;
        const sollSchnitteProStunde = effizienzRow?.sollSchnitte || 0; // Dies ist ein Stundenwert

        // Tägliche Soll-Schnitte berechnen (Stundenwert * 24)
        const sollSchnitteProTag = sollSchnitteProStunde * 21.5;

        // Verwende die vom Backend berechnete Effizienz direkt
        // Keine eigene Berechnung im Frontend notwendig
        const berechneteEffizienz = effizienzRow?.effizienzProzent || 0;

        const maschinenTyp: 'H1' | 'H3' = machine.includes('H3') ? 'H3' : 'H1';

        combinedRows.push({
          id: `${machine}-${schnitteRow.Datum}`,
          machine,
          maschinenNummer: extractMachineNumber(machine),
          maschinenBezeichnung: formatMachineName(machine),
          maschinenTypBezeichnung: getMachineType(machine),
          datum: schnitteRow.Datum,
          formattedDate,
          schnitte,
          sollSchnitte: sollSchnitteProStunde, // Beibehaltung des Stundenwerts für die Datenzeile
          tagesSchnitte,
          istSchnitteProStunde: effizienzRow?.istSchnitteProStunde || 0,
          effizienzProzent: berechneteEffizienz, // Verwendung der neu berechneten Effizienz
          maschinenTyp
        });
      });
    });

    // Sortiere nach Datum (neueste zuerst), dann nach Maschine
    return combinedRows.sort((a, b) => {
      const dateComparison = new Date(b.datum).getTime() - new Date(a.datum).getTime();
      if (dateComparison !== 0) return dateComparison;
      return a.machine.localeCompare(b.machine);
    });
  };

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      pagination: {
        pageSize: 50, // Mehr Zeilen pro Seite für Maschinen-Daten
      },
    },
  });

  if (loading) {
    return (
      <Card className="text-black border rounded-md shadow-md p-4 border-[#ff7a05]">
        <CardContent>
          <div className="flex flex-col h-80 w-full items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
            <p className="mt-4 font-bold">Lädt Maschinendaten...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="text-black border rounded-md shadow-md p-4 border-[#ff7a05]">
        <CardContent>
          <div className="flex flex-col h-80 w-full items-center justify-center">
            <p className="text-red-500 font-bold">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <div className="flex flex-row items-center justify-between">
          <div className="space-y-2">
            <CardTitle className="text-xl">
              Datentabelle: {data.length} Datensätze für {allMachines.length} Maschinen
            </CardTitle>
          </div>
          <Button
            variant="sfm"
            size="sm"
            onClick={() => openDialog(
              <div className="space-y-4">
                <div className="text-center space-y-2">
                  <h2 className="text-xl font-bold text-black">HEATMAP</h2>
                  <p className="text-gray-600">Maschineneffizienz dargestellt als Heatmap</p>
                </div>
                <MaschinenHeatmapChart dateRange={dateRange} />
              </div>
            )}
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            <span className="font-bold">Heatmap ansehen</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="w-full space-y-4">
          {/* Filter und Spalten-Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Nach Maschine filtern..."
                value={(table.getColumn("maschinenBezeichnung")?.getFilterValue() as string) ?? ""}
                onChange={(event) =>
                  table.getColumn("maschinenBezeichnung")?.setFilterValue(event.target.value)
                }
                className="max-w-sm"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="sfm" className="ml-auto">
                  Spalten <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-white border border-gray-300 shadow-lg">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    // Deutsche Bezeichnungen für die Spalten
                    const getColumnLabel = (columnId: string): string => {
                      switch (columnId) {
                        case 'maschinenBezeichnung': return 'Maschine';
                        case 'maschinenTypBezeichnung': return 'Maschinentyp';
                        case 'formattedDate': return 'Datum';
                        case 'schnitte': return 'Schnitte';
                        case 'effizienzProzent': return 'Effizienz (%)';
                        case 'istSchnitteProStunde': return 'Schnitte/h';
                        case 'maschinenTyp': return 'Halle';
                        default: return columnId;
                      }
                    };

                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="text-black bg-white hover:bg-gray-100"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {getColumnLabel(column.id)}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Data Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      Keine Ergebnisse gefunden.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="flex-1 text-sm text-muted-foreground">
              {table.getFilteredSelectedRowModel().rows.length} von{" "}
              {table.getFilteredRowModel().rows.length} Zeile(n) ausgewählt.
            </div>
            <div className="flex items-center space-x-6 lg:space-x-8">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Zeilen pro Seite</p>
                <select
                  value={table.getState().pagination.pageSize}
                  onChange={(e) => {
                    table.setPageSize(Number(e.target.value))
                  }}
                  className="h-8 w-[70px] rounded border border-input bg-background text-sm"
                >
                  {[25, 50, 100].map((pageSize) => (
                    <option key={pageSize} value={pageSize}>
                      {pageSize}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                Seite {table.getState().pagination.pageIndex + 1} von{" "}
                {table.getPageCount()}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="sfm"
                  size="sm"
                  onClick={() => table.setPageIndex(0)}
                  disabled={!table.getCanPreviousPage()}
                >
                  Erste
                </Button>
                <Button
                  variant="sfm"
                  size="sm"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  Vorherige
                </Button>
                <Button
                  variant="sfm"
                  size="sm"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Nächste
                </Button>
                <Button
                  variant="sfm"
                  size="sm"
                  onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                  disabled={!table.getCanNextPage()}
                >
                  Letzte
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Datentabelle: {data.length} Datensätze für {allMachines.length} Maschinen
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Kombiniert Schnitte-Daten und Effizienz-Kennzahlen | Sortierbar und filterbar
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});