import { useState, useEffect } from 'react';
import { AlertTriangle, Info, XCircle, Filter, Search, Download, Play, Trash2, Terminal, Clock, Pause, Database } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useWorkflows } from '@/hooks/useWorkflows';
import { WorkflowLog } from '@/types/workflow';
import { workflowsClient } from '@/helpers/ipc/workflows.ipc';

// Available processes based on backend WorkflowService
const AVAILABLE_PROCESSES = [
  { id: 'servicegrad', name: 'Servicegrad', description: 'SAP Servicegrad Automatisierung' },
  { id: 'bestand', name: 'Bestand Workflow', description: 'Lagerspiegel Export' },
  { id: 'rueckstandsliste', name: 'Rückstandsliste', description: 'SAP Rückstandsliste Datenexport' },
  { id: 'lx03_240', name: 'LX03 Lagertyp 240', description: 'SAP LX03 Lagertyp 240' },
  { id: 'lx03_200', name: 'LX03 Lagertyp 200', description: 'SAP LX03 Lagertyp 200' },
  { id: 'lx03_rest', name: 'LX03 Lagertyp Rest', description: 'SAP LX03 Lagertyp Rest' },
];

interface LiveLogStream {
  processId: string;
  lines: string[];
  subscribed: boolean;
  unsubscribe: (() => void) | null;
}

interface ProcessCardState {
  processId: string;
  viewMode: 'live' | 'system';
}

export function WorkflowLogViewer() {
  const { logs, loading, error } = useWorkflows();
  const [filteredLogs, setFilteredLogs] = useState<WorkflowLog[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [processFilter, setProcessFilter] = useState<string>('all');

  // View mode für jede Process Card
  const [processCardStates, setProcessCardStates] = useState<Record<string, ProcessCardState>>(() => {
    const states: Record<string, ProcessCardState> = {};
    AVAILABLE_PROCESSES.forEach(process => {
      states[process.id] = {
        processId: process.id,
        viewMode: 'live',
      };
    });
    return states;
  });

  // Live-Streams für alle Prozesse
  const [liveStreams, setLiveStreams] = useState<Record<string, LiveLogStream>>(() => {
    const streams: Record<string, LiveLogStream> = {};
    AVAILABLE_PROCESSES.forEach(process => {
      streams[process.id] = {
        processId: process.id,
        lines: [],
        subscribed: false,
        unsubscribe: null,
      };
    });
    return streams;
  });

  useEffect(() => {
    filterLogs();
  }, [logs, searchTerm, levelFilter, processFilter]);

  const filterLogs = () => {
    let filtered = [...logs].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.processId.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (levelFilter !== 'all') {
      filtered = filtered.filter(log => log.level === levelFilter);
    }

    if (processFilter !== 'all') {
      filtered = filtered.filter(log => log.processId === processFilter);
    }

    setFilteredLogs(filtered);
  };

  const getLogIcon = (level: WorkflowLog['level']) => {
    switch (level) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  const getLogBadge = (level: WorkflowLog['level']) => {
    switch (level) {
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      default:
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Info</Badge>;
    }
  };

  const exportLogs = () => {
    const csvContent = [
      ['Timestamp', 'Level', 'Process', 'Message'].join(','),
      ...filteredLogs.map(log => [
        log.timestamp.toISOString(),
        log.level,
        log.processId,
        `"${log.message.replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-logs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const uniqueProcesses = Array.from(new Set(logs.map(log => log.processId))).filter(Boolean);

  // Live-Stream Steuerung für alle Prozesse
  const startStream = (processId: string) => {
    if (liveStreams[processId]?.subscribed) return;

    try {
      const unsubscribe = workflowsClient.subscribeLogs(processId as any, (line) => {
        setLiveStreams(prev => ({
          ...prev,
          [processId]: {
            ...prev[processId],
            lines: [...prev[processId].lines, line],
          }
        }));
      });

      setLiveStreams(prev => ({
        ...prev,
        [processId]: {
          ...prev[processId],
          subscribed: true,
          unsubscribe,
          lines: [], // Clear previous lines when starting new stream
        }
      }));
    } catch (error) {
      console.error(`Fehler beim Starten des Streams für ${processId}:`, error);
    }
  };

  const stopStream = (processId: string) => {
    const stream = liveStreams[processId];
    if (stream?.unsubscribe) {
      stream.unsubscribe();
    }

    setLiveStreams(prev => ({
      ...prev,
      [processId]: {
        ...prev[processId],
        subscribed: false,
        unsubscribe: null,
      }
    }));
  };

  const clearStream = (processId: string) => {
    setLiveStreams(prev => ({
      ...prev,
      [processId]: {
        ...prev[processId],
        lines: [],
      }
    }));
  };

  const startAllStreams = () => {
    AVAILABLE_PROCESSES.forEach(process => {
      if (!liveStreams[process.id]?.subscribed) {
        startStream(process.id);
      }
    });
  };

  const stopAllStreams = () => {
    AVAILABLE_PROCESSES.forEach(process => {
      if (liveStreams[process.id]?.subscribed) {
        stopStream(process.id);
      }
    });
  };

  const clearAllStreams = () => {
    AVAILABLE_PROCESSES.forEach(process => {
      clearStream(process.id);
    });
  };

  // View mode ändern für eine Process Card
  const setProcessViewMode = (processId: string, viewMode: 'live' | 'system') => {
    setProcessCardStates(prev => ({
      ...prev,
      [processId]: {
        ...prev[processId],
        viewMode,
      }
    }));
  };

  // System logs für einen bestimmten Prozess filtern
  const getSystemLogsForProcess = (processId: string) => {
    return filteredLogs.filter(log => log.processId === processId);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      AVAILABLE_PROCESSES.forEach(process => {
        const stream = liveStreams[process.id];
        if (stream?.unsubscribe) {
          stream.unsubscribe();
        }
      });
    };
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Global Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Global Log Filter
          </CardTitle>
          <CardDescription>
            Globale Filter für alle Log-Ansichten
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Logs durchsuchen..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={levelFilter} onValueChange={setLevelFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Level</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button onClick={stopAllStreams} variant="outline">
                <Pause className="w-4 h-4 mr-2" /> Alle stoppen
              </Button>
              <Button onClick={exportLogs} variant="outline">
                <Download className="h-4 w-4 mr-2" /> Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Process Cards mit individueller Menüauswahl - Kompakt */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {AVAILABLE_PROCESSES.map((process) => {
          const stream = liveStreams[process.id];
          const isSubscribed = stream?.subscribed || false;
          const lineCount = stream?.lines.length || 0;
          const cardState = processCardStates[process.id];
          const systemLogs = getSystemLogsForProcess(process.id);

          return (
            <Card key={process.id} className="border">
              <CardHeader className="pb-2 pt-3 px-3">
                <div className="flex items-center justify-between">
                  <div className="min-w-0 flex-1">
                    <CardTitle className="text-sm flex items-center gap-1 truncate">
                      <Terminal className="h-3 w-3 flex-shrink-0" />
                      {process.name}
                    </CardTitle>
                    <CardDescription className="text-xs truncate">
                      {process.description}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    {cardState.viewMode === 'live' && isSubscribed && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs px-1 py-0">
                        <Clock className="h-2 w-2 mr-1 animate-pulse" />
                        Live
                      </Badge>
                    )}
                    <Badge variant="outline" className="text-xs px-1 py-0">
                      {cardState.viewMode === 'live' ? `${lineCount}` : `${systemLogs.length}`}
                    </Badge>
                  </div>
                </div>

                {/* Kompakte Menüauswahl */}
                <div className="flex gap-1 mt-2">
                  <Button
                    size="sm"
                    variant={cardState.viewMode === 'live' ? 'default' : 'outline'}
                    onClick={() => setProcessViewMode(process.id, 'live')}
                    className="flex items-center gap-1 h-6 px-2 text-xs"
                  >
                    <Terminal className="h-2 w-2" />
                    Live
                  </Button>
                  <Button
                    size="sm"
                    variant={cardState.viewMode === 'system' ? 'default' : 'outline'}
                    onClick={() => setProcessViewMode(process.id, 'system')}
                    className="flex items-center gap-1 h-6 px-2 text-xs"
                  >
                    <Database className="h-2 w-2" />
                    System
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="px-3 pb-3">
                {cardState.viewMode === 'live' ? (
                  // Live Logs View - Kompakt
                  <>
                    <div className="flex gap-1 mb-2">
                      {!isSubscribed ? (
                        <Button
                          onClick={() => startStream(process.id)}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white h-6 px-2 text-xs"
                        >
                          <Play className="w-2 h-2 mr-1" /> Start
                        </Button>
                      ) : (
                        <Button
                          onClick={() => stopStream(process.id)}
                          size="sm"
                          variant="outline"
                          className="h-6 px-2 text-xs"
                        >
                          <Pause className="w-2 h-2 mr-1" /> Stop
                        </Button>
                      )}
                      <Button
                        onClick={() => clearStream(process.id)}
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs"
                      >
                        <Trash2 className="w-2 h-2 mr-1" /> Clear
                      </Button>
                    </div>

                    <ScrollArea className="h-[120px] border rounded">
                      {lineCount > 0 ? (
                        <pre className="whitespace-pre-wrap text-xs bg-gray-900 text-green-400 p-2 rounded font-mono">
                          {stream.lines.slice(-30).join('')}
                        </pre>
                      ) : (
                        <div className="h-full flex items-center justify-center bg-gray-50 border-2 border-dashed border-gray-300 rounded">
                          <div className="text-center text-gray-500">
                            <Terminal className="h-3 w-3 mx-auto mb-1 opacity-50" />
                            <p className="text-xs">Keine Live Logs</p>
                          </div>
                        </div>
                      )}
                    </ScrollArea>
                  </>
                ) : (
                  // System Logs View - Kompakt
                  <ScrollArea className="h-[140px] border rounded">
                    <div className="p-1 space-y-1">
                      {systemLogs.length === 0 ? (
                        <div className="text-center py-3 text-gray-500">
                          <Database className="h-3 w-3 mx-auto mb-1 opacity-50" />
                          <p className="text-xs">Keine System Logs</p>
                        </div>
                      ) : (
                        systemLogs.slice(0, 5).map((log) => (
                          <div
                            key={log.id}
                            className="flex items-start gap-1 p-1 border rounded hover:bg-gray-50"
                          >
                            <div className="flex-shrink-0 mt-0.5">
                              {getLogIcon(log.level)}
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                {getLogBadge(log.level)}
                                <span className="text-xs text-gray-500 truncate">
                                  {log.timestamp.toLocaleTimeString('de-DE')}
                                </span>
                              </div>

                              <p className="text-xs text-gray-900 break-words line-clamp-1">
                                {log.message}
                              </p>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}