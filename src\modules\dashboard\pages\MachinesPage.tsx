import { useState } from "react";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { MaschinenEfficiencyChart } from "@/modules/dashboard/components/charts/MaschinenEfficiencyChart";
import { MaschinenDataTable } from "@/modules/dashboard/components/charts/MaschinenDataTable";
import { Scissors } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";

/**
 * Maschinen-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Maschinen-Bereich an:
 * - Maschinen-Effizienz als Balkendiagramm
 */
export default function MachinesPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 3, 15), // 15. April 2025
    to: new Date(2025, 4, 7), // 7. Mai 2025 (alle verfügbaren Daten)
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center">
          <Scissors className="h-8 w-8 mr-2" />
          <h1 className="text-3xl font-bold text-black">MACHINEN-EFFIZIENZ</h1>
        </div>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>

      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-6">
          <div className="col-span-2">
            <ChartErrorBoundary>
              <MaschinenEfficiencyChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Maschinendatentabelle */}
          <div className="col-span-2">
            <ChartErrorBoundary>
              <MaschinenDataTable dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}
