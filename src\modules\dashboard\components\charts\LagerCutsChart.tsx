import React, { useEffect, useState, memo } from "react";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import { useTranslation } from "react-i18next";
import apiService from "@/services/api.service";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";;
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { CircleSlash2 } from "lucide-react";

// Definition der Datentypen - Service liefert bereits aggregierte Daten
interface LagerCutsDataPoint {
  name: string;
  date?: Date | string;
  lagerSumme: number;
  cutLagerKSumme: number;
  cutLagerRSumme: number;
}

// LagerDatabaseRow ist nicht mehr nötig, da getLagerCutsChartData bereits formatierte Daten liefert

interface LagerCutsChartProps {
  dateRange?: DateRange;
}

export const LagerCutsChart = memo(function LagerCutsChart({ dateRange }: LagerCutsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<LagerCutsDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    lagerSumme: {
      label: "Lager --> Ablaengerei",
      color: "var(--chart-1)",
    },
    cutLagerKSumme: {
      label: "Kunden Schnitte --> Lager",
      color: "var(--chart-2)",
    },
    cutLagerRSumme: {
      label: "Rest Schnitte --> Lager",
      color: "var(--chart-3)",
    }
  };

  // Service liefert bereits berechnete Summen - keine weitere Berechnung nötig
  const chartDataWithSums = chartData;

  // Berechne Gesamtwerte und Verhältnisse für den Footer
  const totalLagerAbgang = chartDataWithSums.reduce((sum, item) => sum + item.lagerSumme, 0);
  const totalKundenZugang = chartDataWithSums.reduce((sum, item) => sum + item.cutLagerKSumme, 0);
  const totalRestZugang = chartDataWithSums.reduce((sum, item) => sum + item.cutLagerRSumme, 0);
  const totalLagerZugang = totalKundenZugang + totalRestZugang;
  
  // Verhältnis berechnen: Lagerzugang / Lagerabgang
  const verhaeltnis = totalLagerAbgang > 0 ? (totalLagerZugang / totalLagerAbgang) : 0;
  const verhaeltnisText = totalLagerAbgang > 0 ? `${verhaeltnis.toFixed(2)}:1` : 'N/A';
  
  // Durchschnittswerte
  const avgLagerAbgang = chartDataWithSums.length > 0 ? totalLagerAbgang / chartDataWithSums.length : 0;
  const avgLagerZugang = chartDataWithSums.length > 0 ? totalLagerZugang / chartDataWithSums.length : 0;

  // Lade Daten aus der Datenbank
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Correctly use the imported apiService instance
        const result = await apiService.getLagerCutsChartData();
        
        // The service now guarantees an array, so we can simplify the checks
        if (!result) {
          setChartData([]);
          return;
        }

        let processedData = result as LagerCutsDataPoint[];
        
        // Filter by date range if available
        if (dateRange && dateRange.from && dateRange.to) {
          processedData = processedData.filter(item => {
            if (!item.date) return true;
            
            try {
              return isWithinInterval(new Date(item.date), { // Ensure item.date is a Date object
                start: dateRange.from as Date,
                end: dateRange.to as Date
              });
            } catch (error) {
              return true;
            }
          });
        }
        setChartData(processedData || []); // Ensure we always set an array
      } catch (err) {
        setError("Fehler beim Laden der Daten" + (err instanceof Error ? ": " + err.message : ""));
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateRange]);

  if (loading) return <div className="p-4 text-center">Lade Daten...</div>;
  if (error) return <div className="p-4 text-red-500">Fehler: {error}</div>;
  
  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>BEWEGUNGEN</CardTitle>
        <CardDescription>
          Bewegungen zwischen den Lägern und der Ablaengerei
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="h-60 w-full"
        >
          <BarChart data={chartDataWithSums} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="name" 
              className="text-xs font-bold"
              tickLine={false}
              axisLine={false}
              tickMargin={2}
              height={30}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {
                try {
                  // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                  const date = new Date(value);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleDateString('de-DE', { 
                      day: '2-digit', 
                      month: '2-digit' 
                    });
                  }
                  // Falls es kein gültiges Datum ist, zeige den String direkt
                  return String(value);
                } catch {
                  return value;
                }
              }}
            />
            <YAxis 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              // Beschriftung für die Y-Achse
              label={{ 
                value: "Anzahl Lager", 
                angle: -90, 
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => `Datum: ${label}`}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="lagerSumme"
              name="Lager --> Ablaengerei"
              fill={chartConfig.lagerSumme.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="cutLagerKSumme"
              name="Kunden Schnitte --> Lager"
              fill={chartConfig.cutLagerKSumme.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[0, 0, 0, 0]}
              className="neo-brutalism-bar"
              stackId="lager"
            />
            <Bar
              dataKey="cutLagerRSumme"
              name="Rest Schnitte --> Lager"
              fill={chartConfig.cutLagerRSumme.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="lager"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
            <span className="text-black text-md flex items-center">Schnitte/Trommel-Verhältnis: {verhaeltnisText} | </span>
            <span className="text-black text-md flex items-center"> <CircleSlash2 className="mr-2 h-4 w-4" /> Ablängerei ins Lager: {avgLagerZugang.toFixed(0)} | </span>
            <span className="text-black text-md flex items-center"> <CircleSlash2 className="mr-2 h-4 w-4" /> Lager in Ablängerei: {avgLagerAbgang.toFixed(0)} </span>
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
            <span className="text-black text-md">Gesamt Ablängerei ins Lager: {totalLagerZugang.toLocaleString()} | </span>
            <span className="text-black text-md"> Gesamt Lager in Ablängerei: {totalLagerAbgang.toLocaleString()} | </span>
              {chartData.length > 0 
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank` 
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
