import { Request, Response } from 'express';
import { StoerungenRepository } from '../repositories/stoerungen.repository';
import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { z } from 'zod';
import { getBackendCache } from '../services/cache.service';

const prisma = new PrismaClient();
const stoerungenRepo = StoerungenRepository.getInstance(prisma);

const createStoerungSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  status: z.enum(['NEW', 'IN_PROGRESS', 'RESOLVED']).default('NEW'),
  category: z.string().optional(),
  affected_system: z.string().optional(),
  location: z.string().optional(),
  reported_by: z.string().optional(),
  assigned_to: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

const updateStoerungSchema = createStoerungSchema.partial();

const addCommentSchema = z.object({
  stoerung_id: z.number(),
  user_id: z.string().optional(),
  comment: z.string().min(1, 'Comment is required'),
});

const updateSystemStatusSchema = z.object({
  system_name: z.string().min(1, 'System name is required'),
  status: z.enum(['OK', 'WARNING', 'ERROR']),
  metadata: z.any().optional(),
});

export class StoerungenController {
  async getStoerungen(req: Request, res: Response) {
    try {
      const { status, severity, category, affected_system, limit, offset } = req.query;

      const options = {
        status: status as string,
        severity: severity as string,
        category: category as string,
        affected_system: affected_system as string,
        limit: limit ? parseInt(limit as string) : undefined,
        offset: offset ? parseInt(offset as string) : undefined,
      };

      // Remove undefined values
      Object.keys(options).forEach(key => {
        if (options[key as keyof typeof options] === undefined) {
          delete options[key as keyof typeof options];
        }
      });

      const stoerungen = await stoerungenRepo.getStoerungen(options);
      res.json({
        success: true,
        data: stoerungen
      });
    } catch (error) {
      console.error('Error fetching störungen:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch störungen' });
    }
  }

  async getStoerungById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      const stoerung = await stoerungenRepo.getStoerungById(id);
      if (!stoerung) {
        return res.status(404).json({ error: 'Störung not found' });
      }

      res.json({
        success: true,
        data: stoerung
      });
    } catch (error) {
      console.error('Error fetching störung:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch störung' });
    }
  }

  async createStoerung(req: Request, res: Response) {
    try {
      const validatedData = createStoerungSchema.parse(req.body);
      const stoerung = await stoerungenRepo.createStoerung(validatedData);
      
      res.status(201).json({
        success: true,
        data: stoerung
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error creating störung:', error);
      res.status(500).json({ success: false, error: 'Failed to create störung' });
    }
  }

  async updateStoerung(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      const validatedData = updateStoerungSchema.parse(req.body);
      
      // Calculate MTTR if resolving
      let updateData: any = { ...validatedData };
      if (validatedData.status === 'RESOLVED') {
        const existing = await stoerungenRepo.getStoerungById(id);
        if (existing && !existing.resolved_at) {
          const mttr = Math.round((Date.now() - new Date(existing.created_at).getTime()) / (1000 * 60));
          updateData.mttr_minutes = mttr;
        }
      }

      const stoerung = await stoerungenRepo.updateStoerung(id, updateData);
      res.json({
        success: true,
        data: stoerung
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error updating störung:', error);
      res.status(500).json({ success: false, error: 'Failed to update störung' });
    }
  }

  async deleteStoerung(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      const success = await stoerungenRepo.deleteStoerung(id);
      if (!success) {
        return res.status(404).json({ error: 'Störung not found' });
      }

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting störung:', error);
      res.status(500).json({ success: false, error: 'Failed to delete störung' });
    }
  }

  async addComment(req: Request, res: Response) {
    try {
      const validatedData = addCommentSchema.parse(req.body);
      const comment = await stoerungenRepo.addComment(validatedData);
      
      res.status(201).json({
        success: true,
        data: comment
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error adding comment:', error);
      res.status(500).json({ success: false, error: 'Failed to add comment' });
    }
  }

  async getStoerungsStats(req: Request, res: Response) {
    try {
      const stats = await stoerungenRepo.getStoerungsStats();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error fetching störungen stats:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch störungen stats' });
    }
  }

  async getActiveStoerungen(req: Request, res: Response) {
    try {
      const active = await stoerungenRepo.getStoerungen({
        status: 'NEW,IN_PROGRESS',
      });
      res.json({
        success: true,
        data: active
      });
    } catch (error) {
      console.error('Error fetching active störungen:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch active störungen' });
    }
  }

  async getSystemStatus(req: Request, res: Response) {
    try {
      const statuses = await stoerungenRepo.getSystemStatus();
      res.json({
        success: true,
        data: statuses
      });
    } catch (error) {
      console.error('Error fetching system status:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch system status' });
    }
  }

  async updateSystemStatus(req: Request, res: Response) {
    try {
      const validatedData = updateSystemStatusSchema.parse(req.body);
      const status = await stoerungenRepo.updateSystemStatus(validatedData);
      
      res.json({
        success: true,
        data: status
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error updating system status:', error);
      res.status(500).json({ success: false, error: 'Failed to update system status' });
    }
  }
}