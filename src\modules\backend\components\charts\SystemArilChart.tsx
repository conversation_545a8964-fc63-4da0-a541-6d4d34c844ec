import React, { useEffect, useState, memo } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import apiService from "@/services/api.service";

interface SystemArilChartProps {
  dateRange?: DateRange;
}

export const SystemArilChart = memo(function SystemArilChart({ dateRange }: SystemArilChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const chartConfig = {
    ninioSapAril: { label: "SAP", color: "#8884d8" },
    nioWitronAril: { label: "Witron", color: "#82ca9d" },
    nioSiemensAril: { label: "Siemens", color: "#ffc658" },
    nioProzessAril: { label: "Prozess", color: "#ff8042" },
    nioSonstigesAril: { label: "Sonstiges", color: "#0088FE" },
  };

  useEffect(() => {
    loadData();
  }, [dateRange]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("SystemArilChart: Starte Datenladung...");

      // Konvertiere DateRange zu String-Format für API
      const startDate = dateRange?.from ? 
        `${dateRange.from.getFullYear()}-${String(dateRange.from.getMonth() + 1).padStart(2, '0')}-${String(dateRange.from.getDate()).padStart(2, '0')}` 
        : undefined;
      const endDate = dateRange?.to ? 
        `${dateRange.to.getFullYear()}-${String(dateRange.to.getMonth() + 1).padStart(2, '0')}-${String(dateRange.to.getDate()).padStart(2, '0')}` 
        : undefined;

      console.log("SystemArilChart: Rufe getSystemStats über API Service auf...");
      const result = await apiService.getSystemStats(startDate, endDate);
      console.log("SystemArilChart: Erhaltene Daten:", result);

      if (result && Array.isArray(result)) {
        console.log(`SystemArilChart: ${result.length} Datensätze erhalten`);

        // Filtere Daten basierend auf dem ausgewählten Datumsbereich
        let filteredData = result;
        if (dateRange?.from || dateRange?.to) {
          console.log("SystemArilChart: Filtere Daten nach Datumsbereich:", dateRange);
          console.log("SystemArilChart: Originaldaten vor Filterung:", result.map((item: unknown) => (item as SystemArilDataPoint).Datum));
          
          filteredData = result.filter((item: unknown) => {
            const dbItem = item as SystemArilDataPoint;
            try {
              let itemDate: Date;
              
              // Versuche verschiedene Parsing-Methoden
              const originalDatum = dbItem.Datum;
              console.log(`SystemArilChart: Verarbeite Original-Datum: "${originalDatum}"`);
              
                             // Methode 1: Bereinige nur "+" Zeichen
               const cleanDateStr = originalDatum.replace(/\+/g, '');
               const dateParts = cleanDateStr.split('.');
              
              if (dateParts.length >= 3 && !isNaN(parseInt(dateParts[0])) && !isNaN(parseInt(dateParts[1])) && !isNaN(parseInt(dateParts[2]))) {
                // Format: DD.MM.YYYY
                const day = parseInt(dateParts[0]);
                const month = parseInt(dateParts[1]);
                const year = parseInt(dateParts[2]);
                
                // Validiere die Werte
                if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 2000 && year <= 2030) {
                  itemDate = new Date(year, month - 1, day);
                  console.log(`SystemArilChart: Erfolgreich geparst ${originalDatum} -> ${cleanDateStr} -> ${itemDate.toISOString().split('T')[0]}`);
                } else {
                  throw new Error(`Ungültige Datumswerte: Tag=${day}, Monat=${month}, Jahr=${year}`);
                }
              } else {
                // Methode 2: Versuche direktes Parsing der bereinigten Version
                console.log(`SystemArilChart: Versuche direktes Parsing von "${cleanDateStr}"`);
                itemDate = new Date(cleanDateStr);
                if (isNaN(itemDate.getTime())) {
                  throw new Error(`Direktes Parsing fehlgeschlagen für "${cleanDateStr}"`);
                }
              }

              const itemDateStr = itemDate.toISOString().split('T')[0];
              const fromDateStr = dateRange?.from?.toISOString().split('T')[0] || 'nicht gesetzt';
              const toDateStr = dateRange?.to?.toISOString().split('T')[0] || 'nicht gesetzt';
              
              console.log(`SystemArilChart: Vergleiche ${itemDateStr} mit Bereich ${fromDateStr} bis ${toDateStr}`);

              if (dateRange?.from && itemDate < dateRange.from) {
                console.log(`SystemArilChart: ${originalDatum} ist vor dem Start-Datum, wird herausgefiltert`);
                return false;
              }
              if (dateRange?.to && itemDate > dateRange.to) {
                console.log(`SystemArilChart: ${originalDatum} ist nach dem End-Datum, wird herausgefiltert`);
                return false;
              }

              console.log(`SystemArilChart: ${originalDatum} ist im Bereich, wird behalten`);
              return true;
            } catch (e) {
              console.error('SystemArilChart: Fehler bei der Datumsfilterung:', e, 'für Datum:', dbItem.Datum);
              console.log('SystemArilChart: Behalte Datenpunkt trotz Parse-Fehler bei');
              return true; // Bei Fehlern beim Datumsvergleich den Datenpunkt behalten (sicherere Option)
            }
          });
          console.log(`SystemArilChart: Nach Filterung ${filteredData.length} Datensätze übrig`);
          console.log("SystemArilChart: Gefilterte Daten:", filteredData.map((item: unknown) => (item as SystemArilDataPoint).Datum));
        }

        // Sortiere die Daten chronologisch nach Datum
        const sortedData = (filteredData as SystemArilDataPoint[]).sort((a, b) => {
          try {
            // Konvertiere DD.MM.YYYY Format zu Date-Objekten für Sortierung
            const parseGermanDate = (dateStr: string): Date => {
              const parts = dateStr.split('.');
              if (parts.length >= 3) {
                // DD.MM.YYYY -> new Date(YYYY, MM-1, DD)
                return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
              }
              return new Date(dateStr);
            };
            
            const dateA = parseGermanDate(a.Datum);
            const dateB = parseGermanDate(b.Datum);
            
            return dateA.getTime() - dateB.getTime();
          } catch (e) {
            console.error('SystemArilChart: Fehler beim Sortieren nach Datum:', e);
            return 0;
          }
        });
        
        setChartData(sortedData);
      } else {
        console.error("SystemArilChart: Keine gültigen Daten erhalten:", result);
        throw new Error("Keine gültigen Daten erhalten");
      }
    } catch (error) {
      console.error("SystemArilChart: Fehler beim Laden der Daten:", error);
      setError("Fehler beim Laden der Daten");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>{t("loading")}...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  // Berechne Durchschnittswerte für Footer
  const avgSap = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.ninioSapAril, 0) / chartData.length : 0;
  const avgWitron = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.nioWitronAril, 0) / chartData.length : 0;
  const avgSiemens = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.nioSiemensAril, 0) / chartData.length : 0;
  const totalAll = chartData.reduce((sum, item) => sum + item.ninioSapAril + item.nioWitronAril + item.nioSiemensAril + item.nioProzessAril + item.nioSonstigesAril, 0);

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>ARIL NIO FAHRTEN</CardTitle>
        <CardDescription>
          System versursachte NIO Fahrten
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-60 w-full">
          <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="Datum"
              className="text-xs font-bold"
              tickLine={false}
              axisLine={false}
              tickMargin={1}
              height={30}
              tickFormatter={(value) => {
                try {
                  if (!value) return '-';
                  
                  // Konvertiere zu String falls es ein Date-Objekt ist
                  const dateStr = String(value);
                  
                  // Falls es bereits im Format DD.MM.YYYY ist
                  if (dateStr.includes('.') && dateStr.split('.').length >= 2) {
                    const [day, month] = dateStr.split('.');
                    // Entferne alle nicht-numerischen Zeichen (wie "+") und formatiere
                    const cleanDay = day.replace(/[^0-9]/g, '').padStart(2, '0');
                    const cleanMonth = month.replace(/[^0-9]/g, '').padStart(2, '0');
                    return `${cleanDay}.${cleanMonth}`;
                  }
                  
                  // Falls es ein ISO-Date oder anderes Format ist, versuche es zu parsen
                  const date = new Date(value);
                  if (!isNaN(date.getTime())) {
                    const day = date.getDate().toString().padStart(2, '0');
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    return `${day}.${month}`;
                  }
                  
                  return dateStr.length > 10 ? dateStr.substring(0, 10) : dateStr;
                } catch {
                  return String(value).substring(0, 8) || '-';
                }
              }}
            />
            <YAxis
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              label={{
                value: "Aril-Werte",
                angle: -90,
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => {
                    // Bereinige das Datum von unerwünschten Zeichen
                    const cleanLabel = String(label).replace(/[^0-9.]/g, '');
                    return `Datum: ${cleanLabel}`;
                  }}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="ninioSapAril"
              name={chartConfig.ninioSapAril.label}
              fill={chartConfig.ninioSapAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioWitronAril"
              name={chartConfig.nioWitronAril.label}
              fill={chartConfig.nioWitronAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioSiemensAril"
              name={chartConfig.nioSiemensAril.label}
              fill={chartConfig.nioSiemensAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioProzessAril"
              name={chartConfig.nioProzessAril.label}
              fill={chartConfig.nioProzessAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioSonstigesAril"
              name={chartConfig.nioSonstigesAril.label}
              fill={chartConfig.nioSonstigesAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: SAP: {chartData.length > 0 ? avgSap.toFixed(1) : 'N/A'} |
              Witron: {chartData.length > 0 ? avgWitron.toFixed(1) : 'N/A'} |
              Siemens: {chartData.length > 0 ? avgSiemens.toFixed(1) : 'N/A'}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt: {chartData.length > 0 ? totalAll : 0} |
              {chartData.length > 0
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank`
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
