import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { authService } from '@/services/auth.service';
import { useToast } from '@/components/ui/use-toast';
import { useAuthContext } from '@/contexts/AuthContext';
import { LabelForm } from '@/components/auth/LabelForm';

// Bild als ES-Module importieren (Vite/Electron-sicher)
import cardBg from '@/assets/LeitstandLogin.png';

const RegistrationPage = () => {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated, isLoading: authLoading } = useAuthContext();

  // Check if user is already authenticated
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      navigate({ to: '/' });
    }
  }, [isAuthenticated, authLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Basic validation
    if (!email || !username || !name || !password || !confirmPassword) {
      setError('Bitte füllen Sie alle Felder aus.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 8) {
      setError('Das Passwort muss mindestens 8 Zeichen lang sein.');
      return;
    }

    setIsLoading(true);

    try {
      console.log('🔄 Attempting registration for user:', username);
      const result = await authService.register({
        email,
        username,
        name,
        password,
      });

      if (result.success) {
        console.log('✅ Registration successful');

        toast({
          title: 'Registrierung erfolgreich',
          description: 'Sie können sich jetzt anmelden.',
        });

        // Navigate to login page with success parameter
        navigate({ to: '/login', search: { registered: 'true' } });
      } else {
        console.log('❌ Registration failed:', result.message);
        setError(result.message || 'Registrierung fehlgeschlagen.');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setError('Ein unerwarteter Fehler ist aufgetreten.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle navigation to login page
  const handleNavigateToLogin = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate({ to: '/login' });
  };

  return (
    <div className="relative min-h-screen flex justify-center items-center bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Image Card - eine Ebene dahinter */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-0 w-225">
        <img
          src={cardBg}
          alt="Background Card Image"
          className="w-full h-225 object-cover rounded"
          onError={(e) => {
            console.error('Image failed to load:', cardBg);
          }}
        />
      </div>

      {/* Registration Form - im Vordergrund ohne Hintergrund */}
      <div className="absolute top-82 right-170 z-10 w-full max-w-90 p-4 transform rotate-[7.5deg] transition-transform duration-300">
        <LabelForm
          title="Registrieren"
          description="Benutzerkonto Erstellen"
          className="-mt-1 [&_.label-form-header_h1]:mb-1 [&_.label-form-header_p]:mt-2"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          styleConfig={{ backgroundColor: 'transparent' }}
        >
          <div className="label-stack">
            <div>
              <Label
                htmlFor="name"
                className="label-form-label"
              >
                Vollständiger Name
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Vollständiger Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                variant="ghost"
                className="-mt-4"
                required
                autoComplete="name"
              />
            </div>

            <div>
              <Label
                htmlFor="email"
                className="label-form-label"
              >
                E-Mail-Adresse
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                variant="ghost"
                className="-mt-4"
                required
                autoComplete="email"
              />
            </div>

            <div>
              <Label
                htmlFor="username"
                className="label-form-label"
              >
                Benutzername
              </Label>
              <Input
                id="username"
                type="text"
                placeholder="Ihr Benutzername"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                variant="ghost"
                className="-mt-4"
                required
                autoComplete="username"
              />
            </div>

            <div>
              <Label
                htmlFor="password"
                className="label-form-label"
              >
                Passwort
              </Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                variant="ghost"
                className="-mt-4"
                required
                autoComplete="new-password"
              />
            </div>

            <div>
              <Label
                htmlFor="confirmPassword"
                className="label-form-label"
              >
                Passwort bestätigen
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                variant="ghost"
                className="-mt-3"
                required
                autoComplete="new-password"
              />
            </div>

            <Button
              type="submit"
              variant="link"
              className="text-center font-bold text-3xl mt-1"
              disabled={isLoading}
            >
              {isLoading ? 'Registrierung läuft...' : 'SIGN-UP'}
            </Button>
          </div>
        </LabelForm>

        {/* Login Link */}
        <div className="text-center -mt-8 relative z-20">
          <p className="label-text-secondary text-sm">
            Bereits ein Konto?{' '}
            <button
              type="button"
              className="label-text-primary hover:underline font-semibold cursor-pointer bg-transparent border-none p-0 inline"
              onClick={handleNavigateToLogin}
            >
              Anmelden
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegistrationPage;