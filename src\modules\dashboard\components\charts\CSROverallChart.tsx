"use client"

import { TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface CSROverallChartProps {
  data: Array<{
    date: string
    achievedItems: number
    missedItems: number
    punctualityPercent: number
  }>
}

const chartConfig = {
  achievedItems: {
    label: "Achieved Items",
    color: "hsl(142.1 76.2% 36.3%)", // <PERSON><PERSON><PERSON>n für positive Werte
  },
  missedItems: {
    label: "Missed Items", 
    color: "hsl(346.8 77.2% 49.8%)", // Rot für negative Werte
  },
} satisfies ChartConfig

export function CSROverallChart({ data }: CSROverallChartProps) {
  const chartData = data.map(item => ({
    date: new Date(item.date).toLocaleDateString('de-DE', { 
      month: 'short', 
      day: 'numeric' 
    }),
    achieved: item.achievedItems,
    missed: -item.missedItems, // Negative für visuelle Darstellung
    punctuality: item.punctualityPercent
  }))

  const avgPunctuality = data.reduce((sum, item) => sum + item.punctualityPercent, 0) / data.length
  const trend = avgPunctuality > 85 ? { value: 2.3, isPositive: true } : { value: 1.8, isPositive: false }

  return (
    <Card className="border-1 border-[#ff7a05]/30">
      <CardHeader>
        <CardTitle className="text-lg font-heading">Overall CSR Performance</CardTitle>
        <CardDescription>Zeigt die Performance auf die Lieferunterpositionen</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[250px] w-[825px]">
          <BarChart accessibilityLayer data={chartData} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid vertical={false} />
            <XAxis 
              dataKey="date"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              angle={-45}
              textAnchor="end"
              height={27}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {  
                try {
                  // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                  const date = new Date(value);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleDateString('de-DE', {
                      day: '2-digit',
                      month: '2-digit'
                    });
                  }
                   // Falls es kein gültiges Datum ist, zeige den String direkt
                   return String(value);
                  } catch {
                    return value;
                  }
                }}
              />
            <YAxis 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              tickFormatter={(value) => `${value}`}
            />
            <ChartTooltip
              content={<ChartTooltipContent 
                labelClassName="font-bold"
                formatter={(value, name) => [
                  Math.abs(Number(value)).toString(),
                  name === 'achieved' ? 'Achieved Items' : 'Missed Items'
                ]}
              />}
            />
            <Bar 
              dataKey="achieved" 
              fill="var(--color-achievedItems)"
              radius={[4, 4, 0, 0]}
              stroke="#000000"
              strokeWidth={2}
            />
            <Bar 
              dataKey="missed" 
              fill="var(--color-missedItems)"
              radius={[0, 0, 4, 4]}
              stroke="#000000"
              strokeWidth={2}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          {trend.isPositive ? (
            <>
              Aufsteigender Trend {trend.value}% für die ausgewählte Periode
              <TrendingUp className="h-4 w-4 text-green-500" />
            </>
          ) : (
            <>
               Absteigender Trend {trend.value}% für die ausgewählte Periode
              <TrendingDown className="h-4 w-4 text-red-500" />
            </>
          )}
        </div>
        <div className="text-muted-foreground leading-none">
          Durchschnitt der Pünktlichen Lieferpositionen: {avgPunctuality.toFixed(1)}% über den ausgewählten Zeitraum
        </div>
      </CardFooter>
    </Card>
  )
}