// Pfad: src/components/ui/Card_SubtlePattern.tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface SubtlePatternCardProps {
  title: string;
  value: React.ReactNode;
  subtitle: string;
  icon: React.ReactNode;
  className?: string;
  valueClassName?: string;
}

export const SubtlePatternCard = ({
  title,
  value,
  subtitle,
  icon,
  className,
  valueClassName,
}: SubtlePatternCardProps) => {
  return (
    <div
      className={cn(
        // Light mode
        'relative h-full overflow-hidden rounded-xl border border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-300',
        // Dark mode
        'dark:border-slate-200 dark:bg-slate-900 dark:hover:border-slate-300',
        className
      )}
    >
      {/* Dekoratives Hintergrund-Pattern */}
      <div
        className={cn(
          // Light mode pattern
          "absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)]",
          "bg-[size:24px_24px]",
          "[mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_60%,transparent_100%)]",
          // Dark mode pattern -> etwas heller
          "dark:bg-[linear-gradient(to_right,#ffffff12_1px,transparent_1px),linear-gradient(to_bottom,#ffffff12_1px,transparent_1px)]"
        )}
      ></div>

      {/* Inhalt der Karte */}
      <div className="relative z-10 flex h-full flex-col justify-between">
        {/* Titel + Icon */}
        <div className="flex items-start justify-between">
          <h3 className="text-sm font-medium text-black dark:text-white">
            {title}
          </h3>
          <div className="text-[#ff7a05]">{icon}</div>
        </div>

        {/* Wert + Untertitel */}
        <div className="mt-4">
          <div
            className={cn(
              'text-2xl font-bold text-black dark:text-white',
              valueClassName
            )}
          >
            {value}
          </div>
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        </div>
      </div>
    </div>
  );
};
