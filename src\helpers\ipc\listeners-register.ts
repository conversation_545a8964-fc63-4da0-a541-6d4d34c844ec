import { BrowserWindow } from "electron";
import { addWindowEventListeners } from "./window/window-listeners";
import { registerConfigHandlers } from "./config/config-handlers";
import { registerTeamsHandlers } from "./teams/teams-handlers";

/**
 * Registriert alle Event-Listener für die IPC-Kommunikation
 * @param mainWindow Das Hauptfenster der Anwendung
 */
export default function registerListeners(mainWindow: BrowserWindow) {
  // Registriere Fenster-Listener
  addWindowEventListeners(mainWindow);
  
  // Registriere Konfigurations-Handler
  registerConfigHandlers();
  
  // Registriere Teams-Handler
  registerTeamsHandlers();

  console.log('Alle IPC-Handler wurden registriert (Database und Theme Handler entfernt, Teams Handler hinzugefügt).');
}
