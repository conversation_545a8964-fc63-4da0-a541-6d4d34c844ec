/**
 * RAGManagementPage - Document Upload and RAG Management
 * 
 * Central page for managing RAG documents and knowledge base
 */

import React, { useState, useEffect } from 'react';
import { Search, FileText, Database, Clock, BarChart, Layers, BrainCircuit, Settings, ArrowLeft } from 'lucide-react';
import { useNavigate } from '@tanstack/react-router';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import DocumentUpload from '@/components/rag/DocumentUpload';
import DocumentList from '@/components/rag/DocumentList';
import SmoothTab from '@/components/Animation/kokonutui/smooth-tab';
import RAGSettingsManager from '@/modules/ai/components/settings/RAGSettingsManager';

interface RAGStats {
    totalDocuments: number;
    totalChunks: number;
    totalEmbeddings: number;
    totalKnowledgeBases: number;
    averageChunkSize: number;
    storageSize: number;
    averageResponseTime: number;
    averageSimilarity: number;
}

interface SearchResult {
    chunk: {
        id: string;
        content: string;
        document: {
            title: string;
            knowledgeBase: {
                name: string;
            };
        };
    };
    similarity: number;
}

interface RecentQuery {
    id: number;
    query: string;
    resultsFound: number;
    executionTimeMs: number;
    createdAt: string;
}

export const RAGManagementPage: React.FC = () => {
    const navigate = useNavigate();
    const [stats, setStats] = useState<RAGStats | null>(null);
    const [isLoadingStats, setIsLoadingStats] = useState(true);
    const [statsError, setStatsError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
    const [recentQueries, setRecentQueries] = useState<RecentQuery[]>([]);
    const [isSearching, setIsSearching] = useState(false);
    const [selectedTab, setSelectedTab] = useState('upload');
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // Utility function for API calls with fallback
    const fetchWithFallback = async (endpoint: string, options?: RequestInit) => {
        try {
            return await fetch(endpoint, options);
        } catch (proxyError) {
            console.warn('Proxy failed, trying direct backend URL:', proxyError);
            return await fetch(`http://localhost:3001${endpoint}`, options);
        }
    };

    // Load initial data
    useEffect(() => {
        loadStats();
        loadRecentQueries();
    }, []);

    // Debug stats changes
    useEffect(() => {
        console.log('Stats state changed:', stats);
        console.log('Loading state:', isLoadingStats);
        console.log('Error state:', statsError);
    }, [stats, isLoadingStats, statsError]);

    const loadStats = async () => {
        try {
            setIsLoadingStats(true);
            setStatsError(null);
            
            const response = await fetchWithFallback('/api/rag/statistics');

            if (response.ok) {
                const data = await response.json();
                console.log('RAG Statistics loaded:', data);
                console.log('Setting stats to:', data.data);
                setStats(data.data);
            } else {
                const errorText = await response.text();
                console.error('Failed to load RAG statistics:', response.status, errorText);
                setStatsError(`Fehler beim Laden der Statistiken (${response.status})`);
            }
        } catch (error) {
            console.error('Error loading RAG statistics:', error);
            setStatsError('Verbindungsfehler beim Laden der Statistiken');
        } finally {
            setIsLoadingStats(false);
        }
    };

    const loadRecentQueries = async () => {
        try {
            const response = await fetchWithFallback('/api/rag/queries/recent?limit=10');

            if (response.ok) {
                const data = await response.json();
                setRecentQueries(data.data);
            }
        } catch (error) {
            console.error('Error loading recent queries:', error);
        }
    };

    const handleSearch = async () => {
        if (!searchQuery.trim()) return;

        setIsSearching(true);
        try {
            const response = await fetchWithFallback(`/api/rag/documents/search?query=${encodeURIComponent(searchQuery)}&limit=5`);

            if (response.ok) {
                const data = await response.json();
                setSearchResults(data.data.results);
                loadRecentQueries(); // Refresh recent queries
            }
        } catch (error) {
            console.error('Error searching documents:', error);
        } finally {
            setIsSearching(false);
        }
    };

    const handleUploadComplete = () => {
        loadStats(); // Refresh statistics after upload
        setRefreshTrigger(prev => prev + 1); // Trigger document list refresh
    };

    const handleUploadSuccess = () => {
        // Switch to documents tab to show the uploaded document
        setTimeout(() => {
            setSelectedTab('documents');
        }, 2000);
    };

    const handleDocumentDeleted = () => {
        loadStats(); // Refresh statistics after deletion
        setRefreshTrigger(prev => prev + 1); // Trigger document list refresh
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (dateString: string): string => {
        return new Date(dateString).toLocaleString('de-DE');
    };

    const cardItems = [
        {
          title: 'Dokumente',
          icon: <FileText className="size-6" />,
          value: stats?.totalDocuments ?? 0,
          subtitle: `${stats?.totalChunks ?? 0} Textabschnitte`,
          errorSubtitle: statsError,
        },
        {
          title: 'Embeddings',
          icon: <Database className="size-6" />,
          value: stats?.totalEmbeddings ?? 0,
          subtitle: formatFileSize(stats?.storageSize ?? 0),
          errorSubtitle: 'Daten nicht verfügbar',
        },
        {
          title: 'Wissensbereiche',
          icon: <BarChart className="size-6" />,
          value: stats?.totalKnowledgeBases ?? 0,
          subtitle: 'Kategorien verfügbar',
          errorSubtitle: 'Daten nicht verfügbar',
        },
        {
          title: 'Chunk-Größe',
          icon: <Layers className="size-6" />,
          value: stats?.averageChunkSize ?? 0,
          subtitle: 'Tokens durchschnittlich',
          errorSubtitle: 'Daten nicht verfügbar',
        },
      ];

    const renderTabContent = () => {
        switch (selectedTab) {
            case 'upload':
                return (
                    <div className="flex items-center justify-center">
                        <DocumentUpload
                            onUploadComplete={handleUploadComplete}
                            onUploadSuccess={handleUploadSuccess}
                            className="max-w-7xl"
                        />
                    </div>
                );

            case 'documents':
                return (
                    <div className="flex items-center justify-center">
                        <DocumentList
                            onDocumentDeleted={handleDocumentDeleted}
                            refreshTrigger={refreshTrigger}
                            className="max-w-7xl w-full"
                        />
                    </div>
                );

            case 'search':
                return (
                    <div className="items-center justify-center">
                        <Card className={`w-full border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200`} >
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BrainCircuit className="h-5 w-5" />
                                    Semantischer Test
                                </CardTitle>
                                <CardDescription className='text-gray-600'>
                                    Teste wie JASZ die Hochgeladenen Dokumente gelernt und verstanden hat
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex gap-2">
                                    <Input
                                        className='bg-white'
                                        placeholder="Frage oder Suchbegriff eingeben..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                    <Button onClick={handleSearch} disabled={isSearching} className="bg-white">
                                        <Search className="h-4 w-4 mr-2" />
                                        {isSearching ? 'Suche...' : 'Suchen'}
                                    </Button>
                                </div>

                                {searchResults.length > 0 && (
                                    <div className="space-y-3">
                                        <h3 className="text-lg font-semibold">Suchergebnisse</h3>
                                        {searchResults.map((result, index) => (
                                            <Card key={index} className="p-4">
                                                <div className="flex justify-between items-start mb-2">
                                                    <h4 className="font-medium">{result.chunk.document.title}</h4>
                                                    <Badge variant="secondary">
                                                        {(result.similarity * 100).toFixed(1)}% Ähnlichkeit
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-gray-600 mb-2">
                                                    {result.chunk.document.knowledgeBase.name}
                                                </p>
                                                <p className="text-sm">{result.chunk.content}</p>
                                            </Card>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                );

            case 'analytics':
                return (
                    <Card className={`w-full border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200`} >
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Letzte Suchanfragen
                            </CardTitle>
                            <CardDescription>
                                Übersicht der kürzlich durchgeführten Suchen
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recentQueries.length > 0 ? (
                                <div className="space-y-3">
                                    {recentQueries.map((query) => (
                                        <div key={query.id} className="flex justify-between items-center p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <p className="font-medium">{query.query}</p>
                                                <p className="text-sm text-gray-500">
                                                    {formatDate(query.createdAt)}
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <Badge variant="outline">
                                                    {query.resultsFound} Ergebnisse
                                                </Badge>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    {query.executionTimeMs}ms
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                                    <p className="text-gray-500 mb-2">
                                        Noch keine Suchanfragen vorhanden
                                    </p>
                                    <p className="text-sm text-gray-400">
                                        Verwenden Sie die "Semantische Suche", um Ihre ersten Abfragen zu testen
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                );

            case 'settings':
                return (
                    <div className="flex items-center justify-center">
                        <RAGSettingsManager className="max-w-7xl w-full" />
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <div className="w-full bg-bg min-h-screen p-8 relative">
            <div className="space-y-6">
                {/* Zurück-Button links oben */}
                <div className="absolute top-0 left-4 z-10">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate({ to: "/modules/ai" })}
                    >
                        <ArrowLeft className="h-8 w-8 text-orange-600" />
                    </Button>
                </div>

                {/* Header */}
                <div className="mb-6">
                    <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                        <Database className="h-8 w-8 text-indigo-600" />
                        JASZ AI-Wissensdatenbank
                    </h1>
                    <p className="text-gray-600 mt-1">
                        Verwaltung der Dokumenten und Wissensdatenbank für JASZ
                    </p>
                </div>

                {/* Error Banner */}
                {statsError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <div className="text-red-600">⚠️</div>
                            <span className="text-red-800">{statsError}</span>
                        </div>
                        <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={loadStats}
                            disabled={isLoadingStats}
                        >
                            {isLoadingStats ? 'Laden...' : 'Erneut versuchen'}
                        </Button>
                    </div>
                )}

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {cardItems.map((card, index) => (
                        <SubtlePatternCard
                        key={index}
                        title={card.title}
                        icon={card.icon}
                        value={
                            isLoadingStats
                            ? '...'
                            : statsError
                                ? 'Fehler'
                                : card.value
                        }
                        subtitle={
                            isLoadingStats
                            ? 'Lade Daten...'
                            : statsError
                                ? card.errorSubtitle ?? 'Fehlerdetails nicht verfügbar'
                                : card.subtitle
                        }
                        valueClassName={statsError ? 'text-red-500' : ''}
                        />
                    ))}
                </div>

                {/* Main Content Tabs */}
                <div className="space-y-6">
                    {/* Zentraler Container für Tabs und Content */}
                    <div className="flex justify-center">
                        <div className="w-full max-w-7xl space-y-6">
                            <SmoothTab
                                items={[
                                    {
                                        id: 'upload',
                                        title: 'Dokumente hochladen',
                                        color: 'bg-indigo-500 hover:bg-indigo-600'
                                    },
                                    {
                                        id: 'documents',
                                        title: 'Dokumentenverwaltung',
                                        color: 'bg-blue-500 hover:bg-blue-600'
                                    },
                                    {
                                        id: 'search',
                                        title: 'Semantischer Test',
                                        color: 'bg-purple-500 hover:bg-purple-600'
                                    },
                                    {
                                        id: 'analytics',
                                        title: 'Analytics',
                                        color: 'bg-emerald-500 hover:bg-emerald-600'
                                    },
                                    {
                                        id: 'settings',
                                        title: 'Einstellungen',
                                        color: 'bg-gray-500 hover:bg-gray-600'
                                    }
                                ]}
                                defaultTabId="upload"
                                onChange={setSelectedTab}
                            />

                            {/* Tab Content */}
                            <div className="w-full">
                                {renderTabContent()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RAGManagementPage;