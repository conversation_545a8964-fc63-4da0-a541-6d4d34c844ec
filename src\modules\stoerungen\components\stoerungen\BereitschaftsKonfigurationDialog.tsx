import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Settings, Users, Calendar, BarChart3, X, ShieldAlert } from 'lucide-react';

// Fach-Views
import { BereitschaftsPersonenVerwaltung } from './BereitschaftsPersonenVerwaltung';
import { BereitschaftsWochenplanung } from './BereitschaftsWochenplanung';
import { BereitschaftsKonfigurationComponent } from './BereitschaftsKonfiguration';
import { BereitschaftsEskalationsmatrix } from './BereitschaftsEskalationsmatrix';

interface BereitschaftsKonfigurationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfigurationChange?: () => void;
}

export const BereitschaftsKonfigurationDialog: React.FC<BereitschaftsKonfigurationDialogProps> = ({
  open,
  onOpenChange,
  onConfigurationChange
}) => {
  const [activeTab, setActiveTab] = useState<'personen' | 'planung' | 'konfiguration' | 'eskalation' | 'statistiken'>('personen');

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={() => onOpenChange(false)}
      />
      
      {/* Dialog Content */}
      <div className="relative bg-white rounded-lg shadow-lg max-w-5xl max-h-[90vh] w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            Bereitschaftsplan-Verwaltung
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-4">
            {/* Tab Navigation */}
            <div className="flex space-x-2 border-b">
              <button
                onClick={() => setActiveTab('personen')}
                className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
                  activeTab === 'personen'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Users className="h-4 w-4" />
                Personen
              </button>
              <button
                onClick={() => setActiveTab('planung')}
                className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
                  activeTab === 'planung'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Calendar className="h-4 w-4" />
                Planung
              </button>
              <button
                onClick={() => setActiveTab('konfiguration')}
                className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
                  activeTab === 'konfiguration'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Settings className="h-4 w-4" />
                Einstellungen
              </button>
              <button
                onClick={() => setActiveTab('eskalation')}
                className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
                  activeTab === 'eskalation'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <ShieldAlert className="h-4 w-4" />
                Eskalation
              </button>
              <button
                onClick={() => setActiveTab('statistiken')}
                className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
                  activeTab === 'statistiken'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <BarChart3 className="h-4 w-4" />
                Statistiken
              </button>
            </div>

            {/* Tab Content */}
            <div className="min-h-[420px] overflow-auto pr-1">
              {activeTab === 'personen' && (
                <BereitschaftsPersonenVerwaltung onPersonenChange={onConfigurationChange} />
              )}

              {activeTab === 'planung' && (
                <BereitschaftsWochenplanung onPlanungChange={onConfigurationChange} />
              )}

              {activeTab === 'konfiguration' && (
                <BereitschaftsKonfigurationComponent onConfigurationChange={onConfigurationChange} />
              )}

              {activeTab === 'eskalation' && (
                <BereitschaftsEskalationsmatrix />
              )}

              {activeTab === 'statistiken' && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-lg font-semibold mb-2">Bereitschafts-Statistiken</h3>
                  <p className="text-gray-600 mb-4">
                    Analysen folgen. Nutzen Sie vorerst Personen, Planung und Einstellungen.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};