import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Settings, Save, RefreshCw, Clock, Mail, Users, Calendar } from 'lucide-react';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import { BereitschaftsKonfiguration, WOCHEN_TAGE, WochenTag } from '@/types/bereitschafts';
// import { toast } from 'sonner';

interface BereitschaftsKonfigurationProps {
  onConfigurationChange?: () => void;
}

export const BereitschaftsKonfigurationComponent: React.FC<BereitschaftsKonfigurationProps> = ({
  onConfigurationChange
}) => {
  const [konfiguration, setKonfiguration] = useState<BereitschaftsKonfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    wechselTag: 5,
    wechselUhrzeit: '08:00',
    rotationAktiv: true,
    benachrichtigungTage: 2,
    emailBenachrichtigung: true
  });

  useEffect(() => {
    loadKonfiguration();
  }, []);

  const loadKonfiguration = async () => {
    try {
      setLoading(true);
      const config = await bereitschaftsService.getKonfiguration();
      setKonfiguration(config);

      // Ensure all values are properly defined with fallbacks
      setFormData({
        wechselTag: config?.wechselTag ?? 5,
        wechselUhrzeit: config?.wechselUhrzeit ?? '08:00',
        rotationAktiv: config?.rotationAktiv ?? true,
        benachrichtigungTage: config?.benachrichtigungTage ?? 2,
        emailBenachrichtigung: config?.emailBenachrichtigung ?? true
      });
    } catch (error) {
      console.error('Fehler beim Laden der Konfiguration:', error);
      console.error('Konfiguration konnte nicht geladen werden');

      // Ensure formData has safe default values even when API fails
      setFormData({
        wechselTag: 5,
        wechselUhrzeit: '08:00',
        rotationAktiv: true,
        benachrichtigungTage: 2,
        emailBenachrichtigung: true
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Validierung mit Fallback-Werten
      const wechselTag = formData.wechselTag ?? 5;
      const wechselUhrzeit = formData.wechselUhrzeit ?? '08:00';
      const benachrichtigungTage = formData.benachrichtigungTage ?? 2;

      if (wechselTag < 1 || wechselTag > 7) {
        console.error('Wechseltag muss zwischen 1 (Montag) und 7 (Sonntag) liegen');
        return;
      }

      if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(wechselUhrzeit)) {
        console.error('Ungültige Uhrzeit (Format: HH:MM)');
        return;
      }

      if (benachrichtigungTage < 0 || benachrichtigungTage > 14) {
        console.error('Benachrichtigungstage müssen zwischen 0 und 14 liegen');
        return;
      }

      const updatedConfig = await bereitschaftsService.updateKonfiguration(formData);
      setKonfiguration(updatedConfig);

      console.log('Konfiguration erfolgreich gespeichert');
      onConfigurationChange?.();
    } catch (error) {
      console.error('Fehler beim Speichern der Konfiguration:', error);
      console.error('Konfiguration konnte nicht gespeichert werden');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (konfiguration) {
      setFormData({
        wechselTag: konfiguration.wechselTag,
        wechselUhrzeit: konfiguration.wechselUhrzeit,
        rotationAktiv: konfiguration.rotationAktiv,
        benachrichtigungTage: konfiguration.benachrichtigungTage,
        emailBenachrichtigung: konfiguration.emailBenachrichtigung
      });
      console.log('Änderungen zurückgesetzt');
    }
  };

  const hasChanges = konfiguration && (
    (formData.wechselTag ?? 5) !== konfiguration.wechselTag ||
    (formData.wechselUhrzeit ?? '08:00') !== konfiguration.wechselUhrzeit ||
    (formData.rotationAktiv ?? true) !== konfiguration.rotationAktiv ||
    (formData.benachrichtigungTage ?? 2) !== konfiguration.benachrichtigungTage ||
    (formData.emailBenachrichtigung ?? true) !== konfiguration.emailBenachrichtigung
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Bereitschafts-Konfiguration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="ml-2">Konfiguration wird geladen...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            Bereitschafts-Konfiguration
          </CardTitle>
          {hasChanges && (
            <Badge variant="outline" className="text-orange-600 border-orange-300">
              Ungespeicherte Änderungen
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Wechsel-Einstellungen */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="h-4 w-4 text-blue-600" />
            <h4 className="font-semibold text-gray-900">Wechsel-Einstellungen</h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="wechselTag">Wechseltag</Label>
              <Select
                value={formData.wechselTag?.toString() ?? '5'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, wechselTag: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Wechseltag auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(WOCHEN_TAGE).map(([tag, name]) => (
                    <SelectItem key={tag} value={tag}>
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-600">
                Tag der Woche, an dem die Bereitschaft wechselt
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="wechselUhrzeit">Wechselzeit</Label>
              <Input
                id="wechselUhrzeit"
                type="time"
                value={formData.wechselUhrzeit ?? '08:00'}
                onChange={(e) => setFormData(prev => ({ ...prev, wechselUhrzeit: e.target.value }))}
                className="w-full"
              />
              <p className="text-xs text-gray-600">
                Uhrzeit des Bereitschaftswechsels
              </p>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-blue-800">
              <Clock className="h-4 w-4" />
              <span className="font-medium">Aktueller Wechsel:</span>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              Jeden {WOCHEN_TAGE[(formData.wechselTag ?? 5) as WochenTag]} um {formData.wechselUhrzeit ?? '08:00'} Uhr
            </p>
          </div>
        </div>

        {/* Rotation-Einstellungen */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Users className="h-4 w-4 text-blue-600" />
            <h4 className="font-semibold text-gray-900">Rotation-Einstellungen</h4>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <Label htmlFor="rotationAktiv" className="font-medium">
                Automatische Rotation
              </Label>
              <p className="text-sm text-gray-600">
                Automatischer Wechsel der Bereitschaftspersonen
              </p>
            </div>
            <Switch
              id="rotationAktiv"
              checked={formData.rotationAktiv ?? true}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, rotationAktiv: checked }))}
            />
          </div>
        </div>

        {/* Benachrichtigungs-Einstellungen */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Mail className="h-4 w-4 text-blue-600" />
            <h4 className="font-semibold text-gray-900">Benachrichtigungs-Einstellungen</h4>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <Label htmlFor="emailBenachrichtigung" className="font-medium">
                E-Mail-Benachrichtigungen
              </Label>
              <p className="text-sm text-gray-600">
                Automatische E-Mail-Benachrichtigungen senden
              </p>
            </div>
            <Switch
              id="emailBenachrichtigung"
              checked={formData.emailBenachrichtigung ?? true}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, emailBenachrichtigung: checked }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="benachrichtigungTage">Benachrichtigung im Voraus (Tage)</Label>
            <Input
              id="benachrichtigungTage"
              type="number"
              min="0"
              max="14"
              value={formData.benachrichtigungTage ?? 2}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                benachrichtigungTage: parseInt(e.target.value) || 0
              }))}
              className="w-full"
            />
            <p className="text-xs text-gray-600">
              Anzahl Tage vor Bereitschaftsbeginn für Benachrichtigung
            </p>
          </div>
        </div>

        {/* Aktionen */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges || saving}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Zurücksetzen
          </Button>

          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            variant="accept"
          >
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {saving ? 'Speichern...' : 'Speichern'}
          </Button>
        </div>

        {/* Info-Box */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <p className="text-sm text-yellow-800">
            <strong>Hinweis:</strong> Änderungen an der Konfiguration wirken sich auf zukünftige
            Bereitschaftsplanungen aus. Bereits geplante Bereitschaften bleiben unverändert.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};