"use client"

import { TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, Cell, LabelList, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend, 
  ChartLegendContent
} from "@/components/ui/chart"

interface CSRChartProps {
  title: string
  description: string
  data: Array<{
    date: string
    value: number
    label?: string
  }>
  dataKey: string
  unit: string
  trend?: {
    value: number
    isPositive: boolean
  }
}

const chartConfig = {
  value: {
    label: "Value",
    color: "hsl(221.2 83.2% 53.3%)", // Blau als Standard
  },
} satisfies ChartConfig

export function CSRChartPunktuality({ title, description, data, dataKey, unit, trend }: CSRChartProps) {
  return (
    <Card className="border-1 border-[#ff7a05]/30">
      <CardHeader>
        <CardTitle className="text-xl font-heading">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[250px] w-[825px]">
          <BarChart accessibilityLayer data={data} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              angle={-45}
              textAnchor="end"
              height={27}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {  
                try {
                  // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                  const date = new Date(value);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleDateString('de-DE', {
                      day: '2-digit',
                      month: '2-digit'
                    });
                  }
                   // Falls es kein gültiges Datum ist, zeige den String direkt
                   return String(value);
                  } catch {
                    return value;
                  }
                }}
              />
              <YAxis
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                axisLine={{ stroke: "#000000", strokeWidth: 2 }}
                tickFormatter={(value) => `${value}${unit}`}
              />
            <ChartTooltip
              content={<ChartTooltipContent
                labelClassName="font-bold"
                hideIndicator
                formatter={(value, name) => [
                  `${value}${unit}`,
                  name
                ]}
              />}
            />
            <Bar 
              dataKey="value" 
              radius={[4, 4, 0, 0]}
              stroke="#000000"
              strokeWidth={2}
            >
              <LabelList
                position="top"
                dataKey="value"
                fillOpacity={1}
                formatter={(value: number) => `${value}${unit}`}
              />
              {data.map((item, index) => (
                <Cell
                  key={index}
                  fill={item.value >= 0 ? "hsl(142.1 76.2% 36.3%)" : "hsl(346.8 77.2% 49.8%)"}
                />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
      {trend && (
        <CardFooter className="flex-col items-start gap-2 text-sm">
          <div className="flex gap-2 leading-none font-medium">
            {trend.isPositive ? (
              <>
                Aufsteigender Trend {Math.abs(trend.value)}% für die ausgewählte Periode
                <TrendingUp className="h-4 w-4 text-green-500" />
              </>
            ) : (
              <>
                Absteigender Trend {Math.abs(trend.value)}% für die ausgewählte Periode
                <TrendingDown className="h-4 w-4 text-red-500" />
              </>
            )}
          </div>
          <div className="text-muted-foreground leading-none">
            Zeigt {title.toLowerCase()} für den ausgewählten Zeitraum an.
          </div>
        </CardFooter>
      )}
    </Card>
  )
}