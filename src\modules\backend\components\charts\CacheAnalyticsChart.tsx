import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Zap,
  Database,
  TrendingUp,
  RefreshCw,
  HardDrive,
  Clock,
  Target
} from 'lucide-react';
import { motion } from 'framer-motion';

interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  averageAccessTime: number;
}

interface CacheRecommendation {
  type: 'info' | 'warning' | 'success';
  message: string;
}

export function CacheAnalyticsChart() {
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [recommendations, setRecommendations] = useState<CacheRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const chartConfig = {
    hits: {
      label: "Cache Hits",
      color: "oklch(0.685 0.169 237.323)",
    },
    misses: {
      label: "Cache Misses",
      color: "oklch(0.66 0.17 301)",
    },
  } satisfies ChartConfig;

  const fetchCacheData = async () => {
    try {
      const response = await fetch('/api/performance/cache');
      if (!response.ok) throw new Error('Failed to fetch cache data');

      const data = await response.json();
      setCacheStats(data.data.stats);
      setRecommendations(data.data.recommendations || []);
    } catch (error) {
      console.error('Error fetching cache data:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSnapshot = async () => {
    setSaving(true);
    try {
      // Cache snapshot functionality not available in basic performance routes
      // Just refresh the data instead
      await fetchCacheData();
    } catch (error) {
      console.error('Error saving cache snapshot:', error);
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchCacheData();

    // Auto-refresh every 60 seconds
    const interval = setInterval(fetchCacheData, 60000);
    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getCacheEfficiencyStatus = (hitRate: number) => {
    if (hitRate >= 0.8) return { status: 'excellent', color: 'text-green-600', label: 'Ausgezeichnet' };
    if (hitRate >= 0.6) return { status: 'good', color: 'text-blue-600', label: 'Gut' };
    if (hitRate >= 0.4) return { status: 'fair', color: 'text-yellow-600', label: 'Akzeptabel' };
    return { status: 'poor', color: 'text-red-600', label: 'Verbesserung nötig' };
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cache Analytics</CardTitle>
          <CardDescription>Lade Cache-Statistiken...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    );
  }

  if (!cacheStats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cache Analytics</CardTitle>
          <CardDescription>Keine Cache-Daten verfügbar</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Cache-Statistiken konnten nicht geladen werden</div>
        </CardContent>
      </Card>
    );
  }

  const pieData = [
    {
      name: 'Cache Hits',
      value: Math.round(cacheStats.hitRate * 100),
      fill: 'var(--color-hits)',
    },
    {
      name: 'Cache Misses',
      value: Math.round(cacheStats.missRate * 100),
      fill: 'var(--color-misses)',
    },
  ];

  const efficiency = getCacheEfficiencyStatus(cacheStats.hitRate);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Cache Analytics
              <Badge
                className={`${efficiency.color} bg-opacity-10`}
                variant="outline"
              >
                {efficiency.label}
              </Badge>
            </CardTitle>
            <CardDescription>
              Cache-Performance und Effizienz-Metriken
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchCacheData}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={saveSnapshot}
              disabled={saving}
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Database className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Kompakte Cache Hit/Miss Pie Chart */}
        <div className="mb-4">
          <div className="h-[150px]">
            <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[150px]">
              <PieChart>
                <defs>
                  <linearGradient id="fillHits" x1="0" y1="0" x2="1" y2="1">
                    <stop offset="0%" stopColor="var(--color-hits)" stopOpacity={0.8} />
                    <stop offset="100%" stopColor="var(--color-hits)" stopOpacity={0.3} />
                  </linearGradient>
                  <linearGradient id="fillMisses" x1="0" y1="0" x2="1" y2="1">
                    <stop offset="0%" stopColor="var(--color-misses)" stopOpacity={0.8} />
                    <stop offset="100%" stopColor="var(--color-misses)" stopOpacity={0.3} />
                  </linearGradient>
                </defs>
                <Pie
                  data={[
                    {
                      name: 'Cache Hits',
                      value: Math.round(cacheStats.hitRate * 100),
                      fill: 'url(#fillHits)',
                    },
                    {
                      name: 'Cache Misses',
                      value: Math.round(cacheStats.missRate * 100),
                      fill: 'url(#fillMisses)',
                    },
                  ]}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={50}
                  label={({ value }) => `${value}%`}
                  stroke="none"
                />
                <ChartTooltip
                  content={<ChartTooltipContent formatter={(value) => [`${value}%`, '']} />}
                />
              </PieChart>
            </ChartContainer>
          </div>
        </div>

        {/* Kompakte Cache Statistics Grid */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center p-2 border rounded text-xs"
          >
            <div className="flex items-center justify-center mb-1">
              <HardDrive className="h-3 w-3 text-blue-600" />
            </div>
            <div className="text-xs text-muted-foreground">Einträge</div>
            <div className="text-sm font-bold">{cacheStats.totalEntries.toLocaleString()}</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0, transition: { delay: 0.1 } }}
            className="text-center p-2 border rounded text-xs"
          >
            <div className="flex items-center justify-center mb-1">
              <Database className="h-3 w-3 text-purple-600" />
            </div>
            <div className="text-xs text-muted-foreground">Größe</div>
            <div className="text-sm font-bold">{formatBytes(cacheStats.totalSize)}</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
            className="text-center p-2 border rounded text-xs"
          >
            <div className="flex items-center justify-center mb-1">
              <Clock className="h-3 w-3 text-green-600" />
            </div>
            <div className="text-xs text-muted-foreground">Ø Zeit</div>
            <div className="text-sm font-bold">{Math.round(cacheStats.averageAccessTime)}ms</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0, transition: { delay: 0.3 } }}
            className="text-center p-2 border rounded text-xs"
          >
            <div className="flex items-center justify-center mb-1">
              <Target className="h-3 w-3 text-red-600" />
            </div>
            <div className="text-xs text-muted-foreground">Evictions</div>
            <div className="text-sm font-bold">{cacheStats.evictionCount.toLocaleString()}</div>
          </motion.div>
        </div>

        {/* Kompakte Performance Metrics */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between p-2 border rounded text-sm">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <div>
                <div className="font-medium text-xs">Cache Trefferrate</div>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-lg font-bold ${efficiency.color}`}>
                {formatPercentage(cacheStats.hitRate)}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between p-2 border rounded text-sm">
            <div className="flex items-center gap-2">
              <Database className="h-3 w-3 text-blue-600" />
              <div>
                <div className="font-medium text-xs">Cache Effizienz</div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">
                {(cacheStats.hitRate / (cacheStats.hitRate + cacheStats.missRate) * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>

        {/* Kompakte Cache Health Summary */}
        <div className="p-2 bg-muted/50 rounded text-xs">
          <h4 className="text-xs font-medium mb-2">Cache Status</h4>
          <div className="grid grid-cols-3 gap-2 text-center">
            <div>
              <div className={`font-bold text-sm ${efficiency.color}`}>
                {efficiency.label}
              </div>
              <div className="text-muted-foreground text-xs">Bewertung</div>
            </div>
            <div>
              <div className="font-bold text-sm">
                {cacheStats.evictionCount < cacheStats.totalEntries * 0.1 ? 'Niedrig' :
                  cacheStats.evictionCount < cacheStats.totalEntries * 0.3 ? 'Mittel' : 'Hoch'}
              </div>
              <div className="text-muted-foreground text-xs">Evictions</div>
            </div>
            <div>
              <div className="font-bold text-sm">
                {cacheStats.averageAccessTime < 10 ? 'Schnell' :
                  cacheStats.averageAccessTime < 50 ? 'Normal' : 'Langsam'}
              </div>
              <div className="text-muted-foreground text-xs">Geschwindigkeit</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}