/**
 * BentoCard Component
 * 
 * A beautiful card component inspired by the BentoGrid design
 * with hover effects, background patterns, and smooth animations.
 */

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ArrowRight } from 'lucide-react';

interface BentoCardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
  onClick?: () => void;
  color?: string;
  showArrow?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export const BentoCard: React.FC<BentoCardProps> = ({
  title,
  description,
  icon,
  children,
  className,
  onClick,
  color = '#6366f1',
  showArrow = false,
  size = 'medium'
}) => {
  const isClickable = !!onClick;

  return (
    <motion.div
      className={cn(
        'group border-primary/10 bg-background hover:border-primary/30 relative flex h-full flex-col justify-between overflow-hidden rounded-xl border px-4 pt-4 pb-4 shadow-md transition-all duration-500',
        isClickable && 'cursor-pointer',
        className
      )}
      style={{ zIndex: 'auto' }}
      onClick={onClick}
      whileHover={isClickable ? { scale: 1.02, zIndex: 1 } : undefined}
      whileTap={isClickable ? { scale: 0.98, zIndex: 1 } : undefined}
    >
      {/* Background Pattern */}
      <div className="absolute top-0 -right-1/2 z-0 size-full bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      {/* Large Background Icon */}
      {icon && (
        <div 
          className="absolute right-1 bottom-2 scale-[4] transition-all duration-700 group-hover:scale-[4.2] opacity-5 group-hover:opacity-10"
          style={{ color }}
        >
          {icon}
        </div>
      )}

      <div className="relative z-0 flex h-full flex-col justify-between">
        <div>
          {/* Icon Circle */}
          {icon && (
            <div 
              className="mb-3 flex h-10 w-10 items-center justify-center rounded-full shadow transition-all duration-500 group-hover:shadow-lg"
              style={{ 
                backgroundColor: `${color}20`,
                boxShadow: `0 4px 6px -1px ${color}20`
              }}
            >
              <div style={{ color }} className="flex items-center justify-center">
                {React.cloneElement(icon as React.ReactElement, { className: 'h-5 w-5' })}
              </div>
            </div>
          )}

          {/* Title and Description */}
          <div className="mb-3">
            <h3 className="mb-1 text-lg font-semibold tracking-tight">{title}</h3>
            {description && (
              <p className="text-muted-foreground text-xs">{description}</p>
            )}
          </div>

          {/* Custom Content */}
          {children && (
            <div className="mt-2">
              {children}
            </div>
          )}
        </div>

        {/* Action Indicator */}
        {showArrow && isClickable && (
          <div className="mt-2 flex items-center text-xs" style={{ color }}>
            <span className="mr-1">Öffnen</span>
            <motion.div
              className="transition-all duration-500 group-hover:translate-x-1"
              initial={false}
            >
              <ArrowRight className="size-3" />
            </motion.div>
          </div>
        )}
      </div>

      {/* Bottom Gradient Line */}
      <div 
        className="absolute bottom-0 left-0 h-1 w-full blur-2xl transition-all duration-500 group-hover:blur-lg"
        style={{ 
          background: `linear-gradient(to right, ${color}, ${color}50)`
        }}
      />
    </motion.div>
  );
};

export default BentoCard;