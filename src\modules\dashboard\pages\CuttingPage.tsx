import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { CuttingsChart } from "@/modules/dashboard/components/charts/CuttingsChart";
import { ReturnsChart } from "@/modules/dashboard/components/charts/QMeldungenChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { LagerCutsChart } from "@/modules/dashboard/components/charts/LagerCutsChart";
import { SchnitteDataChart } from "@/modules/dashboard/components/charts/SchnitteDataChart";
import { MaschinenEfficiencyChart } from "@/modules/dashboard/components/charts/MaschinenEfficiencyChart";
// import { Scissors } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import cutterIcon from "@/assets/iconCutter.png";

/**
 * Cutting-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Cutting-Bereich an:
 * - Cuttings nach Maschinen als Balkendiagramm
 * - Retouren als Kreisdiagramm
 * - Lagerbestand Schnitte als Balkendiagramm
 */
export default function CuttingPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 3, 15), // 15. April 2025
    to: new Date(2025, 4, 7), // 7. Mai 2025 (alle verfügbaren Daten)
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center">
          <img
            src={cutterIcon}
            alt="Ablängerei"
            className="h-8 w-8 mr-2 object-contain"
          />
          <h1 className="text-3xl font-bold text-black">ABLÄNGEREI</h1>
        </div>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>

      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2">
            <ChartErrorBoundary>
              <LagerCutsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <ReturnsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <CuttingsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <SchnitteDataChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}
