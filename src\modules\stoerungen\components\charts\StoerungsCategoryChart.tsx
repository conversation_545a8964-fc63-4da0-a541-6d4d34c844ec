import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { BarChart, Bar, CartesianGrid, XAxis, YAxis, Cell } from 'recharts';
import { DateRange } from 'react-day-picker';
import { BarChart3, AlertTriangle } from 'lucide-react';
import { Stoerung } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';


interface StoerungsCategoryChartProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

export const StoerungsCategoryChart: React.FC<StoerungsCategoryChartProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const [stoerungen, setStoerungen] = useState<Stoerung[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoerungen = async () => {
      try {
        setError(null);
        const data = await stoerungenService.getStoerungen();
        setStoerungen(data);
      } catch (err) {
        console.error('Error fetching störungen for categories:', err);
        setError('Fehler beim Laden der Kategoriedaten');

        // Fallback: Mock data for development
        const mockData: Stoerung[] = [
          {
            id: 1, title: 'FTS Problem', severity: 'HIGH', status: 'IN_PROGRESS',
            category: 'System', affected_system: 'FTS', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 2, title: 'Scanner defekt', severity: 'MEDIUM', status: 'NEW',
            category: 'Hardware', affected_system: 'Scanner', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 3, title: 'SAP Verbindung', severity: 'CRITICAL', status: 'RESOLVED',
            category: 'Software', affected_system: 'SAP', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 4, title: 'Prozess Störung', severity: 'LOW', status: 'NEW',
            category: 'Prozess', affected_system: 'ARiL', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 5, title: 'Netzwerk Problem', severity: 'HIGH', status: 'IN_PROGRESS',
            category: 'Netzwerk', affected_system: 'Server', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 6, title: 'Hardware Ausfall', severity: 'MEDIUM', status: 'RESOLVED',
            category: 'Hardware', affected_system: 'FTS', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 7, title: 'System Update', severity: 'LOW', status: 'RESOLVED',
            category: 'System', affected_system: 'SAP', created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
        ];
        setStoerungen(mockData);
      }
    };

    fetchStoerungen();
  }, [refreshKey]);

  // Filter by date range if provided
  const filteredStoerungen = React.useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) return stoerungen;

    return stoerungen.filter(stoerung => {
      const createdDate = new Date(stoerung.created_at);
      return createdDate >= dateRange.from! && createdDate <= dateRange.to!;
    });
  }, [stoerungen, dateRange]);

  // Process data for bar charts
  const categoryData = React.useMemo(() => {
    const categories: Record<string, number> = {};

    filteredStoerungen.forEach(stoerung => {
      const category = stoerung.category || 'Unbekannt';
      categories[category] = (categories[category] || 0) + 1;
    });

    // Define chart colors for different categories
    const categoryColors = [
      '#3b82f6', // blue-500
      '#ef4444', // red-500
      '#10b981', // emerald-500
      '#f59e0b', // amber-500
      '#8b5cf6', // violet-500
      '#06b6d4', // cyan-500
      '#84cc16', // lime-500
      '#f97316', // orange-500
    ];

    return Object.entries(categories)
      .map(([name, count], index) => ({
        name,
        count,
        percentage: filteredStoerungen.length > 0 ? Math.round((count / filteredStoerungen.length) * 100) : 0,
        fill: categoryColors[index % categoryColors.length],
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 8); // Top 8 categories for better visibility
  }, [filteredStoerungen]);

  const systemData = React.useMemo(() => {
    const systems: Record<string, number> = {};

    filteredStoerungen.forEach(stoerung => {
      const system = stoerung.affected_system || 'Unbekannt';
      systems[system] = (systems[system] || 0) + 1;
    });

    // Define chart colors for different systems
    const systemColors = [
      '#3b82f6', // blue-500
      '#ef4444', // red-500
      '#10b981', // emerald-500
      '#f59e0b', // amber-500
      '#8b5cf6', // violet-500
      '#06b6d4', // cyan-500
      '#84cc16', // lime-500
      '#f97316', // orange-500
    ];

    return Object.entries(systems)
      .map(([name, count], index) => ({
        name,
        count,
        percentage: filteredStoerungen.length > 0 ? Math.round((count / filteredStoerungen.length) * 100) : 0,
        fill: systemColors[index % systemColors.length],
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 8); // Top 8 systems for better visibility
  }, [filteredStoerungen]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <CategoryBarChart data={categoryData} error={error} />
      <SystemsBarChart data={systemData} error={error} />
    </div>
  );
});

// Category Bar Chart Component
const CategoryBarChart: React.FC<{ data: any[]; error: string | null }> = ({ data, error }) => {
  const chartConfig = {
    count: {
      label: "Anzahl",
      color: "hsl(var(--chart-1))",
    },
    category1: { label: "Kategorie 1", color: "hsl(var(--chart-1))" },
    category2: { label: "Kategorie 2", color: "hsl(var(--chart-2))" },
    category3: { label: "Kategorie 3", color: "hsl(var(--chart-3))" },
    category4: { label: "Kategorie 4", color: "hsl(var(--chart-4))" },
    category5: { label: "Kategorie 5", color: "hsl(var(--chart-5))" },
    category6: { label: "Kategorie 6", color: "hsl(var(--chart-6))" },
    category7: { label: "Kategorie 7", color: "hsl(var(--chart-7))" },
    category8: { label: "Kategorie 8", color: "hsl(var(--chart-8))" },
  };

  if (error || data.length === 0) {
    return (
      <Card className="h-[300px] border-[#ff7a05]/30">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-base">
            <BarChart3 className="h-4 w-4" />
            Störungen nach Kategorie
          </CardTitle>
          <div className="text-xs text-gray-600">
            Verteilung nach Kategorien
          </div>
        </CardHeader>
        <CardContent className="space-y-1 p-2 pt-0">
          {error && (
            <div className="p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-xs text-yellow-800">{error}</p>
            </div>
          )}
          <div className="text-center py-2 text-black">
            <AlertTriangle className="h-6 w-6 mx-auto mb-1 opacity-50" />
            <p className="text-xs">Keine Kategoriedaten verfügbar</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[300px] w-[450px] border-[#ff7a05]/30">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-base">
          <BarChart3 className="h-4 w-4" />
          Störungen nach Kategorie
        </CardTitle>
        <div className="text-xs text-gray-600">
          Verteilung nach Kategorien
        </div>
      </CardHeader>

      <CardContent className="p-0 pt-0">
        <ChartContainer config={chartConfig} className="h-[225px]">
          <BarChart data={data} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              angle={-45}
              textAnchor="end"
              height={80}
              interval={0}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
            />
            <ChartTooltip
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  const data = payload[0].payload;
                  return (
                    <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <div
                          className="w-3 h-3 rounded-sm border border-black"
                          style={{ backgroundColor: data.fill }}
                        />
                        <p className="font-medium text-sm">{`Kategorie: ${data.name}`}</p>
                      </div>
                      <p className="text-sm text-gray-600">{`${data.count} Störungen (${data.percentage}%)`}</p>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Bar
              dataKey="count"
              radius={[4, 4, 0, 0]}
              stroke="#000"
              strokeWidth={2}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

// Systems Bar Chart Component  
const SystemsBarChart: React.FC<{ data: any[]; error: string | null }> = ({ data, error }) => {
  const chartConfig = {
    count: {
      label: "Anzahl",
      color: "hsl(var(--chart-2))",
    },
    system1: { label: "System 1", color: "hsl(var(--chart-1))" },
    system2: { label: "System 2", color: "hsl(var(--chart-2))" },
    system3: { label: "System 3", color: "hsl(var(--chart-3))" },
    system4: { label: "System 4", color: "hsl(var(--chart-4))" },
    system5: { label: "System 5", color: "hsl(var(--chart-5))" },
    system6: { label: "System 6", color: "hsl(var(--chart-6))" },
    system7: { label: "System 7", color: "hsl(var(--chart-7))" },
    system8: { label: "System 8", color: "hsl(var(--chart-8))" },
  };

  if (error || data.length === 0) {
    return (
      <Card className="h-[400px] border-[#ff7a05]/30">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-base">
            <AlertTriangle className="h-4 w-4" />
            Betroffene Systeme
          </CardTitle>
          <div className="text-xs text-gray-600">
            Top 8 betroffene Systeme
          </div>
        </CardHeader>
        <CardContent className="space-y-1 p-2 pt-0">
          {error && (
            <div className="p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-xs text-yellow-800">{error}</p>
            </div>
          )}
          <div className="text-center py-2 text-black">
            <AlertTriangle className="h-6 w-6 mx-auto mb-1 opacity-50" />
            <p className="text-xs">Keine Systemdaten verfügbar</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[300px] border-[#ff7a05]/30">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-base">
          <AlertTriangle className="h-4 w-4" />
          Betroffene Systeme
        </CardTitle>
        <div className="text-xs text-gray-600">
          Top 8 betroffene Systeme
        </div>
      </CardHeader>

      <CardContent className="p-0 pt-0">
        <ChartContainer config={chartConfig} className="h-[225px]">
          <BarChart data={data} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              angle={-45}
              textAnchor="end"
              height={80}
              interval={0}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
            />
            <ChartTooltip
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  const data = payload[0].payload;
                  return (
                    <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <div
                          className="w-3 h-3 rounded-sm border border-black"
                          style={{ backgroundColor: data.fill }}
                        />
                        <p className="font-medium text-sm">{`System: ${data.name}`}</p>
                      </div>
                      <p className="text-sm text-gray-600">{`${data.count} Störungen (${data.percentage}%)`}</p>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Bar
              dataKey="count"
              radius={[4, 4, 0, 0]}
              stroke="#000"
              strokeWidth={2}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

StoerungsCategoryChart.displayName = 'StoerungsCategoryChart';