import React, { useEffect, useState, use<PERSON><PERSON>back, memo } from "react";
import {
  Line,
  CartesianGrid,
  LineChart,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer
} from "recharts";
import { useTranslation } from "react-i18next";
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent, 
  ChartLegend, 
  ChartLegendContent 
} from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import apiService from "@/services/api.service";
import { isWithinInterval } from "date-fns";

// Typdefinitionen
interface DateRange {
  from?: Date;
  to?: Date;
}

interface Lagerauslastung240DataPoint {
  date: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

interface Lagerauslastung240ChartProps {
  data?: Lagerauslastung240DataPoint[];
  dateRange?: DateRange;
}

// Farben für die Linien
const chartConfig = {
  "auslastungA": {
    label: "Auslastung A",
    color: "var(--chart-1)",
  },
  "auslastungB": {
    label: "Auslastung B",
    color: "var(--chart-2)",
  },
  "auslastungC": 
  { label: "Auslastung C",
    color: "var(--chart-3)",
  },
  "gesamt": {
    label: "Gesamt",
    color: "var(--chart-4)",
  }
};


// Hilfsfunktion zur Formatierung des Datums im Format YYYY-MM-DD
const formatDate = (date: Date): string => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return new Date().toISOString().split('T')[0];
  }
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// Hilfsfunktion zum Parsen von Zahlen mit Fallback
const parseNumber = (value: string | number | null | undefined, defaultValue = 0): number => {
  if (value === null || value === undefined) return defaultValue;
  const num = typeof value === 'string' ? parseFloat(value) : value;
  return isNaN(num) ? defaultValue : num;
};

/**
 * Lagerauslastung240 Chart Komponente
 * Zeigt die Lagerauslastung der AAA, BBB und CCC Artikel als drei Linien in einem Diagramm.
 */
export const Lagerauslastung240Chart = memo<Lagerauslastung240ChartProps>(function Lagerauslastung240Chart({
  data: propData,
  dateRange
}) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<Lagerauslastung240DataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [avgAuslastungA, setAvgAuslastungA] = useState<number>(0);
  const [avgAuslastungB, setAvgAuslastungB] = useState<number>(0);
  const [avgAuslastungC, setAvgAuslastungC] = useState<number>(0);
  const [avgGesamt, setAvgGesamt] = useState<number>(0);

  // Berechne Durchschnittswerte
  const calculateAverages = useCallback((data: Lagerauslastung240DataPoint[]) => {
    if (!data || data.length === 0) {
      setAvgAuslastungA(0);
      setAvgAuslastungB(0);
      setAvgAuslastungC(0);
      setAvgGesamt(0);
      return;
    }

    const sumA = data.reduce((sum, item) => sum + item.auslastungA, 0);
    const sumB = data.reduce((sum, item) => sum + item.auslastungB, 0);
    const sumC = data.reduce((sum, item) => sum + item.auslastungC, 0);
    const sumGesamt = data.reduce((sum, item) => sum + (item.gesamt || 0), 0);

    setAvgAuslastungA(sumA / data.length);
    setAvgAuslastungB(sumB / data.length);
    setAvgAuslastungC(sumC / data.length);
    setAvgGesamt(sumGesamt / data.length);
  }, []);

  // Lade Daten über die API
  const loadData = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      // Standardmäßig die letzten 30 Tage laden, wenn kein Datumsbereich angegeben ist
      const defaultEndDate = new Date();
      const defaultStartDate = new Date();
      defaultStartDate.setDate(defaultStartDate.getDate() - 30);
      
      // Datumsparameter für die API vorbereiten
      let startDate: string | undefined = formatDate(defaultStartDate);
      let endDate: string | undefined = formatDate(defaultEndDate);
      
      // Überschreibe mit benutzerdefiniertem Datumsbereich, falls vorhanden
      if (dateRange?.from) {
        startDate = formatDate(new Date(dateRange.from));
      }
      
      if (dateRange?.to) {
        endDate = formatDate(new Date(dateRange.to));
      }
      
      const result = await apiService.getLagerauslastung240Data(startDate, endDate);
      
      if (result && Array.isArray(result) && result.length > 0) {
        const processedData = processData(result);
        setChartData(processedData);
        calculateAverages(processedData);
        setError(null);
      } else {
        setChartData([]);
        setError('Keine Daten für den ausgewählten Zeitraum verfügbar');
      }
    } catch (error) {
      setError(`Fehler beim Laden der Daten: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
      setChartData([]);
    } finally {
      setLoading(false);
    }
  }, [dateRange, calculateAverages]);

  // Verarbeite die Daten für das Diagramm
  const processData = (data: Lagerauslastung240DataPoint[]): Lagerauslastung240DataPoint[] => {
    if (!data || !Array.isArray(data)) {
      return [];
    }
    
    // Bereinige und validiere die Daten
    const cleanedData = data
      .filter(item => {
        // Prüfe auf gültige Datumswerte
        const isValidDate = item.date && !isNaN(new Date(item.date).getTime());
        if (!isValidDate) {
          return false;
        }
        return true;
      })
      .map(item => {
        // Stelle sicher, dass alle erforderlichen Felder vorhanden sind
        const processedItem: Lagerauslastung240DataPoint = {
          date: item.date,
          auslastungA: parseNumber(item.auslastungA, 0),
          auslastungB: parseNumber(item.auslastungB, 0),
          auslastungC: parseNumber(item.auslastungC, 0),
        };
        
        // Berechne den Gesamtdurchschnitt, falls nicht vorhanden
        if (item.gesamt !== undefined) {
          processedItem.gesamt = parseNumber(item.gesamt);
        } else {
          // Durchschnitt der drei Auslastungswerte
          processedItem.gesamt = (processedItem.auslastungA + processedItem.auslastungB + processedItem.auslastungC) / 3;
        }
        
        return processedItem;
      });
    
    // Sortiere die Daten nach Datum
    const sortedData = [...cleanedData].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
    
    return sortedData;
  };

  // Lade die Daten, wenn sich der Datumsbereich ändert oder die Komponente gemountet wird
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Zeige Ladeindikator an, während Daten geladen werden
  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Lagerauslastung ATrL</CardTitle>
          <CardDescription>Lade Daten...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  // Zeige Fehlermeldung an, falls ein Fehler aufgetreten ist
  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Lagerauslastung ATrL</CardTitle>
          <CardDescription className="text-red-500">{error}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center h-64 gap-4">
          <p className="text-red-500">{error}</p>
          <Button onClick={loadData}>
            <Loader2 className="mr-2 h-4 w-4" />
            Erneut versuchen
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Lagerauslastung ATrL</CardTitle>
          <CardDescription>Keine Daten verfügbar</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <p>Keine Daten für den ausgewählten Zeitraum gefunden.</p>
        </CardContent>
      </Card>
    );
  }

  // Rendere das eigentliche Diagramm
  // Skaliere die Daten für die Anzeige von 0-1 auf 0-100
  const chartDisplayData = chartData.map(item => {
    const scaledA = item.auslastungA * 100;
    const scaledB = item.auslastungB * 100;
    const scaledC = item.auslastungC * 100;
    return {
      ...item,
      auslastungA: scaledA,
      auslastungB: scaledB,
      auslastungC: scaledC,
      gesamt: (scaledA + scaledB + scaledC) / 3, // Richtiger Durchschnitt der skalierten Werte
    };
  });

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>Lagerauslastung ATrL</CardTitle>
        <CardDescription>Auslastung der A, B und C Artikel im Lager</CardDescription>
      </CardHeader>
      <CardContent>
          <ChartContainer
            config={chartConfig}
            className="h-60 w-full"
          >
            <LineChart data={chartDisplayData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="date" 
                className="text-xs sm:text-sm"
                tickLine={false}
                axisLine={false}
                tickMargin={6}
                height={40}
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  try {
                  const date = new Date(value);
                  // Formatiere das Datum als TT.MM
                  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                  } catch {
                    return value;
                  }
                }}
              />
              <YAxis 
                className="text-xs sm:text-sm"
                domain={[80, 100]}
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={false}
                tickMargin={1}
                width={35}
                // Beschriftung für die Y-Achse
                label={{ 
                  value: "Auslastung %", 
                  angle: -90, 
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12 },
                  offset: -5
                }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelClassName="font-bold"
                    labelFormatter={(label) => `Datum: ${label}`}
                  />
                }
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Line
                type="monotone"
                dataKey="auslastungA"
                name="Auslastung A"
                fill={chartConfig["auslastungA"].color}
                stroke={chartConfig["auslastungA"].color}
                strokeWidth={3}
                dot={{ fill: "var(--chart-1)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
              />
              <Line
                type="monotone"
                dataKey="auslastungB"
                name="Auslastung B"
                fill={chartConfig["auslastungB"].color}
                stroke={chartConfig["auslastungB"].color}
                strokeWidth={3}
                dot={{ fill: "var(--chart-2)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
              />
              <Line
                type="monotone"
                dataKey="auslastungC"
                name="Auslastung C"
                fill={chartConfig["auslastungC"].color}
                stroke={chartConfig["auslastungC"].color}
                strokeWidth={3}
                dot={{ fill: "var(--chart-3)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
              />
              <Line
                type="monotone"
                dataKey="gesamt"
                name="Durchschnitt"
                fill={chartConfig["gesamt"].color}
                stroke={chartConfig["gesamt"].color}
                strokeDasharray="5 5"
                strokeWidth={3}
                dot={{ fill: "var(--chart-4)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
              />
            </LineChart>
          </ChartContainer>
      </CardContent>
      <CardFooter className="flex w-full items-start gap-2 text-sm">
        <div className="flex items-center">
          <span className="text-black text-md flex items-center">Durchschn. A: {(avgAuslastungA * 100).toFixed(1)}%</span>
        </div>
        <div className="flex items-center">
          <span className="text-black text-md flex items-center">Durchschn. B: {(avgAuslastungB * 100).toFixed(1)}%</span>
        </div>
        <div className="flex items-center">
          <span className="text-black text-md flex items-center">Durchschn. C: {(avgAuslastungC * 100).toFixed(1)}%</span>
        </div>
        <div className="flex items-center">
          <span className="text-black text-md flex items-center">Gesamtdurchschnitt: {((avgAuslastungA + avgAuslastungB + avgAuslastungC) / 3 * 100).toFixed(1)}%</span>
        </div>
      </CardFooter>
    </Card>
  );
});

export default Lagerauslastung240Chart;