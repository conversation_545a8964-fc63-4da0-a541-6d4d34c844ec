{"name": "sfm-chatbot-backend", "version": "1.0.0", "description": "Backend für den SFM Chatbot mit OpenRouter-Integration", "main": "src/server.js", "scripts": {"start": "ts-node src/server.ts", "dev": "nodemon", "build": "tsc", "serve": "node dist/server.js", "shutdown": "node -e \"const http = require('http'); http.get('http://localhost:3001/api/shutdown');\";", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:chat-integration": "jest --testPathPattern=chat-integration-complete", "test:chat-performance": "jest --testPathPattern=chat-performance", "test:chat-e2e": "jest --testPathPattern=chat-e2e-scenarios", "test:chat-all": "ts-node src/tests/run-integration-tests.ts", "prisma:generate": "npm run prisma:generate:sfm && npm run prisma:generate:rag", "prisma:generate:sfm": "prisma generate --schema=./prisma-sfm-dashboard/schema.prisma", "prisma:generate:rag": "prisma generate --schema=./prisma-rag/schema.prisma", "prisma:push": "npm run prisma:push:sfm && npm run prisma:push:rag", "prisma:push:sfm": "prisma db push --schema=./prisma-sfm-dashboard/schema.prisma", "prisma:push:rag": "prisma db push --schema=./prisma-rag/schema.prisma", "rag:init": "ts-node src/scripts/init-rag-database.ts", "rag:reset": "del /f database\\rag_knowledge.db* 2>nul & npm run rag:init", "rag:populate-categories": "ts-node scripts/populate-categories.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.7.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "openai": "^4.43.0", "sqlite-vss": "^0.1.2", "uuid": "^11.0.4", "zod": "^3.25.76"}, "devDependencies": {"@types/better-sqlite3": "^7.6.11", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.12", "@types/multer": "^1.4.13", "@types/node": "^20.3.1", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.0.1", "prisma": "^6.13.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.1.3", "vite": "^7.0.2"}, "engines": {"node": ">=14.0.0"}}