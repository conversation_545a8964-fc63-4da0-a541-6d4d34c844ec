// This allows TypeScript to pick up the magic constants that's auto-generated by Forge's Vite
// plugin that tells the Electron app where to look for the Vite-bundled app code (depending on
// whether you're running in development or production).
declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string;
declare const MAIN_WINDOW_VITE_NAME: string;

// Preload types
interface ThemeModeContext {
  toggle: () => Promise<boolean>;
  dark: () => Promise<void>;
  light: () => Promise<void>;
  system: () => Promise<boolean>;
  current: () => Promise<"dark" | "light" | "system">;
}

interface ElectronWindow {
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
}

interface DatabaseAPI {
  getServiceLevelData: () => Promise<Array<{ datum: string; servicegrad: number }>>;
  getPickingData: () => Promise<Array<unknown>>;
  getReturnsData: () => Promise<Array<unknown>>;
  getDeliveryPositionsData: () => Promise<Array<unknown>>;
  getTagesleistungData: () => Promise<Array<unknown>>;
  getAblaengereiData: () => Promise<Array<unknown>>;
  getLagerCutsData: () => Promise<Array<unknown>>;
  getCuttingsData: () => Promise<Array<unknown>>;
  // Neue Service-Methoden hinzugefügt
  getCuttingChartData: () => Promise<Array<unknown>>;
  getLagerCutsChartData: () => Promise<Array<unknown>>;
  getWEData: () => Promise<Array<unknown>>;
  getSystemAtrlData: () => Promise<Array<unknown>>;
  getSystemArilData: () => Promise<Array<unknown>>;
  getAtrlData: () => Promise<Array<unknown>>;
  getArilData: () => Promise<Array<unknown>>;
  getSchnitteData: () => Promise<Array<unknown>>;
  getMaschinenEfficiency: () => Promise<Array<unknown>>;
}

interface StoerungTeamsData {
  title: string;
  description?: string;
  severity: string;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  status: string;
  send_protocol: boolean;
}

interface TeamsAPI {
  sendStoerung: (data: StoerungTeamsData) => Promise<{ success: boolean; error?: string }>;
  testConnection: () => Promise<{ success: boolean; error?: string }>;
}

interface ElectronAPI {
  database: DatabaseAPI;
  window: {
    minimize: () => void;
    maximize: () => void;
    close: () => void;
  };
  teams: TeamsAPI;
}

declare global {
  interface Window {
    themeMode: ThemeModeContext;
    electronWindow: ElectronWindow;
    electronAPI: ElectronAPI;
  }
}

export {};
