import { useState } from "react";
import { Settings, AlertTriangle, CheckCircle, Activity, Database, BarChart3 } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";
import { WorkflowGrid } from "../components/workflows/WorkflowGrid";
import { WorkflowOverview } from "../components/workflows/WorkflowOverview";
import { WorkflowPerformanceChart } from "../components/charts/WorkflowPerformanceChart";
import { WorkflowLogViewer } from "../components/workflows/WorkflowLogViewer";
import { WorkflowStatus } from "../components/workflows/WorkflowStatus";
import { PerformanceAnalyticsChart } from "../components/charts/PerformanceAnalyticsChart";


export default function WorkflowPage() {
  const [activeTab, setActiveTab] = useState("sap");

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift */}
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-black flex items-center gap-2">
          <Settings className="h-8 w-8" />
          WORKFLOWS
        </h1>
      </div>

      {/* Smooth Tabs für verschiedene Workflow-Ansichten */}
      <div className="space-y-6">
        <div className="flex justify-center">
          <SmoothTab
            items={[
              {
                id: "sap",
                title: "SAP Workflows",
                icon: Database,
                color: "bg-purple-500 hover:bg-purple-600",
              },
              {
                id: "overview",
                title: "Workflow Übersicht",
                icon: Activity,
                color: "bg-blue-500 hover:bg-blue-600",
              },
              {
                id: "performance",
                title: "Performance Analytics",
                icon: BarChart3,
                color: "bg-green-500 hover:bg-green-600",
              },
              {
                id: "logs",
                title: "System Logs",
                icon: AlertTriangle,
                color: "bg-yellow-500 hover:bg-yellow-600",
              },
            ]}
            defaultTabId="sap"
            onChange={handleTabChange}
            hideCardContent={true}
          />
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "sap" && (
            <ChartErrorBoundary>
              <div className="space-y-6">
                <WorkflowStatus />
                <WorkflowGrid />
              </div>
            </ChartErrorBoundary>
          )}

          {activeTab === "overview" && (
            <ChartErrorBoundary>
              <WorkflowOverview />
            </ChartErrorBoundary>
          )}

          {activeTab === "performance" && (
            <ChartErrorBoundary>
              <PerformanceAnalyticsChart />
            </ChartErrorBoundary>
          )}

          {activeTab === "logs" && (
            <ChartErrorBoundary>
              <WorkflowLogViewer />
            </ChartErrorBoundary>
          )}
        </div>
      </div>
    </div>
  );
}