"use strict";
/**
 * Dispatch Repository Implementation
 *
 * Implementiert die DispatchRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Versanddaten.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DispatchRepositoryImpl = void 0;
const cache_service_1 = require("../services/cache.service");
/**
 * Cache-TTL für Dispatch-Daten (verschiedene Datentypen)
 */
const DISPATCH_CACHE_TTL = {
    SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute - häufig aktualisiert
    PERFORMANCE: 2 * 60 * 1000, // 2 Minuten - moderate Aktualisierung
    PICKING: 3 * 60 * 1000, // 3 Minuten - weniger häufig
    DELIVERY: 2 * 60 * 1000, // 2 Minuten
    RETURNS: 5 * 60 * 1000, // 5 Minuten - seltene Änderungen
    METRICS: 10 * 60 * 1000 // 10 Minuten - Aggregierte Daten
};
class DispatchRepositoryImpl {
    constructor(prisma) {
        this.cache = (0, cache_service_1.getBackendCache)();
        this.stats = {
            totalQueries: 0,
            cacheHits: 0,
            cacheMisses: 0,
            hitRate: 0,
            avgQueryTime: 0,
            lastAccessed: new Date()
        };
        this.prisma = prisma;
    }
    /**
     * Service Level Daten mit Cache-Optimierung
     */
    async getServiceLevelData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getServiceLevelData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const whereClause = this.buildDateWhereClause(dateRange);
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    servicegrad: true,
                },
                where: {
                    datum: { not: null },
                    servicegrad: { not: null },
                    ...whereClause
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map(item => ({
                datum: item.datum,
                servicegrad: item.servicegrad || 0,
            }));
        }, DISPATCH_CACHE_TTL.SERVICE_LEVEL);
    }
    /**
     * Tägliche Performance-Daten
     */
    async getDailyPerformanceData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getDailyPerformanceData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const whereClause = this.buildDateWhereClause(dateRange);
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    produzierte_tonnagen: true,
                },
                where: {
                    datum: { not: null },
                    ...whereClause
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map(item => ({
                datum: item.datum,
                value: item.produzierte_tonnagen || 0,
            }));
        }, DISPATCH_CACHE_TTL.PERFORMANCE);
    }
    /**
     * Kommissionierungsdaten
     */
    async getPickingData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getPickingData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const whereClause = this.buildDateWhereClause(dateRange);
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    atrl: true,
                    aril: true,
                    fuellgrad_aril: true,
                },
                where: {
                    datum: { not: null },
                    atrl: { not: null },
                    aril: { not: null },
                    fuellgrad_aril: { not: null },
                    ...whereClause
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map(item => ({
                date: item.datum,
                atrl: item.atrl || 0,
                aril: item.aril || 0,
                fuellgrad_aril: item.fuellgrad_aril || 0,
            }));
        }, DISPATCH_CACHE_TTL.PICKING);
    }
    /**
     * Lieferpositionsdaten
     */
    async getDeliveryPositionsData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getDeliveryPositionsData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const whereClause = this.buildDateWhereClause(dateRange);
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    ausgeliefert_lup: true,
                    rueckstaendig: true,
                },
                where: {
                    datum: { not: null },
                    ausgeliefert_lup: { not: null },
                    rueckstaendig: { not: null },
                    ...whereClause
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map(item => ({
                date: item.datum,
                ausgeliefert_lup: item.ausgeliefert_lup || 0,
                rueckstaendig: item.rueckstaendig || 0,
            }));
        }, DISPATCH_CACHE_TTL.DELIVERY);
    }
    /**
     * Tagesleistungsdaten
     */
    async getTagesleistungData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getTagesleistungData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const whereClause = this.buildDateWhereClause(dateRange);
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    produzierte_tonnagen: true,
                    direktverladung_kiaa: true,
                    umschlag: true,
                    kg_pro_colli: true,
                    elefanten: true,
                },
                where: {
                    datum: { not: null },
                    produzierte_tonnagen: { not: null },
                    ...whereClause
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map(item => ({
                date: item.datum,
                produzierte_tonnagen: item.produzierte_tonnagen || 0,
                direktverladung_kiaa: item.direktverladung_kiaa || 0,
                umschlag: item.umschlag || 0,
                kg_pro_colli: item.kg_pro_colli || 0,
                elefanten: item.elefanten || 0,
            }));
        }, DISPATCH_CACHE_TTL.PERFORMANCE);
    }
    /**
     * Retourendaten aggregiert
     */
    async getReturnsData() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getReturnsData');
        return await this.cachedQuery(cacheKey, async () => {
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    qm_angenommen: true,
                    qm_abgelehnt: true,
                    qm_offen: true,
                },
            });
            const angenommen = result.reduce((sum, item) => sum + (item.qm_angenommen || 0), 0);
            const abgelehnt = result.reduce((sum, item) => sum + (item.qm_abgelehnt || 0), 0);
            const offen = result.reduce((sum, item) => sum + (item.qm_offen || 0), 0);
            return [
                { name: 'Angenommen', value: angenommen },
                { name: 'Abgelehnt', value: abgelehnt },
                { name: 'Offen', value: offen },
            ];
        }, DISPATCH_CACHE_TTL.RETURNS);
    }
    /**
     * Performance-Metriken für einen bestimmten Zeitraum
     */
    async getPerformanceMetrics(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getPerformanceMetrics', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const whereClause = this.buildDateWhereClause(dateRange);
            const result = await this.prisma.dispatchData.aggregate({
                where: whereClause,
                _sum: {
                    produzierte_tonnagen: true,
                    ausgeliefert_lup: true,
                    atrl: true,
                },
                _avg: {
                    servicegrad: true,
                    atrl: true,
                },
                _count: {
                    datum: true,
                },
            });
            // Berechne Periode
            const startDate = dateRange.startDate || '';
            const endDate = dateRange.endDate || '';
            const daysDiff = startDate && endDate
                ? Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 3600 * 24))
                : result._count.datum || 0;
            return {
                totalTonnage: result._sum.produzierte_tonnagen || 0,
                averageServiceLevel: (result._avg.servicegrad || 0) * 100,
                totalDeliveries: result._sum.ausgeliefert_lup || 0,
                averagePickingRate: result._avg.atrl || 0,
                period: {
                    startDate: startDate,
                    endDate: endDate,
                    days: daysDiff,
                },
            };
        }, DISPATCH_CACHE_TTL.METRICS);
    }
    /**
     * Top-Performance-Tage ermitteln
     */
    async getTopPerformanceDays(limit = 10) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getTopPerformanceDays', { limit });
        return await this.cachedQuery(cacheKey, async () => {
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    produzierte_tonnagen: true,
                    servicegrad: true,
                },
                where: {
                    datum: { not: null },
                    produzierte_tonnagen: { not: null },
                    servicegrad: { not: null },
                },
                orderBy: [
                    { produzierte_tonnagen: 'desc' },
                    { servicegrad: 'desc' },
                ],
                take: limit,
            });
            return result.map(item => ({
                datum: item.datum,
                produzierte_tonnagen: item.produzierte_tonnagen || 0,
                servicegrad: (item.servicegrad || 0) * 100,
            }));
        }, DISPATCH_CACHE_TTL.METRICS);
    }
    // Base Repository Implementation (simplified for key methods)
    async findById(id) {
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        return await this.prisma.dispatchData.findUnique({ where: { id: numericId } });
    }
    async findAll(options) {
        return await this.prisma.dispatchData.findMany({
            skip: options === null || options === void 0 ? void 0 : options.offset,
            take: options === null || options === void 0 ? void 0 : options.limit,
            orderBy: (options === null || options === void 0 ? void 0 : options.orderBy) ? { [options.orderBy]: options.orderDirection || 'asc' } : undefined,
        });
    }
    async findWhere(criteria) {
        return await this.prisma.dispatchData.findMany({ where: criteria });
    }
    async findOneWhere(criteria) {
        return await this.prisma.dispatchData.findFirst({ where: criteria });
    }
    async create(data) {
        const result = await this.prisma.dispatchData.create({ data });
        await this.invalidateCache(); // Invalidate cache after write operations
        return result;
    }
    async update(id, data) {
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        const result = await this.prisma.dispatchData.update({
            where: { id: numericId },
            data
        });
        await this.invalidateCache();
        return result;
    }
    async delete(id) {
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        try {
            await this.prisma.dispatchData.delete({ where: { id: numericId } });
            await this.invalidateCache();
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    async count(criteria) {
        return await this.prisma.dispatchData.count({ where: criteria });
    }
    async invalidateCache(key) {
        if (key) {
            // Invalidate specific key
            // Implementation depends on cache service
        }
        else {
            // Invalidate all dispatch-related cache entries
            this.cache.invalidateByDataTypes(['dispatch']);
        }
    }
    async getStats() {
        return this.stats;
    }
    /**
     * Private helper methods
     */
    async cachedQuery(cacheKey, queryFn, ttl) {
        this.stats.totalQueries++;
        this.stats.lastAccessed = new Date();
        return await this.cache.cachedQuery(cacheKey, queryFn, ttl);
    }
    buildDateWhereClause(dateRange) {
        if (!dateRange)
            return {};
        const whereClause = {};
        if (dateRange.startDate && dateRange.endDate) {
            whereClause.datum = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            };
        }
        else if (dateRange.startDate) {
            whereClause.datum = {
                gte: dateRange.startDate,
            };
        }
        else if (dateRange.endDate) {
            whereClause.datum = {
                lte: dateRange.endDate,
            };
        }
        return whereClause;
    }
}
exports.DispatchRepositoryImpl = DispatchRepositoryImpl;
