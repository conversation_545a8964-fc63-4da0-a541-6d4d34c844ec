"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoerungenController = void 0;
const stoerungen_repository_1 = require("../repositories/stoerungen.repository");
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const prisma = new client_1.PrismaClient();
const stoerungenRepo = stoerungen_repository_1.StoerungenRepository.getInstance(prisma);
const createStoerungSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Title is required'),
    description: zod_1.z.string().optional(),
    severity: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
    status: zod_1.z.enum(['NEW', 'IN_PROGRESS', 'RESOLVED']).default('NEW'),
    category: zod_1.z.string().optional(),
    affected_system: zod_1.z.string().optional(),
    location: zod_1.z.string().optional(),
    reported_by: zod_1.z.string().optional(),
    assigned_to: zod_1.z.string().optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
});
const updateStoerungSchema = createStoerungSchema.partial();
const addCommentSchema = zod_1.z.object({
    stoerung_id: zod_1.z.number(),
    user_id: zod_1.z.string().optional(),
    comment: zod_1.z.string().min(1, 'Comment is required'),
});
const updateSystemStatusSchema = zod_1.z.object({
    system_name: zod_1.z.string().min(1, 'System name is required'),
    status: zod_1.z.enum(['OK', 'WARNING', 'ERROR']),
    metadata: zod_1.z.any().optional(),
});
class StoerungenController {
    async getStoerungen(req, res) {
        try {
            const { status, severity, category, affected_system, limit, offset } = req.query;
            const options = {
                status: status,
                severity: severity,
                category: category,
                affected_system: affected_system,
                limit: limit ? parseInt(limit) : undefined,
                offset: offset ? parseInt(offset) : undefined,
            };
            // Remove undefined values
            Object.keys(options).forEach(key => {
                if (options[key] === undefined) {
                    delete options[key];
                }
            });
            const stoerungen = await stoerungenRepo.getStoerungen(options);
            res.json({
                success: true,
                data: stoerungen
            });
        }
        catch (error) {
            console.error('Error fetching störungen:', error);
            res.status(500).json({ success: false, error: 'Failed to fetch störungen' });
        }
    }
    async getStoerungById(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Invalid ID' });
            }
            const stoerung = await stoerungenRepo.getStoerungById(id);
            if (!stoerung) {
                return res.status(404).json({ error: 'Störung not found' });
            }
            res.json({
                success: true,
                data: stoerung
            });
        }
        catch (error) {
            console.error('Error fetching störung:', error);
            res.status(500).json({ success: false, error: 'Failed to fetch störung' });
        }
    }
    async createStoerung(req, res) {
        try {
            const validatedData = createStoerungSchema.parse(req.body);
            const stoerung = await stoerungenRepo.createStoerung(validatedData);
            res.status(201).json({
                success: true,
                data: stoerung
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                return res.status(400).json({ error: 'Validation error', details: error.errors });
            }
            console.error('Error creating störung:', error);
            res.status(500).json({ success: false, error: 'Failed to create störung' });
        }
    }
    async updateStoerung(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Invalid ID' });
            }
            const validatedData = updateStoerungSchema.parse(req.body);
            // Calculate MTTR if resolving
            let updateData = { ...validatedData };
            if (validatedData.status === 'RESOLVED') {
                const existing = await stoerungenRepo.getStoerungById(id);
                if (existing && !existing.resolved_at) {
                    const mttr = Math.round((Date.now() - new Date(existing.created_at).getTime()) / (1000 * 60));
                    updateData.mttr_minutes = mttr;
                }
            }
            const stoerung = await stoerungenRepo.updateStoerung(id, updateData);
            res.json({
                success: true,
                data: stoerung
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                return res.status(400).json({ error: 'Validation error', details: error.errors });
            }
            console.error('Error updating störung:', error);
            res.status(500).json({ success: false, error: 'Failed to update störung' });
        }
    }
    async deleteStoerung(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Invalid ID' });
            }
            const success = await stoerungenRepo.deleteStoerung(id);
            if (!success) {
                return res.status(404).json({ error: 'Störung not found' });
            }
            res.status(204).send();
        }
        catch (error) {
            console.error('Error deleting störung:', error);
            res.status(500).json({ success: false, error: 'Failed to delete störung' });
        }
    }
    async addComment(req, res) {
        try {
            const validatedData = addCommentSchema.parse(req.body);
            const comment = await stoerungenRepo.addComment(validatedData);
            res.status(201).json({
                success: true,
                data: comment
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                return res.status(400).json({ error: 'Validation error', details: error.errors });
            }
            console.error('Error adding comment:', error);
            res.status(500).json({ success: false, error: 'Failed to add comment' });
        }
    }
    async getStoerungsStats(req, res) {
        try {
            const stats = await stoerungenRepo.getStoerungsStats();
            res.json({
                success: true,
                data: stats
            });
        }
        catch (error) {
            console.error('Error fetching störungen stats:', error);
            res.status(500).json({ success: false, error: 'Failed to fetch störungen stats' });
        }
    }
    async getActiveStoerungen(req, res) {
        try {
            const active = await stoerungenRepo.getStoerungen({
                status: 'NEW,IN_PROGRESS',
            });
            res.json({
                success: true,
                data: active
            });
        }
        catch (error) {
            console.error('Error fetching active störungen:', error);
            res.status(500).json({ success: false, error: 'Failed to fetch active störungen' });
        }
    }
    async getSystemStatus(req, res) {
        try {
            const statuses = await stoerungenRepo.getSystemStatus();
            res.json({
                success: true,
                data: statuses
            });
        }
        catch (error) {
            console.error('Error fetching system status:', error);
            res.status(500).json({ success: false, error: 'Failed to fetch system status' });
        }
    }
    async updateSystemStatus(req, res) {
        try {
            const validatedData = updateSystemStatusSchema.parse(req.body);
            const status = await stoerungenRepo.updateSystemStatus(validatedData);
            res.json({
                success: true,
                data: status
            });
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                return res.status(400).json({ error: 'Validation error', details: error.errors });
            }
            console.error('Error updating system status:', error);
            res.status(500).json({ success: false, error: 'Failed to update system status' });
        }
    }
}
exports.StoerungenController = StoerungenController;
