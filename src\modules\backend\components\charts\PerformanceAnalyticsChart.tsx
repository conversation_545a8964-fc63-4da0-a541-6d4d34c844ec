import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  Activity, 
  Database,
  Zap,
  BarChart3,
  Download,
  RefreshCw
} from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { SimplePerformanceStatsChart } from './SimplePerformanceStatsChart';
import { PerformanceTrendsChart } from './PerformanceTrendsChart';
import { PerformanceAlertsPanel } from './PerformanceAlertsPanel';
import { CacheAnalyticsChart } from './CacheAnalyticsChart';

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  intentAccuracy: number;
  queryPerformance: {
    stoerungen: { avg: number; count: number; successRate: number };
    dispatch: { avg: number; count: number; successRate: number };
    cutting: { avg: number; count: number; successRate: number };
  };
}

interface PerformanceAlert {
  id: number;
  type: 'warning' | 'error';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  createdAt: string;
  resolved: boolean;
}

export function PerformanceAnalyticsChart() {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'5min' | '1hour' | '24hours' | '7days' | '30days'>('24hours');
  const [refreshing, setRefreshing] = useState(false);

  const fetchPerformanceData = async () => {
    try {
      setRefreshing(true);
      
      // Fetch performance stats
      const statsResponse = await fetch(`/api/performance/stats?timeRange=${timeRange}`);
      if (!statsResponse.ok) throw new Error('Failed to fetch performance stats');
      const statsData = await statsResponse.json();
      setStats(statsData.data);

      // Fetch alerts
      const alertsResponse = await fetch('/api/performance/alerts');
      if (!alertsResponse.ok) throw new Error('Failed to fetch alerts');
      const alertsData = await alertsResponse.json();
      setAlerts(alertsData.data.alerts || []);

      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch performance data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchPerformanceData, 30000);
    return () => clearInterval(interval);
  }, [timeRange]);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const exportData = async () => {
    try {
      const response = await fetch(`/api/performance/export?format=json`);
      if (!response.ok) throw new Error('Export failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-analytics-${timeRange}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  // BentoCard component similar to WorkflowPerformanceChart
  const BentoCard = ({
    title,
    value,
    icon,
    accent = 'primary',
    trend,
  }: {
    title: string;
    value: React.ReactNode;
    icon: React.ReactNode;
    accent?: 'primary' | 'green' | 'red' | 'blue' | 'purple';
    trend?: 'up' | 'down' | 'stable';
  }) => {
    const accentClasses =
      accent === 'green'
        ? {
            grid: 'bg-[linear-gradient(to_right,#16a34a30_1px,transparent_1px),linear-gradient(to_bottom,#16a34a30_1px,transparent_1px)]',
            icon: 'text-green-600',
            chipBg: 'bg-emerald-500/10',
            chipHover: 'group-hover:bg-emerald-500/20',
            glow: 'from-emerald-500 to-emerald-400/30',
            valueText: 'text-emerald-600',
          }
        : accent === 'red'
        ? {
            grid: 'bg-[linear-gradient(to_right,#ef444430_1px,transparent_1px),linear-gradient(to_bottom,#ef444430_1px,transparent_1px)]',
            icon: 'text-red-600',
            chipBg: 'bg-red-500/10',
            chipHover: 'group-hover:bg-red-500/20',
            glow: 'from-red-500 to-red-400/30',
            valueText: 'text-red-600',
          }
        : accent === 'blue'
        ? {
            grid: 'bg-[linear-gradient(to_right,#3b82f630_1px,transparent_1px),linear-gradient(to_bottom,#3b82f630_1px,transparent_1px)]',
            icon: 'text-blue-600',
            chipBg: 'bg-blue-500/10',
            chipHover: 'group-hover:bg-blue-500/20',
            glow: 'from-blue-500 to-blue-400/30',
            valueText: 'text-blue-600',
          }
        : accent === 'purple'
        ? {
            grid: 'bg-[linear-gradient(to_right,#9333ea30_1px,transparent_1px),linear-gradient(to_bottom,#9333ea30_1px,transparent_1px)]',
            icon: 'text-purple-600',
            chipBg: 'bg-purple-500/10',
            chipHover: 'group-hover:bg-purple-500/20',
            glow: 'from-purple-500 to-purple-400/30',
            valueText: 'text-purple-600',
          }
        : {
            grid: 'bg-[linear-gradient(to_right,#6b728030_1px,transparent_1px),linear-gradient(to_bottom,#6b728030_1px,transparent_1px)]',
            icon: 'text-primary',
            chipBg: 'bg-primary/10',
            chipHover: 'group-hover:bg-primary/20',
            glow: 'from-primary to-primary/30',
            valueText: '',
          };

    return (
      <motion.div
        variants={{
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0, transition: { type: 'spring', damping: 25 } },
        }}
        className={cn(
          'group border-primary/10 bg-background hover:border-primary/30 relative flex h-full flex-col justify-between overflow-hidden rounded-xl border px-6 pt-6 pb-6 shadow-md transition-all duration-500'
        )}
      >
        {/* Background grid */}
        <div
          className={cn(
            'absolute top-0 -right-1/2 z-0 size-full cursor-pointer [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]',
            accentClasses.grid
          )}
        />
        {/* Background icon */}
        <div
          className={cn(
            'absolute right-2 bottom-2 scale-[5] transition-all duration-700 group-hover:scale-[5.05] opacity-10',
            accentClasses.icon
          )}
          aria-hidden
        >
          {icon}
        </div>

        <div className="relative z-10 flex h-full flex-col justify-between">
          <div>
            <div className="flex items-center justify-between mb-4">
              <div
                className={cn(
                  'text-primary shadow-primary/10 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500',
                  accentClasses.chipBg,
                  accentClasses.chipHover
                )}
              >
                {icon}
              </div>
              {trend && (
                <div className={cn(
                  'flex items-center text-xs',
                  trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'
                )}>
                  <TrendingUp className={cn(
                    'h-3 w-3 mr-1',
                    trend === 'down' && 'rotate-180'
                  )} />
                  {trend}
                </div>
              )}
            </div>
            <h3 className="mb-2 text-sm font-medium text-muted-foreground">{title}</h3>
            <div className={cn('text-2xl font-bold', accentClasses.valueText)}>{value}</div>
          </div>
        </div>
        {/* Bottom glow */}
        <div
          className={cn(
            'absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-xl transition-all duration-500 group-hover:blur-lg opacity-40',
            accentClasses.glow
          )}
        />
      </motion.div>
    );
  };

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchPerformanceData} className="mt-4">
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border rounded-md bg-background"
          >
            <option value="5min">Letzte 5 Minuten</option>
            <option value="1hour">Letzte Stunde</option>
            <option value="24hours">Letzte 24 Stunden</option>
            <option value="7days">Letzte 7 Tage</option>
            <option value="30days">Letzte 30 Tage</option>
          </select>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchPerformanceData}
            disabled={refreshing}
          >
            <RefreshCw className={cn('h-4 w-4 mr-2', refreshing && 'animate-spin')} />
            Aktualisieren
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={exportData}
          >
            <Download className="h-4 w-4 mr-2" />
            Exportieren
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <motion.div
        className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5"
        variants={{ hidden: {}, visible: { transition: { staggerChildren: 0.12, delayChildren: 0.1 } } }}
        initial="hidden"
        animate="visible"
      >
        <BentoCard
          title="Gesamt Anfragen"
          value={stats?.totalRequests || 0}
          icon={<Activity className="size-6" />}
          accent="blue"
        />
        <BentoCard
          title="Ø Antwortzeit"
          value={formatDuration(stats?.averageResponseTime || 0)}
          icon={<Clock className="size-6" />}
          accent="purple"
        />
        <BentoCard
          title="Erfolgsrate"
          value={formatPercentage(stats?.successRate || 0)}
          icon={<CheckCircle className="size-6" />}
          accent="green"
        />
        <BentoCard
          title="Cache Trefferrate"
          value={formatPercentage(stats?.cacheHitRate || 0)}
          icon={<Zap className="size-6" />}
          accent="blue"
        />
        <BentoCard
          title="KI Genauigkeit"
          value={formatPercentage(stats?.intentAccuracy || 0)}
          icon={<BarChart3 className="size-6" />}
          accent="purple"
        />
      </motion.div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Trends */}
        <PerformanceTrendsChart timeRange={timeRange} />

        {/* Query Performance */}
        <SimplePerformanceStatsChart stats={stats} />
      </div>

      {/* Alerts and Cache Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Alerts */}
        <PerformanceAlertsPanel alerts={alerts} onRefresh={fetchPerformanceData} />

        {/* Cache Analytics */}
        <CacheAnalyticsChart />
      </div>

      {/* Query Performance Details */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>Query Performance Details</CardTitle>
            <CardDescription>Detaillierte Performance-Metriken nach Datenquelle</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Datenquelle</th>
                    <th className="text-left p-2">Anzahl Queries</th>
                    <th className="text-left p-2">Ø Antwortzeit</th>
                    <th className="text-left p-2">Erfolgsrate</th>
                    <th className="text-left p-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(stats.queryPerformance).map(([source, metrics]) => (
                    <tr key={source} className="border-b">
                      <td className="p-2 font-medium capitalize">
                        {source === 'stoerungen' ? 'Störungen' : 
                         source === 'dispatch' ? 'Versand' : 
                         source === 'cutting' ? 'Ablängerei' : source}
                      </td>
                      <td className="p-2">{metrics.count}</td>
                      <td className="p-2">{formatDuration(metrics.avg)}</td>
                      <td className="p-2">
                        <Badge
                          variant={metrics.successRate >= 0.9 ? "secondary" : metrics.successRate >= 0.7 ? "outline" : "destructive"}
                          className={metrics.successRate >= 0.9 ? "bg-green-100 text-green-800" : ""}
                        >
                          {formatPercentage(metrics.successRate)}
                        </Badge>
                      </td>
                      <td className="p-2">
                        <div className="flex items-center">
                          {metrics.successRate >= 0.9 ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : metrics.successRate >= 0.7 ? (
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          ) : (
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}