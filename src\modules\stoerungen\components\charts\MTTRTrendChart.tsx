import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { DateRange } from 'react-day-picker';
import { TrendingUp, Clock, AlertTriangle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { Stoerung } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { motion } from 'framer-motion';

interface MTTRProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

interface MTTRTrendChartProps extends MTTRProps { }

interface MTTRKpiCardsProps extends MTTRProps { }

// Custom hook to share MTTR data logic between components
const useMTTRData = (dateRange?: DateRange, refreshKey: number = 0) => {
  console.log('🚀 MTTR Hook - Component mounted/updated', {
    dateRange: dateRange ? {
      from: dateRange.from?.toLocaleDateString(),
      to: dateRange.to?.toLocaleDateString()
    } : null,
    refreshKey
  });

  const [stoerungen, setStoerungen] = useState<Stoerung[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoerungen = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await stoerungenService.getStoerungen();
        console.log('🔍 MTTR Hook - Data received:', {
          total: data?.length || 0,
          isArray: Array.isArray(data),
          sample: data?.[0] ? {
            id: data[0].id,
            title: data[0].title,
            status: data[0].status,
            mttr_minutes: data[0].mttr_minutes,
            resolved_at: data[0].resolved_at
          } : null
        });

        if (Array.isArray(data)) {
          const resolvedCount = data.filter(s => s.status === 'RESOLVED' && s.mttr_minutes).length;
          console.log('📊 MTTR Hook - Resolved with MTTR:', resolvedCount, 'out of', data.length);

          // Show sample resolved störungen
          const resolvedSample = data.filter(s => s.status === 'RESOLVED' && s.mttr_minutes).slice(0, 3);
          console.log('📋 MTTR Hook - Sample resolved störungen:', resolvedSample.map(s => ({
            title: s.title,
            mttr_minutes: s.mttr_minutes,
            resolved_at: s.resolved_at
          })));
        }

        setStoerungen(data);
      } catch (err) {
        console.error('Error fetching störungen for MTTR:', err);
        setError('Fehler beim Laden der MTTR-Daten');

        // Fallback: Empty array - the database should have data now
        setStoerungen([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStoerungen();
  }, [refreshKey]);

  // Filter resolved störungen with MTTR data
  const resolvedStoerungen = React.useMemo(() => {
    if (!Array.isArray(stoerungen)) {
      console.error('MTTR Hook - störungen is not an array:', stoerungen);
      return [];
    }

    let filtered = stoerungen.filter(s => {
      const isResolved = s.status === 'RESOLVED';
      const hasMTTR = s.mttr_minutes != null && s.mttr_minutes > 0;
      const hasResolvedAt = s.resolved_at != null;
      return isResolved && hasMTTR && hasResolvedAt;
    });

    console.log('MTTR Hook - Filtered resolved störungen:', filtered.length, 'from', stoerungen.length);

    // Filter by date range if provided
    if (dateRange?.from && dateRange?.to) {
      const beforeDateFilter = filtered.length;
      console.log('🗓️ MTTR Hook - Date range filter:', {
        from: dateRange.from.toLocaleDateString(),
        to: dateRange.to.toLocaleDateString(),
        beforeFilter: beforeDateFilter
      });

      const dateFiltered = filtered.filter(stoerung => {
        const resolvedDate = new Date(stoerung.resolved_at!);
        const isInRange = resolvedDate >= dateRange.from! && resolvedDate <= dateRange.to!;
        if (!isInRange && beforeDateFilter < 10) {
          console.log('📅 Filtered out:', stoerung.title, 'resolved:', resolvedDate.toLocaleDateString());
        }
        return isInRange;
      });

      // If date filtering removes all data, show all available data instead
      if (dateFiltered.length === 0 && beforeDateFilter > 0) {
        console.log('⚠️ MTTR Hook - Date filter removed all data, showing all available data');
        // Keep original filtered data
      } else {
        filtered = dateFiltered;
      }

      console.log('MTTR Hook - Date filter result:', filtered.length, 'from', beforeDateFilter);
    }

    const sorted = filtered.sort((a, b) =>
      new Date(a.resolved_at!).getTime() - new Date(b.resolved_at!).getTime()
    );

    if (sorted.length > 0) {
      console.log('MTTR Hook - Final data range:',
        new Date(sorted[0].resolved_at!).toLocaleDateString(),
        'to',
        new Date(sorted[sorted.length - 1].resolved_at!).toLocaleDateString()
      );
    }

    return sorted;
  }, [stoerungen, dateRange]);

  // Process data for trend chart
  const trendData = React.useMemo(() => {
    if (resolvedStoerungen.length === 0) {
      return [];
    }

    // Group by day and calculate average MTTR
    const dailyData: Record<string, { total: number; count: number; mttr_sum: number; critical: number }> = {};

    resolvedStoerungen.forEach(stoerung => {
      const resolvedDate = new Date(stoerung.resolved_at!);
      const dateKey = resolvedDate.toISOString().split('T')[0];

      if (!dailyData[dateKey]) {
        dailyData[dateKey] = { total: 0, count: 0, mttr_sum: 0, critical: 0 };
      }

      dailyData[dateKey].total++;
      dailyData[dateKey].count++;
      dailyData[dateKey].mttr_sum += stoerung.mttr_minutes!;

      if (stoerung.severity === 'CRITICAL') {
        dailyData[dateKey].critical++;
      }
    });

    const processed = Object.entries(dailyData)
      .map(([date, data]) => ({
        date,
        avg_mttr_hours: Math.round((data.mttr_sum / data.count) / 60 * 10) / 10,
        avg_mttr_minutes: Math.round(data.mttr_sum / data.count),
        störungen: data.total,
        kritische_störungen: data.critical,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    console.log('MTTR Hook - Generated trend data for', processed.length, 'days');

    return processed;
  }, [resolvedStoerungen]);

  // Calculate overall metrics
  const metrics = React.useMemo(() => {
    if (resolvedStoerungen.length === 0) {
      return {
        avgMTTR: 0,
        totalResolved: 0,
        fastestResolution: 0,
        slowestResolution: 0,
        improvement: 0,
      };
    }

    const mttrValues = resolvedStoerungen.map(s => s.mttr_minutes!);
    const avgMTTR = mttrValues.reduce((sum, val) => sum + val, 0) / mttrValues.length;
    const fastestResolution = Math.min(...mttrValues);
    const slowestResolution = Math.max(...mttrValues);

    // Calculate trend (improvement over time)
    const firstHalf = mttrValues.slice(0, Math.floor(mttrValues.length / 2));
    const secondHalf = mttrValues.slice(Math.floor(mttrValues.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    const improvement = ((firstHalfAvg - secondHalfAvg) / firstHalfAvg) * 100;

    return {
      avgMTTR: Math.round(avgMTTR),
      totalResolved: resolvedStoerungen.length,
      fastestResolution,
      slowestResolution,
      improvement: Math.round(improvement),
    };
  }, [resolvedStoerungen]);

  return {
    trendData,
    metrics,
    loading,
    error,
    resolvedStoerungen
  };
};

// MTTR KPI Cards Component
export const MTTRKpiCards: React.FC<MTTRKpiCardsProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const { metrics, loading, error } = useMTTRData(dateRange, refreshKey);

  const formatMTTR = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins}m`;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  if (loading) {
    return (
      <>
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="p-4 min-w-0">
            <div className="animate-pulse">
              <div className="h-4  rounded mb-2"></div>
              <div className="h-6  rounded"></div>
            </div>
          </Card>
        ))}
      </>
    );
  }

  return (
    <>
      <div className="group border-[#ff7a05]/30 hover:border-[#ff7a05]/50 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border px-4 py-4 shadow-md transition-all duration-500 bg-yellow-50 min-w-0">
        <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#ff7a0520_1px,transparent_1px),linear-gradient(to_bottom,#ff7a0520_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

        <div className="text-[#ff7a05]/10 group-hover:text-[#ff7a05]/20 absolute right-1 bottom-1 scale-[2] transition-all duration-700 group-hover:scale-[2.2]">
          <Clock className="h-8 w-8" />
        </div>

        <div className="relative z-10 flex h-full flex-col justify-between">
          <div>
            <div className="bg-[#ff7a05]/10 text-[#ff7a05] shadow-[#ff7a05]/10 group-hover:bg-[#ff7a05]/20 group-hover:shadow-[#ff7a05]/20 mb-2 flex h-8 w-8 items-center justify-center rounded-full shadow transition-all duration-500">
              <Clock className="h-4 w-4" />
            </div>
            <p className="text-xs font-medium opacity-80 text-yellow-600 mb-1">Schnellste</p>
            <p className="text-lg font-bold text-yellow-600">
              {formatMTTR(metrics.fastestResolution)}
            </p>
          </div>
        </div>
        <div className="from-[#ff7a05] to-[#ff7a05]/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
      </div>

      <div className="group border-[#ff7a05]/30 hover:border-[#ff7a05]/50 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border px-4 py-4 shadow-md transition-all duration-500 bg-purple-50 min-w-0">
        <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#ff7a0520_1px,transparent_1px),linear-gradient(to_bottom,#ff7a0520_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

        <div className="text-[#ff7a05]/10 group-hover:text-[#ff7a05]/20 absolute right-1 bottom-1 scale-[2] transition-all duration-700 group-hover:scale-[2.2]">
          <TrendingUp className={`h-8 w-8 ${metrics.improvement > 0 ? 'text-green-500' : 'text-red-500'}`} />
        </div>

        <div className="relative z-10 flex h-full flex-col justify-between">
          <div>
            <div className="bg-[#ff7a05]/10 text-[#ff7a05] shadow-[#ff7a05]/10 group-hover:bg-[#ff7a05]/20 group-hover:shadow-[#ff7a05]/20 mb-2 flex h-8 w-8 items-center justify-center rounded-full shadow transition-all duration-500">
              <TrendingUp className="h-4 w-4" />
            </div>
            <p className="text-xs font-medium opacity-80 text-purple-600 mb-1">Verbesserung</p>
            <p className={`text-lg font-bold ${metrics.improvement > 0 ? 'text-green-700' : 'text-red-700'}`}>
              {metrics.improvement > 0 ? '+' : ''}{metrics.improvement}%
            </p>
          </div>
        </div>
        <div className="from-[#ff7a05] to-[#ff7a05]/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
      </div>
    </>
  );
});

MTTRKpiCards.displayName = 'MTTRKpiCards';

// MTTR Trend Chart Component
export const MTTRTrendChart: React.FC<MTTRTrendChartProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const isMobile = useIsMobile();
  const { trendData, metrics, loading, error } = useMTTRData(dateRange, refreshKey);

  const formatMTTR = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins}m`;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            MTTR Trend-Analyse
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[615px] w-[915px] border-[#ff7a05]/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          MTTR Trend-Analyse
        </CardTitle>
        <div className="text-sm text-black">
          Durchschnittliche Reparaturzeit über Zeit
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">{error}</p>
            <p className="text-xs text-yellow-600 mt-1">Zeige Beispieldaten zur Demonstration</p>
          </div>
        )}

        {/* MTTR Trend Chart */}
        {trendData.length === 0 ? (
          <div className="text-center py-8 text-black">
            <AlertTriangle className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Keine MTTR-Daten im ausgewählten Zeitraum verfügbar</p>
            <p className="text-xs mt-1">Nur gelöste Störungen mit Zeiterfassung werden angezeigt</p>
          </div>
        ) : (
          <div className="px-2 pt-4 sm:px-6 sm:pt-6">
            <ChartContainer
              config={{
                avg_mttr_hours: {
                  label: "MTTR (Stunden)",
                  color: "var(--chart-1)",
                },
                störungen: {
                  label: "Störungen",
                  color: "var(--chart-2)",
                },
              }}
              className="aspect-auto h-[400px] w-full"
            >
              <AreaChart data={trendData}>
                <defs>
                  <linearGradient id="fillMTTR" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--color-avg_mttr_hours)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--color-avg_mttr_hours)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("de-DE", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                />
                <ChartTooltip
                  cursor={false}
                  defaultIndex={isMobile ? -1 : 10}
                  content={
                    <ChartTooltipContent
                      labelFormatter={(value) => {
                        return new Date(value).toLocaleDateString("de-DE", {
                          weekday: "short",
                          month: "short",
                          day: "numeric",
                        });
                      }}
                      formatter={(value, name) => {
                        if (name === 'avg_mttr_hours') {
                          return [`${value}h`, 'Ø MTTR'];
                        }
                        return [value, name];
                      }}
                      indicator="dot"
                    />
                  }
                />
                <Area
                  dataKey="avg_mttr_hours"
                  type="natural"
                  fill="url(#fillMTTR)"
                  stroke="var(--color-avg_mttr_hours)"
                  strokeWidth={2}
                />
              </AreaChart>
            </ChartContainer>
          </div>
        )}

      </CardContent>

      {/* Performance Summary in Footer */}
      {trendData.length > 0 && (
        <CardFooter className="pt-0">
          <div className="pt-2">
            <h4 className="text-sm font-medium text-black mb-2">Performance Zusammenfassung</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-black">Schnellste Lösung:</span>
                <span className="ml-2 font-medium text-green-600">
                  {formatMTTR(metrics.fastestResolution)}
                </span>
              </div>
              <div>
                <span className="text-black">Langsamste Lösung:</span>
                <span className="ml-2 font-medium text-red-600">
                  {formatMTTR(metrics.slowestResolution)}
                </span>
              </div>
              <div>
                <span className="text-black">Trend:</span>
                <span className={`ml-2 font-medium ${metrics.improvement > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                  {metrics.improvement > 0 ? 'Verbesserung' : 'Verschlechterung'}
                </span>
              </div>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
});

MTTRTrendChart.displayName = 'MTTRTrendChart';