import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, Phone, ChevronLeft, ChevronRight, Settings, RefreshCw } from 'lucide-react';

interface BereitschaftsPerson {
  id: number;
  name: string;
  telefon: string;
  email: string;
  abteilung: string;
}

interface BereitschaftsWoche {
  id: number;
  person: BereitschaftsPerson;
  von: string;
  bis: string;
  wochenStart: string;
  aktiv: boolean;
  notiz?: string;
  hatAusnahme?: boolean;
}

interface BereitschaftsPlanProps {
  refreshKey?: number;
  onConfigureClick?: () => void;
}

// Beispieldaten für die Anzeige
const beispielPersonen: BereitschaftsPerson[] = [
  {
    id: 1,
    name: '<PERSON>',
    telefon: '+49 ************',
    email: '<PERSON><PERSON>@lapp.com',
    abteilung: 'L<PERSON>'
  },
  {
    id: 2,
    name: '<PERSON>',
    telefon: '+49 ************',
    email: '<PERSON>.<EMAIL>',
    abteilung: 'LSI'
  },
  {
    id: 3,
    name: 'Allen Frederick',
    telefon: '+49 ************',
    email: '<EMAIL>',
    abteilung: 'LSI'
  }
];

export const BereitschaftsPlan: React.FC<BereitschaftsPlanProps> = ({
  refreshKey,
  onConfigureClick
}) => {
  const [loading, setLoading] = useState(false);
  const [aktuelleBereitschaft, setAktuelleBereitschaft] = useState<BereitschaftsWoche | null>(null);
  const [bereitschaftsWochen, setBereitschaftsWochen] = useState<BereitschaftsWoche[]>([]);

  useEffect(() => {
    // Simuliere das Laden von Daten
    setLoading(true);

    // Erstelle Beispiel-Bereitschaftswochen
    const heute = new Date();
    const beispielWochen: BereitschaftsWoche[] = beispielPersonen.map((person, index) => {
      const startDate = new Date(heute);
      startDate.setDate(heute.getDate() + (index * 7));
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7);

      return {
        id: index + 1,
        person,
        von: startDate.toISOString(),
        bis: endDate.toISOString(),
        wochenStart: startDate.toISOString(),
        aktiv: true,
        hatAusnahme: false
      };
    });

    setBereitschaftsWochen(beispielWochen);

    // Setze die erste Woche als aktuelle Bereitschaft
    if (beispielWochen.length > 0) {
      setAktuelleBereitschaft(beispielWochen[0]);
    }

    setTimeout(() => setLoading(false), 500);
  }, [refreshKey]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', {
      weekday: 'long',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getKalenderwoche = (dateString: string) => {
    const date = new Date(dateString);
    const startOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - startOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Bereitschaftsplan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="ml-2">Bereitschaftsplan wird geladen...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Bereitschaftsplan
          </CardTitle>
          <div className="flex items-center gap-2">
            {onConfigureClick && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('🔧 Konfiguration Button geklickt');
                  onConfigureClick();
                }}
                className="text-blue-600 border-blue-300 hover:bg-blue-50"
                title="Bereitschaftsplan konfigurieren"
              >
                <Settings className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => { }}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => { }}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Aktuelle Bereitschaft Highlight */}
        {aktuelleBereitschaft && (
          <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-green-600" />
              <span className="font-semibold text-green-800">Aktuelle Bereitschaft</span>
              <Badge variant="default" className="bg-green-600">
                AKTIV
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-lg text-gray-900">
                  {aktuelleBereitschaft.person.name}
                </h4>
                <p className="text-sm text-gray-600 mb-2">
                  {aktuelleBereitschaft.person.abteilung}
                </p>
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <a
                    href={`tel:${aktuelleBereitschaft.person.telefon}`}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {aktuelleBereitschaft.person.telefon}
                  </a>
                </div>
              </div>

              <div className="text-right">
                <div className="text-sm text-gray-600">
                  Bereitschaft bis:
                </div>
                <div className="font-semibold text-gray-900">
                  {formatDate(aktuelleBereitschaft.bis)}
                </div>
                <div className="text-sm text-gray-600">
                  um 08:00 Uhr
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Wochenplan */}
        <div className="space-y-2">
          <h4 className="font-semibold text-gray-900 flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Wochenplan
          </h4>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            {bereitschaftsWochen.map((woche, index) => (
              <div
                key={woche.id}
                className={`p-3 rounded-lg border transition-all ${index === 0
                  ? 'bg-green-50 border-green-300 shadow-sm'
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">
                        {woche.person.name}
                      </span>
                      {index === 0 && (
                        <Badge variant="default" className="bg-green-600 text-xs">
                          AKTIV
                        </Badge>
                      )}
                      {woche.hatAusnahme && (
                        <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
                          AUSNAHME
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      {woche.person.abteilung}
                    </div>
                    {woche.notiz && (
                      <div className="text-xs text-gray-500 mt-1">
                        {woche.notiz}
                      </div>
                    )}
                  </div>

                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      KW {getKalenderwoche(woche.wochenStart)}
                    </div>
                    <div className="text-xs text-gray-600">
                      {new Date(woche.von).toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' })} - {new Date(woche.bis).toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' })}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Legende */}
        <div className="pt-2 border-t border-gray-200">
          <div className="text-xs text-gray-600">
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                <span>Aktuelle Bereitschaft</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded"></div>
                <span>Diese Woche</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
                <span>Ausnahme</span>
              </div>
            </div>
            <div className="mt-1 text-gray-500">
              Wechsel erfolgt jeden Freitag um 08:00 Uhr
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};