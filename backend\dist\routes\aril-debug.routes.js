"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Debug-Route für ARiL-Daten
router.get('/debug', async (req, res) => {
    try {
        console.log('🔍 ARiL Debug-Route aufgerufen');
        // Teste direkte Prisma-Abfrage mit korrektem Modell-Namen (ARiL mit großem R)
        const result = await prisma.aRiL.findMany({
            take: 5, // Nur erste 5 Datensätze für Debug
            orderBy: {
                Datum: 'desc',
            },
        });
        console.log('✅ ARiL Daten aus Datenbank:', result);
        res.json({
            success: true,
            count: result.length,
            data: result,
            fields: result.length > 0 ? Object.keys(result[0]) : [],
        });
    }
    catch (error) {
        console.error('❌ ARiL Debug Fehler:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler',
            details: error
        });
    }
});
exports.default = router;
