"use client"

import React, { useState, useRef, useEffect } from "react"
import { DateRange } from "react-day-picker"
import { CalendarIcon, Check, X, RefreshCw } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { de } from "date-fns/locale"

interface DateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange | undefined) => void
  className?: string
  label?: string
}

export function DateRangePicker({ 
  value, 
  onChange, 
  className,
  label = "Datumsbereich"
}: DateRangePickerProps) {
  // State für Popup und temporäre Auswahl
  const [isOpen, setIsOpen] = useState(false)
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>(value)
  const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0, openUpwards: false })
  const buttonRef = useRef<HTMLButtonElement>(null)
  const popupRef = useRef<HTMLDivElement>(null)
  
  // Bestätige die Auswahl
  const handleConfirm = () => {
    // Wenn ein Bereich ausgewählt wurde, setze das Enddatum auf das Ende des Tages (23:59:59)
    if (selectedRange?.from && selectedRange?.to) {
      const adjustedRange = {
        from: selectedRange.from,
        to: new Date(
          selectedRange.to.getFullYear(),
          selectedRange.to.getMonth(),
          selectedRange.to.getDate(),
          23, 59, 59, 999 // Ende des Tages (23:59:59.999)
        )
      }
      onChange?.(adjustedRange)
    } else {
      onChange?.(selectedRange)
    }
    setIsOpen(false)
  }
  
  // Zurücksetzen der aktuellen Auswahl im DatePicker
  const handleReset = () => {
    // Nur die Auswahl zurücksetzen, ohne das Popup zu schließen
    setSelectedRange(undefined)
  }
  
  // Abbrechen und Popup schließen
  const handleCancel = () => {
    // Setze die temporäre Auswahl zurück auf den aktuellen Wert
    setSelectedRange(value)
    setIsOpen(false)
  }
  
  // Behandle Klicks außerhalb des Popups
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current && 
        !popupRef.current.contains(event.target as Node) &&
        buttonRef.current && 
        !buttonRef.current.contains(event.target as Node)
      ) {
        // Klick außerhalb des Popups und des Buttons
        handleCancel();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);
  
  // Popup öffnen/schließen
  const togglePopup = () => {
    if (!isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const viewportWidth = window.innerWidth
      const spaceBelow = viewportHeight - rect.bottom
      const spaceNeeded = 380 // Ungefähre Höhe des Popups
      const popupWidth = 600 // Breite des Popups aus dem Style
      
      // Prüfe, ob genug Platz unterhalb des Buttons ist
      const openUpwards = spaceBelow < spaceNeeded && rect.top > spaceNeeded
      
      // Berechne die horizontale Position, damit das Popup immer im sichtbaren Bereich bleibt
      let leftPosition = rect.left
      
      // Wenn das Popup rechts aus dem Viewport herausragen würde, passe die Position an
      if (leftPosition + popupWidth > viewportWidth) {
        // Verschiebe das Popup nach links, damit es vollständig sichtbar ist
        leftPosition = Math.max(0, viewportWidth - popupWidth - 20) // 20px Abstand zum rechten Rand
      }
      
      setPopupPosition({
        top: openUpwards ? rect.top - spaceNeeded : rect.bottom + 5,
        left: leftPosition,
        openUpwards
      })
    }
    setIsOpen(!isOpen)
  }
  
  // Formatiere den aktuellen Zeitraum für die Anzeige
  const formatCurrentRange = () => {
    if (value?.from && value?.to) {
      return `${format(value.from, "dd.MM.yyyy")} - ${format(value.to, "dd.MM.yyyy")}`
    }
    return "Zeitraum wählen..."
  }

  return (
    <div className={cn("relative", className)}>
      {/* Kompakter Button mit Zeitraum-Anzeige */}
      <button
        ref={buttonRef}
        type="button"
        onClick={togglePopup}
        className={cn(
          "flex h-auto min-h-9 w-full items-center justify-between border rounded-md shadow-md p-4 border-[#ff7a05] bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
      >
        <CalendarIcon className="mr-4 h-5 w-5 flex-shrink-0" />
        <div className="flex-1 overflow-hidden">
          <div className="text-base font-medium truncate">{label}</div>
          <div className="text-sm text-muted-foreground mt-1 truncate">{formatCurrentRange()}</div>
        </div>
      </button>

      {/* Popup Calendar */}
      {isOpen && (
        <div 
          ref={popupRef}
          className="fixed z-50 bg-gray-100 border rounded-md shadow-md p-4 border-[#ff7a05]"
          style={{ 
            minWidth: '500px',
            top: `${popupPosition.top}px`,
            left: `${popupPosition.left}px`,
            maxHeight: '500px',
            overflow: 'auto'
          }}
        >
          <div className="space-y-4">
            <h3 className="font-medium text-lg mb-2">Zeitraum auswählen</h3>
            
            <Calendar
              mode="range"
              defaultMonth={selectedRange?.from}
              selected={selectedRange}
              onSelect={setSelectedRange}
              numberOfMonths={2}
              locale={de}
              disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
              className="rounded-md bg-gray-100"
              // Standard Shadcn UI Styling
              classNames={{
                day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground",
                day_today: "bg-accent text-accent-foreground",
                day_range_middle: "bg-accent text-accent-foreground",
                day_range_start: "bg-primary text-primary-foreground",
                day_range_end: "bg-primary text-primary-foreground"
              }}
            />
            
            {/* Platzhalter - wird oben mit Reset-Button zusammen angezeigt */}
            
            {/* Neue Auswahl */}
            <div className="mt-2">
              {selectedRange?.from && selectedRange?.to && (
                <div className="text-sm bg-muted p-2 rounded-md border">
                  <strong>Neue Auswahl:</strong> {format(selectedRange.from, "dd.MM.yyyy")} - {format(selectedRange.to, "dd.MM.yyyy")}
                </div>
              )}
            </div>
            
            {/* Alle Buttons in einer Reihe */}
            <div className="flex gap-2 mt-4">
              <button 
                onClick={handleReset}
                className="flex-1 bg-yellow-500 text-primary-foreground shadow-xs hover:bg-yellow-600 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all h-9 px-4 py-2"
                title="Datumsbereich zurücksetzen"
                type="button"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Zurücksetzen
              </button>
              
              <button 
                onClick={handleConfirm}
                className="flex-1 bg-green-500 text-primary-foreground shadow-xs hover:bg-green-600 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all h-9 px-4 py-2 disabled:pointer-events-none disabled:opacity-50"
                disabled={!selectedRange?.from || !selectedRange?.to}
                type="button"
              >
                <Check className="mr-2 h-4 w-4" />
                Bestätigen
              </button>
              
              <button 
                onClick={handleCancel}
                className="flex-1 bg-red-400 text-primary-foreground shadow-xs hover:bg-red-500 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all h-9 px-4 py-2"
                type="button"
              >
                <X className="mr-2 h-4 w-4" />
                Abbrechen
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 