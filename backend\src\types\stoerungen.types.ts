export interface StoerungCreateData {
  title: string;
  description?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'NEW' | 'IN_PROGRESS' | 'RESOLVED';
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  tags?: string[];
}

export interface StoerungUpdateData {
  title?: string;
  description?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status?: 'NEW' | 'IN_PROGRESS' | 'RESOLVED';
  category?: string;
  affected_system?: string;
  location?: string;
  assigned_to?: string;
  resolved_at?: Date;
  mttr_minutes?: number;
  tags?: string[];
}

export interface StoerungWithComments {
  id: number;
  title: string;
  description?: string;
  severity: string;
  status: string;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  created_at: Date;
  updated_at: Date;
  resolved_at?: Date;
  mttr_minutes?: number;
  tags?: string[];
  comments: StoerungComment[];
}

export interface StoerungComment {
  id: number;
  stoerung_id: number;
  user_id?: string;
  comment: string;
  created_at: Date;
}

export interface StoerungCommentCreateData {
  stoerung_id: number;
  user_id?: string;
  comment: string;
}

export interface StoerungsStats {
  total: number;
  active: number;
  resolved: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  avg_mttr_minutes: number;
  resolution_rate_24h: number;
}

export interface SystemStatusData {
  id: number;
  system_name: string;
  status: 'OK' | 'WARNING' | 'ERROR';
  last_check: Date;
  metadata?: any;
  created_at: Date;
  updated_at: Date;
}

export interface SystemStatusUpdateData {
  system_name: string;
  status: 'OK' | 'WARNING' | 'ERROR';
  metadata?: any;
}