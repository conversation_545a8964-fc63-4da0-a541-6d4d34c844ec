import { Home, Database, Activity, Workflow } from "lucide-react";
import { NavigationConfig } from "./types";

export const backendNavigationConfig: NavigationConfig = {
    menu: [
        {
            title: "Home",
            icon: <Home className="size-5 shrink-0" />,
            to: "/"
        },

        {
            title: "System",
            description: "Systeminformationen",
            icon: <Activity className="size-5 shrink-0" />,
            to: "/modules/backend/system",
        },
        {
            title: "Workflows",
            description: "Workflow zur Datenaufbereitung",
            icon: <Workflow className="size-5 shrink-0" />,
            to: "/modules/backend/workflows",
        },
    ]
};