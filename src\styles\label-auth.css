/* Label Authentication Styles */
/* Monospace/typewriter font styles for label printing aesthetic */

:root {
  /* Label color palette */
  --label-white: #ffffff;
  --label-black: #000000;
  --label-gray-light: rgba(255, 255, 255, 0.9);
  --label-gray-medium: rgba(255, 255, 255, 0.7);
  --label-gray-dark: rgba(0, 0, 0, 0.8);
  --label-shadow: rgba(0, 0, 0, 0.3);

  /* Typography */
  --label-font-primary: 'Courier New', 'Monaco', 'Menlo', 'Consolas', monospace;
  --label-font-secondary: 'Lucida Console', 'Monaco', monospace;

  /* Spacing */
  --label-padding-sm: 0.5rem;
  --label-padding-md: 1rem;
  --label-padding-lg: 1.5rem;
  --label-margin-sm: 0.25rem;
  --label-margin-md: 0.5rem;
  --label-margin-lg: 1rem;

  /* Border and effects */
  --label-border-width: 1px;
  --label-border-radius: 2px;
  --label-shadow-text: 1px 1px 2px var(--label-shadow);
  --label-shadow-box: 0 2px 4px var(--label-shadow);
}

/* Base label typography */
.label-typography {
  font-family: var(--label-font-primary);
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0.02em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* High-contrast text for readability */
.label-text-primary {
  color: var(--label-black);
  text-shadow: var(--label-shadow-text);
  font-weight: 600;
}

.label-text-secondary {
  color: var(--label-gray-dark);
  text-shadow: var(--label-shadow-text);
  font-weight: 400;
}

.label-text-muted {
  color: rgba(0, 0, 0, 0.6);
  text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);
  font-size: 0.9em;
}

/* Semi-transparent form backgrounds for overlay effects */
.label-form-container {
  background: var(--label-gray-light);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  border: var(--label-border-width) solid var(--label-black);
  border-radius: var(--label-border-radius);
  box-shadow: var(--label-shadow-box);
  padding: var(--label-padding-lg);
  margin: var(--label-margin-md);
}

.label-form-background {
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.85) 100%);
  border: 2px solid var(--label-black);
  border-radius: 4px;
  box-shadow:
    0 4px 8px var(--label-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.label-form-transparent {
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border: none;
  border-radius: 4px;
  box-shadow: none;
}

/* Form elements with label-print aesthetic */
.label-input {
  font-family: var(--label-font-primary);
  background: var(--label-white);
  border: var(--label-border-width) solid var(--label-black);
  border-radius: var(--label-border-radius);
  padding: var(--label-padding-sm) var(--label-padding-md);
  color: var(--label-black);
  font-size: 0.95rem;
  line-height: 1.4;
  transition: all 0.2s ease;
  box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.label-input:focus {
  outline: none;
  border-color: var(--label-black);
  box-shadow:
    inset 1px 1px 2px rgba(0, 0, 0, 0.1),
    0 0 0 2px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.98);
}

.label-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
  font-style: italic;
}

/* Label-style buttons */
.label-button {
  font-family: var(--label-font-primary);
  background: var(--label-white);
  border: 2px solid var(--label-black);
  border-radius: var(--label-border-radius);
  padding: var(--label-padding-sm) var(--label-padding-md);
  color: var(--label-black);
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.15s ease;
  box-shadow: 2px 2px 0 var(--label-black);
  position: relative;
  user-select: none;
  box-sizing: border-box;
}

.label-button:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0 var(--label-black);
}

.label-button:active {
  transform: translate(2px, 2px);
  box-shadow: none;
}

.label-button:disabled {
  background: rgba(255, 255, 255, 0.6);
  color: rgba(0, 0, 0, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
}

.label-button-primary {
  background: var(--label-black);
  color: var(--label-white);
  box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.5);
}

.label-button-primary:hover {
  background: rgba(0, 0, 0, 0.9);
  color: var(--label-white);
}

/* Form labels */
.label-form-label {
  font-family: var(--label-font-primary);
  color: var(--label-black);
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--label-margin-sm);
  display: block;
  text-shadow: var(--label-shadow-text);
}

/* Error messages */
.label-error {
  font-family: var(--label-font-primary);
  color: #d32f2f;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #d32f2f;
  border-radius: var(--label-border-radius);
  padding: var(--label-padding-sm);
  margin-top: var(--label-margin-sm);
  font-size: 0.8rem;
  font-weight: 500;
  text-shadow: none;
  box-shadow: var(--label-shadow-box);
}

/* Success messages */
.label-success {
  font-family: var(--label-font-primary);
  color: #2e7d32;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #2e7d32;
  border-radius: var(--label-border-radius);
  padding: var(--label-padding-sm);
  margin-top: var(--label-margin-sm);
  font-size: 0.8rem;
  font-weight: 500;
  text-shadow: none;
  box-shadow: var(--label-shadow-box);
}

/* Loading states */
.label-loading {
  position: relative;
  overflow: hidden;
}

.label-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent);
  animation: label-loading-sweep 1.5s infinite;
}

@keyframes label-loading-sweep {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

/* Responsive design for different screen sizes */
@media (max-width: 768px) {
  .label-form-container {
    padding: var(--label-padding-md);
    margin: var(--label-margin-sm);
  }

  .label-input {
    font-size: 1rem;
    padding: var(--label-padding-md);
  }

  .label-button {
    font-size: 1rem;
    padding: var(--label-padding-md) var(--label-padding-lg);
  }

  .label-form-label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .label-form-container {
    padding: var(--label-padding-sm);
    margin: 0;
    border-radius: 0;
  }

  .label-input {
    font-size: 1.1rem;
    padding: var(--label-padding-md);
  }

  .label-button {
    width: 100%;
    font-size: 1.1rem;
    padding: var(--label-padding-md);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .label-form-container {
    background: var(--label-white);
    border-width: 3px;
    border-color: var(--label-black);
  }

  .label-input {
    border-width: 2px;
    background: var(--label-white);
    border-color: var(--label-black);
  }

  .label-input:focus {
    border-width: 3px;
    outline: 3px solid var(--label-black);
    outline-offset: 2px;
  }

  .label-button {
    border-width: 3px;
    font-weight: 700;
    border-color: var(--label-black);
  }

  .label-text-primary,
  .label-text-secondary {
    text-shadow: none;
    font-weight: 700;
    color: var(--label-black);
  }

  .label-error {
    border-width: 2px;
    background: #fff;
    color: #d32f2f;
    font-weight: 700;
  }

  .label-success {
    border-width: 2px;
    background: #fff;
    color: #2e7d32;
    font-weight: 700;
  }
}

/* Enhanced high contrast class for programmatic control */
.label-form-container.high-contrast {
  background: var(--label-white);
  border-width: 3px;
  border-color: var(--label-black);
}

.high-contrast .label-input {
  border-width: 2px;
  background: var(--label-white);
  border-color: var(--label-black);
}

.high-contrast .label-input:focus {
  border-width: 3px;
  outline: 3px solid var(--label-black);
  outline-offset: 2px;
}

.high-contrast .label-button {
  border-width: 3px;
  font-weight: 700;
  border-color: var(--label-black);
}

.high-contrast .label-text-primary,
.high-contrast .label-text-secondary {
  text-shadow: none;
  font-weight: 700;
  color: var(--label-black);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {

  .label-input,
  .label-button {
    transition: none;
  }

  .label-loading::after {
    animation: none;
  }

  .label-button:hover {
    transform: none;
  }

  .label-button:active {
    transform: none;
  }
}

/* Print styles for label aesthetic */
@media print {
  .label-form-container {
    background: var(--label-white);
    border: 2px solid var(--label-black);
    box-shadow: none;
  }

  .label-input,
  .label-button {
    background: var(--label-white);
    border: 1px solid var(--label-black);
    box-shadow: none;
  }

  .label-text-primary,
  .label-text-secondary {
    text-shadow: none;
    color: var(--label-black);
  }
}

/* Enhanced focus management for accessibility */
.label-focus-visible {
  outline: 3px solid rgba(0, 0, 0, 0.8);
  outline-offset: 2px;
}

/* Keyboard navigation support */
.label-form-container.keyboard-navigation .label-input:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
  border-color: #0066cc;
  box-shadow:
    inset 1px 1px 2px rgba(0, 0, 0, 0.1),
    0 0 0 2px rgba(0, 102, 204, 0.2);
}

.label-form-container.keyboard-navigation .label-button:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
  border-color: #0066cc;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

/* Screen reader active styles */
.label-form-container.screen-reader-active {
  /* Ensure sufficient spacing for screen reader navigation */
  padding: calc(var(--label-padding-lg) + 0.5rem);
}

.screen-reader-active .label-form-label {
  /* More descriptive labels for screen readers */
  font-size: 0.9rem;
  margin-bottom: calc(var(--label-margin-sm) + 0.25rem);
}

.screen-reader-active .label-input {
  /* Better spacing for screen reader users */
  margin-bottom: calc(var(--label-margin-md) + 0.25rem);
}

/* Skip links for keyboard navigation */
.label-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--label-black);
  color: var(--label-white);
  padding: 8px;
  text-decoration: none;
  border-radius: 2px;
  font-family: var(--label-font-primary);
  font-size: 0.9rem;
  z-index: 1000;
  transition: top 0.2s ease;
}

.label-skip-link:focus {
  top: 6px;
  outline: 2px solid var(--label-white);
  outline-offset: 2px;
}

/* Enhanced error and success message accessibility */
.label-error {
  position: relative;
  padding-left: calc(var(--label-padding-sm) + 1.5rem);
}

.label-error::before {
  content: '⚠';
  position: absolute;
  left: var(--label-padding-sm);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  color: #d32f2f;
  font-weight: bold;
}

.label-success {
  position: relative;
  padding-left: calc(var(--label-padding-sm) + 1.5rem);
}

.label-success::before {
  content: '✓';
  position: absolute;
  left: var(--label-padding-sm);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  color: #2e7d32;
  font-weight: bold;
}

/* Screen reader support */
.label-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Utility classes for spacing and layout */
.label-stack {
  display: flex;
  flex-direction: column;
  gap: var(--label-margin-md);
}

.label-row {
  display: flex;
  gap: var(--label-margin-md);
  align-items: center;
}

.label-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.label-full-width {
  width: 100%;
}