import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { Activity, Cpu, HardDrive, Network, Zap, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { AIPerformanceMonitor, PerformanceStats } from '../../services/performance/AIPerformanceMonitor';
import { ResourceTracker, ResourceMetrics, OptimizationRecommendation } from '../../services/performance/ResourceTracker';
import { AIPerformanceOptimizer, OptimizationResult } from '../../services/performance/AIPerformanceOptimizer';

interface AIPerformanceDashboardProps {
  performanceMonitor: AIPerformanceMonitor;
  resourceTracker: ResourceTracker;
  performanceOptimizer: AIPerformanceOptimizer;
}

export const AIPerformanceDashboard: React.FC<AIPerformanceDashboardProps> = ({
  performanceMonitor,
  resourceTracker,
  performanceOptimizer
}) => {
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats[]>([]);
  const [resourceMetrics, setResourceMetrics] = useState<ResourceMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<ResourceMetrics | null>(null);
  const [recommendations, setRecommendations] = useState<OptimizationRecommendation[]>([]);
  const [optimizationHistory, setOptimizationHistory] = useState<OptimizationResult[]>([]);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [autoOptimizationEnabled, setAutoOptimizationEnabled] = useState(true);

  useEffect(() => {
    const updateData = () => {
      setPerformanceStats(performanceMonitor.getAllOperationStats(3600000)); // Last hour
      setResourceMetrics(resourceTracker.getHistory(3600000));
      setCurrentMetrics(resourceTracker.getCurrentMetrics());
      setOptimizationHistory(performanceOptimizer.getOptimizationHistory(10));
    };

    const updateRecommendations = async () => {
      try {
        const analysis = await performanceOptimizer.analyzePerformance();
        setRecommendations(analysis.recommendations);
      } catch (error) {
        console.error('Failed to get recommendations:', error);
      }
    };

    updateData();
    updateRecommendations();

    const interval = setInterval(updateData, 30000); // Update every 30 seconds
    const recommendationInterval = setInterval(updateRecommendations, 300000); // Update every 5 minutes

    return () => {
      clearInterval(interval);
      clearInterval(recommendationInterval);
    };
  }, [performanceMonitor, resourceTracker, performanceOptimizer]);

  const handleRunOptimization = async () => {
    setIsOptimizing(true);
    try {
      const results = await performanceOptimizer.runAutoOptimization();
      setOptimizationHistory(prev => [...results, ...prev].slice(0, 10));
    } catch (error) {
      console.error('Optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleApplyRecommendation = async (recommendationId: string) => {
    // Find corresponding strategy and apply it
    const strategies = performanceOptimizer.getStrategies();
    const strategy = strategies.find(s => s.id.includes(recommendationId.split('-')[0]));
    
    if (strategy) {
      setIsOptimizing(true);
      try {
        const result = await performanceOptimizer.applyOptimization(strategy.id);
        setOptimizationHistory(prev => [result, ...prev].slice(0, 10));
      } catch (error) {
        console.error('Failed to apply optimization:', error);
      } finally {
        setIsOptimizing(false);
      }
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'easy': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'hard': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">AI Performance Dashboard</h2>
          <p className="text-muted-foreground">
            Überwachen und optimieren Sie die Leistung Ihrer AI-Services
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRunOptimization}
            disabled={isOptimizing}
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            {isOptimizing ? 'Optimiere...' : 'Optimierung starten'}
          </Button>
        </div>
      </div>

      {/* Current Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Speicherverbrauch</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currentMetrics ? formatBytes(currentMetrics.memoryUsage.heapUsed) : '0 B'}
            </div>
            <Progress 
              value={currentMetrics ? (currentMetrics.memoryUsage.heapUsed / currentMetrics.memoryUsage.heapTotal) * 100 : 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU-Auslastung</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currentMetrics ? `${currentMetrics.cpuUsage.percent.toFixed(1)}%` : '0%'}
            </div>
            <Progress 
              value={currentMetrics?.cpuUsage.percent || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Operationen</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currentMetrics?.aiSpecificMetrics.activeEmbeddings || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Laufende AI-Operationen
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache-Größe</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currentMetrics ? formatBytes(currentMetrics.aiSpecificMetrics.cacheSize) : '0 B'}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Zwischengespeicherte Daten
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="resources">Ressourcen</TabsTrigger>
          <TabsTrigger value="recommendations">Empfehlungen</TabsTrigger>
          <TabsTrigger value="optimization">Optimierung</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operation Performance</CardTitle>
              <CardDescription>
                Leistungsmetriken für AI-Operationen der letzten Stunde
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performanceStats.map((stat) => (
                  <div key={stat.operationName} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-semibold">{stat.operationName}</h4>
                      <Badge variant={stat.errorRate > 0.05 ? 'destructive' : 'default'}>
                        {(stat.errorRate * 100).toFixed(1)}% Fehlerrate
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Durchschnitt</p>
                        <p className="font-medium">{formatDuration(stat.averageDuration)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">P95</p>
                        <p className="font-medium">{formatDuration(stat.p95Duration)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Durchsatz</p>
                        <p className="font-medium">{stat.throughput.toFixed(2)} ops/s</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Aufrufe</p>
                        <p className="font-medium">{stat.totalCalls}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Ressourcenverbrauch</CardTitle>
              <CardDescription>
                Verlauf der Ressourcennutzung der letzten Stunde
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={resourceMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                      formatter={(value: any, name: string) => {
                        if (name.includes('memory')) {
                          return [formatBytes(value), name];
                        }
                        if (name.includes('cpu')) {
                          return [`${value.toFixed(1)}%`, name];
                        }
                        return [value, name];
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="memoryUsage.heapUsed" 
                      stroke="#8884d8" 
                      name="Speicher"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="cpuUsage.percent" 
                      stroke="#82ca9d" 
                      name="CPU"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimierungsempfehlungen</CardTitle>
              <CardDescription>
                Automatisch generierte Empfehlungen zur Leistungsverbesserung
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <p className="text-lg font-medium">Keine Optimierungen erforderlich</p>
                    <p className="text-muted-foreground">Alle Systeme laufen optimal</p>
                  </div>
                ) : (
                  recommendations.map((rec) => (
                    <Alert key={rec.id}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle className="flex items-center justify-between">
                        <span>{rec.title}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant={getPriorityColor(rec.priority)}>
                            {rec.priority}
                          </Badge>
                          <Badge variant="outline" className={getComplexityColor(rec.complexity)}>
                            {rec.complexity}
                          </Badge>
                        </div>
                      </AlertTitle>
                      <AlertDescription className="mt-2">
                        <p className="mb-2">{rec.description}</p>
                        <p className="text-sm text-muted-foreground mb-2">
                          <strong>Auswirkung:</strong> {rec.impact}
                        </p>
                        <p className="text-sm text-muted-foreground mb-3">
                          <strong>Umsetzung:</strong> {rec.implementation}
                        </p>
                        {rec.estimatedSavings && (
                          <div className="flex gap-4 text-sm mb-3">
                            {rec.estimatedSavings.memory && (
                              <span>💾 {formatBytes(rec.estimatedSavings.memory)} Speicher</span>
                            )}
                            {rec.estimatedSavings.cpu && (
                              <span>⚡ {rec.estimatedSavings.cpu}% CPU</span>
                            )}
                            {rec.estimatedSavings.time && (
                              <span>⏱️ {formatDuration(rec.estimatedSavings.time)} Zeit</span>
                            )}
                          </div>
                        )}
                        <Button
                          size="sm"
                          onClick={() => handleApplyRecommendation(rec.id)}
                          disabled={isOptimizing}
                        >
                          Anwenden
                        </Button>
                      </AlertDescription>
                    </Alert>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimierungsverlauf</CardTitle>
              <CardDescription>
                Verlauf der angewendeten Optimierungen und deren Auswirkungen
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {optimizationHistory.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">
                    Noch keine Optimierungen durchgeführt
                  </p>
                ) : (
                  optimizationHistory.map((result) => (
                    <div key={`${result.strategyId}-${result.timestamp}`} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-semibold">{result.strategyId}</h4>
                        <div className="flex items-center gap-2">
                          <Badge variant={result.applied ? 'default' : 'destructive'}>
                            {result.applied ? 'Erfolgreich' : 'Fehlgeschlagen'}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {new Date(result.timestamp).toLocaleString()}
                          </span>
                        </div>
                      </div>
                      {result.applied && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Latenz-Reduktion</p>
                            <p className="font-medium text-green-600">
                              -{formatDuration(result.improvement.latencyReduction || 0)}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Speicher-Reduktion</p>
                            <p className="font-medium text-green-600">
                              -{formatBytes(result.improvement.memoryReduction || 0)}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">CPU-Reduktion</p>
                            <p className="font-medium text-green-600">
                              -{(result.improvement.cpuReduction || 0).toFixed(1)}%
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Durchsatz-Steigerung</p>
                            <p className="font-medium text-green-600">
                              +{(result.improvement.throughputIncrease || 0).toFixed(2)} ops/s
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};