import { useState } from "react";
import { DateRange } from "react-day-picker";
import { AlertTriangle, Activity, BarChart3, Plus, RefreshCw, BookOpen } from "lucide-react";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import { Button } from "@/components/ui/button";
import { useDialog } from "@/components/ui/dialog";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";

// Import Störungen-specific components from module
import { SystemStatusOverview, StoerungsList } from "../components";
import { openStoerungsForm } from "../utils/openStoerungsForm";
import { StoerungsKpiCards, SeverityDistributionChart, StatusDistributionChart } from "../components/charts/StoerungsStatsChart";
import { StoerungsCategoryChart } from "../components/charts/StoerungsCategoryC<PERSON>";
import { MTT<PERSON>rend<PERSON><PERSON>, MTTRKpiCards } from "../components/charts/MTTRTrendChart";
import { SystemStatusHeatmap } from "@/modules/backend/components/charts/SystemStatusHeatmap";
import { BereitschaftsPlan } from "../components/stoerungen/BereitschaftsPlan";
import { BereitschaftsKonfigurationDialog } from "../components/stoerungen/BereitschaftsKonfigurationDialog";
import { RunbookList } from "@/modules/runbooks/components";

export default function StoerungenPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 4, 1), // 1. Mai 2025 (month is 0-indexed)
    to: new Date(2025, 7, 31), // 31. August 2025
  });

  const [refreshKey, setRefreshKey] = useState(0);
  const [activeTab, setActiveTab] = useState("monitoring");
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const { openDialog } = useDialog();

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleOpenStoerungsForm = () => {
    openStoerungsForm(openDialog, handleRefresh);
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleConfigurationChange = () => {
    handleRefresh();
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Picker */}
      <div className="mb-6 flex justify-between items-center">
        <div className="flex flex-col gap-4">
          <h1 className="text-3xl font-bold text-black flex items-center gap-2">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            STÖRUNGEN
          </h1>

          {/* Buttons unter der Überschrift */}
          <div className="flex items-center gap-2">
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Aktualisieren
            </Button>

            <Button
              onClick={handleOpenStoerungsForm}
              className="gap-2 bg-red-600 hover:bg-red-700"
            >
              <Plus className="h-4 w-4" />
              Störung Melden
            </Button>
          </div>
        </div>

        {/* Date Range Picker rechts oben */}
        <div className="flex items-start">
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
            label="Zeitraum auswählen"
            className="w-64"
          />
        </div>
      </div>

      {/* Smooth Tabs mit Störungsmanagement-Bereichen */}
      <div className="space-y-6">
        <div className="flex justify-center">
          <SmoothTab
            items={[
              {
                id: "monitoring",
                title: "Live Monitoring",
                icon: Activity,
                color: "bg-green-500 hover:bg-green-600",
              },
              {
                id: "management",
                title: "Störungsmanagement",
                icon: AlertTriangle,
                color: "bg-orange-500 hover:bg-orange-600",
              },
              {
                id: "analytics",
                title: "Analytics Dashboard",
                icon: BarChart3,
                color: "bg-blue-500 hover:bg-blue-600",
              },
              {
                id: "runbooks",
                title: "Runbooks",
                icon: BookOpen,
                color: "bg-purple-500 hover:bg-purple-600",
              },
            ]}
            defaultTabId="monitoring"
            onChange={handleTabChange}
            key={refreshKey}
            hideCardContent={true}
          />
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "monitoring" && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                {/* System Status Overview */}
                <ChartErrorBoundary>
                  <SystemStatusOverview refreshKey={refreshKey} />
                </ChartErrorBoundary>

                {/* System Status Heatmap */}
                <ChartErrorBoundary>
                  <SystemStatusHeatmap dateRange={dateRange} refreshKey={refreshKey} />
                </ChartErrorBoundary>
              </div>
            </div>
          )}

          {activeTab === "management" && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                {/* Störungsliste - nimmt 2/3 der Breite ein */}
                <div className="xl:col-span-2">
                  <ChartErrorBoundary>
                    <StoerungsList
                      dateRange={dateRange}
                      refreshKey={refreshKey}
                      onRefresh={handleRefresh}
                    />
                  </ChartErrorBoundary>
                </div>

                {/* Bereitschaftsplan - nimmt 1/3 der Breite ein */}
                <div className="xl:col-span-1">
                  <ChartErrorBoundary>
                    <BereitschaftsPlan
                      refreshKey={refreshKey}
                      onConfigureClick={() => setIsConfigDialogOpen(true)}
                    />
                  </ChartErrorBoundary>
                </div>
              </div>
            </div>
          )}

          {activeTab === "analytics" && (
            <div className="space-y-6">
              {/* Combined KPI Cards Row - All 6 cards in one row spanning full width */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 w-full">
                <ChartErrorBoundary>
                  <StoerungsKpiCards dateRange={dateRange} refreshKey={refreshKey} />
                </ChartErrorBoundary>
                <ChartErrorBoundary>
                  <MTTRKpiCards dateRange={dateRange} refreshKey={refreshKey} />
                </ChartErrorBoundary>
              </div>

              {/* 2x3 Grid Layout: 2x2 left + 1x2 right */}
              <div className="grid grid-cols-4 xl:grid-cols-4 gap-4 grid-rows-1">
                {/* Top Left - Severity Distribution (Radial Chart) */}
                <ChartErrorBoundary>
                  <SeverityDistributionChart dateRange={dateRange} refreshKey={refreshKey} />
                </ChartErrorBoundary>

                {/* Top Middle - Status Distribution (Pie Chart) */}
                <ChartErrorBoundary>
                  <StatusDistributionChart dateRange={dateRange} refreshKey={refreshKey} />
                </ChartErrorBoundary>

                {/* Right - MTTR Trend Chart (spans 2 rows) */}
                <div className="xl:row-span-2">
                  <ChartErrorBoundary>
                    <MTTRTrendChart dateRange={dateRange} refreshKey={refreshKey} />
                  </ChartErrorBoundary>
                </div>

                {/* Bottom Left - Category Charts (2 bar charts in one component) */}
                <div className="xl:col-span-2">
                  <ChartErrorBoundary>
                    <StoerungsCategoryChart dateRange={dateRange} refreshKey={refreshKey} />
                  </ChartErrorBoundary>
                </div>
              </div>
            </div>
          )}

          {activeTab === "runbooks" && (
            <RunbookList />
          )}
        </div>
      </div>

      {/* Bereitschafts-Konfiguration Dialog */}
      <BereitschaftsKonfigurationDialog
        open={isConfigDialogOpen}
        onOpenChange={setIsConfigDialogOpen}
        onConfigurationChange={handleConfigurationChange}
      />
    </div>
  );
}