/**
 * Störungen Data Integration Service
 * 
 * Specialized service for querying and formatting Störungen (incidents) data
 * for AI chatbot integration. Provides incident statistics, system status,
 * and MTTR metrics with time-based filtering capabilities.
 */

import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { StoerungenRepository } from '../repositories/stoerungen.repository';
import { 
  StoerungsStats, 
  SystemStatusData, 
  StoerungWithComments 
} from '../types/stoerungen.types';
import { DateRange } from '../types/data-enrichment.types';

export interface StoerungenDataIntegrationResult {
  statistics: StoerungsStats;
  systemStatus: SystemStatusData[];
  recentIncidents: StoerungWithComments[];
  trends: StoerungenTrends;
  summary: string;
  timestamp: Date;
}

export interface StoerungenTrends {
  mttrTrend: {
    current: number;
    previous: number;
    change: number;
    changePercent: number;
  };
  resolutionRate: {
    last24h: number;
    last7days: number;
    last30days: number;
  };
  severityTrends: {
    critical: { current: number; trend: 'up' | 'down' | 'stable' };
    high: { current: number; trend: 'up' | 'down' | 'stable' };
    medium: { current: number; trend: 'up' | 'down' | 'stable' };
    low: { current: number; trend: 'up' | 'down' | 'stable' };
  };
  systemHealthScore: number; // 0-100 overall health score
}

export interface StoerungenQueryOptions {
  timeRange?: DateRange;
  includeTrends?: boolean;
  includeSystemStatus?: boolean;
  includeRecentIncidents?: boolean;
  maxIncidents?: number;
  severityFilter?: string[];
  statusFilter?: string[];
}

export class StoerungenDataIntegrationService {
  private stoerungenRepo: StoerungenRepository;

  constructor(prisma: PrismaClient) {
    this.stoerungenRepo = StoerungenRepository.getInstance(prisma);
  }

  /**
   * Get comprehensive Störungen data for AI chatbot context
   */
  async getStoerungenDataForAI(
    options: StoerungenQueryOptions = {}
  ): Promise<StoerungenDataIntegrationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 [STÖRUNGEN-INTEGRATION] Fetching comprehensive Störungen data');
      
      // Execute parallel queries for better performance
      const [statistics, systemStatus, recentIncidents, trends] = await Promise.all([
        this.getIncidentStatistics(options.timeRange),
        options.includeSystemStatus !== false ? this.getSystemStatusData() : Promise.resolve([]),
        options.includeRecentIncidents !== false ? this.getRecentIncidentsWithFiltering(options) : Promise.resolve([]),
        options.includeTrends !== false ? this.getIncidentTrends(options.timeRange) : Promise.resolve(this.getEmptyTrends())
      ]);

      // Generate comprehensive summary
      const summary = this.formatStoerungenSummary(statistics, systemStatus, recentIncidents, trends);

      const result: StoerungenDataIntegrationResult = {
        statistics,
        systemStatus,
        recentIncidents,
        trends,
        summary,
        timestamp: new Date()
      };

      console.log(`✅ [STÖRUNGEN-INTEGRATION] Data fetched in ${Date.now() - startTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ [STÖRUNGEN-INTEGRATION] Error fetching Störungen data:', error);
      throw new Error(`Failed to fetch Störungen data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get incident statistics with optional time filtering
   */
  async getIncidentStatistics(timeRange?: DateRange): Promise<StoerungsStats> {
    try {
      // Get basic statistics from repository
      const stats = await this.stoerungenRepo.getStoerungsStats();
      
      // If time range is specified, we would need to implement time-filtered statistics
      // For now, we'll use the existing stats but could enhance this later
      if (timeRange) {
        console.log(`📊 [STÖRUNGEN-INTEGRATION] Time-filtered statistics requested for ${timeRange.startDate} to ${timeRange.endDate}`);
        // TODO: Implement time-filtered statistics when repository supports it
      }
      
      return stats;
    } catch (error) {
      console.error('❌ [STÖRUNGEN-INTEGRATION] Error fetching incident statistics:', error);
      throw error;
    }
  }

  /**
   * Get system status data with health indicators
   */
  async getSystemStatusData(): Promise<SystemStatusData[]> {
    try {
      const systemStatus = await this.stoerungenRepo.getSystemStatus();
      
      // Sort by status priority (ERROR first, then WARNING, then OK)
      return systemStatus.sort((a, b) => {
        const statusPriority = { 'ERROR': 3, 'WARNING': 2, 'OK': 1 };
        return statusPriority[b.status] - statusPriority[a.status];
      });
    } catch (error) {
      console.error('❌ [STÖRUNGEN-INTEGRATION] Error fetching system status:', error);
      throw error;
    }
  }

  /**
   * Get recent incidents with filtering options
   */
  async getRecentIncidentsWithFiltering(
    options: StoerungenQueryOptions
  ): Promise<StoerungWithComments[]> {
    try {
      const queryOptions: any = {
        limit: options.maxIncidents || 10,
        offset: 0
      };

      // Apply filters if specified
      if (options.severityFilter && options.severityFilter.length > 0) {
        queryOptions.severity = options.severityFilter[0]; // Repository only supports single severity
      }
      
      if (options.statusFilter && options.statusFilter.length > 0) {
        queryOptions.status = options.statusFilter[0]; // Repository only supports single status
      }

      const incidents = await this.stoerungenRepo.getStoerungen(queryOptions);
      
      // Apply time filtering if specified (client-side filtering since repository doesn't support it)
      if (options.timeRange) {
        return this.filterIncidentsByTimeRange(incidents, options.timeRange);
      }
      
      return incidents;
    } catch (error) {
      console.error('❌ [STÖRUNGEN-INTEGRATION] Error fetching recent incidents:', error);
      throw error;
    }
  }

  /**
   * Get incident trends and MTTR analysis
   */
  async getIncidentTrends(timeRange?: DateRange): Promise<StoerungenTrends> {
    try {
      // Get current statistics
      const currentStats = await this.stoerungenRepo.getStoerungsStats();
      const systemStatus = await this.stoerungenRepo.getSystemStatus();
      
      // Calculate system health score
      const systemHealthScore = this.calculateSystemHealthScore(currentStats, systemStatus);
      
      // For now, we'll create mock trend data since we don't have historical comparison
      // In a real implementation, this would compare current vs previous periods
      const trends: StoerungenTrends = {
        mttrTrend: {
          current: currentStats.avg_mttr_minutes,
          previous: currentStats.avg_mttr_minutes * 1.1, // Mock previous value
          change: currentStats.avg_mttr_minutes * -0.1,
          changePercent: -10
        },
        resolutionRate: {
          last24h: currentStats.resolution_rate_24h,
          last7days: Math.round(currentStats.resolution_rate_24h * 7 * 0.8), // Mock calculation
          last30days: Math.round(currentStats.resolution_rate_24h * 30 * 0.6) // Mock calculation
        },
        severityTrends: {
          critical: { current: currentStats.critical, trend: currentStats.critical > 0 ? 'up' : 'stable' },
          high: { current: currentStats.high, trend: currentStats.high > 2 ? 'up' : 'stable' },
          medium: { current: currentStats.medium, trend: 'stable' },
          low: { current: currentStats.low, trend: 'stable' }
        },
        systemHealthScore
      };
      
      return trends;
    } catch (error) {
      console.error('❌ [STÖRUNGEN-INTEGRATION] Error calculating incident trends:', error);
      return this.getEmptyTrends();
    }
  }

  /**
   * Format comprehensive Störungen summary for AI consumption
   */
  formatStoerungenSummary(
    statistics: StoerungsStats,
    systemStatus: SystemStatusData[],
    recentIncidents: StoerungWithComments[],
    trends: StoerungenTrends
  ): string {
    const criticalSystems = systemStatus.filter(s => s.status === 'ERROR').length;
    const warningSystems = systemStatus.filter(s => s.status === 'WARNING').length;
    const healthySystems = systemStatus.filter(s => s.status === 'OK').length;
    
    const criticalIncidents = recentIncidents.filter(i => i.severity === 'CRITICAL').length;
    const activeIncidents = recentIncidents.filter(i => i.status !== 'RESOLVED').length;
    
    let summary = `STÖRUNGEN-ÜBERSICHT:\n`;
    summary += `Gesamt: ${statistics.total} Störungen (${statistics.active} aktiv, ${statistics.resolved} gelöst)\n`;
    summary += `Schweregrade: ${statistics.critical} kritisch, ${statistics.high} hoch, ${statistics.medium} mittel, ${statistics.low} niedrig\n`;
    summary += `MTTR: ${statistics.avg_mttr_minutes} Minuten (Durchschnitt)\n`;
    summary += `Auflösungsrate: ${statistics.resolution_rate_24h} in den letzten 24h\n\n`;
    
    summary += `SYSTEMSTATUS:\n`;
    summary += `${criticalSystems} Systeme mit Fehlern, ${warningSystems} mit Warnungen, ${healthySystems} gesund\n`;
    summary += `Gesamt-Systemgesundheit: ${trends.systemHealthScore}%\n\n`;
    
    if (recentIncidents.length > 0) {
      summary += `AKTUELLE VORFÄLLE:\n`;
      summary += `${activeIncidents} aktive Störungen, ${criticalIncidents} kritische Vorfälle\n`;
      
      // Add details about most recent critical incidents
      const recentCritical = recentIncidents
        .filter(i => i.severity === 'CRITICAL' && i.status !== 'RESOLVED')
        .slice(0, 3);
      
      if (recentCritical.length > 0) {
        summary += `Kritische Störungen:\n`;
        recentCritical.forEach(incident => {
          summary += `- ${incident.title} (${incident.affected_system || 'System unbekannt'})\n`;
        });
      }
    }
    
    // Add trend information
    if (trends.mttrTrend.changePercent !== 0) {
      const trendDirection = trends.mttrTrend.changePercent > 0 ? 'gestiegen' : 'gesunken';
      summary += `\nTREND: MTTR ist um ${Math.abs(trends.mttrTrend.changePercent)}% ${trendDirection}\n`;
    }
    
    return summary;
  }

  /**
   * Format incident data for specific AI queries
   */
  formatIncidentDataForQuery(
    incidents: StoerungWithComments[],
    queryType: 'status' | 'severity' | 'system' | 'recent' = 'recent'
  ): string {
    if (incidents.length === 0) {
      return 'Keine Störungen gefunden für die angegebenen Kriterien.';
    }
    
    let formatted = '';
    
    switch (queryType) {
      case 'status':
        const byStatus = this.groupIncidentsByStatus(incidents);
        formatted = 'STÖRUNGEN NACH STATUS:\n';
        Object.entries(byStatus).forEach(([status, count]) => {
          const statusName = this.getStatusDisplayName(status);
          formatted += `${statusName}: ${count}\n`;
        });
        break;
        
      case 'severity':
        const bySeverity = this.groupIncidentsBySeverity(incidents);
        formatted = 'STÖRUNGEN NACH SCHWEREGRAD:\n';
        Object.entries(bySeverity).forEach(([severity, count]) => {
          const severityName = this.getSeverityDisplayName(severity);
          formatted += `${severityName}: ${count}\n`;
        });
        break;
        
      case 'system':
        const bySystem = this.groupIncidentsBySystem(incidents);
        formatted = 'STÖRUNGEN NACH SYSTEM:\n';
        Object.entries(bySystem).forEach(([system, count]) => {
          formatted += `${system || 'Unbekannt'}: ${count}\n`;
        });
        break;
        
      case 'recent':
      default:
        formatted = 'AKTUELLE STÖRUNGEN:\n';
        incidents.slice(0, 5).forEach(incident => {
          const age = this.getIncidentAge(incident.created_at);
          const status = this.getStatusDisplayName(incident.status);
          const severity = this.getSeverityDisplayName(incident.severity);
          formatted += `- ${incident.title} (${severity}, ${status}, ${age})\n`;
          if (incident.affected_system) {
            formatted += `  System: ${incident.affected_system}\n`;
          }
        });
        break;
    }
    
    return formatted;
  }

  // Private helper methods

  private filterIncidentsByTimeRange(
    incidents: StoerungWithComments[],
    timeRange: DateRange
  ): StoerungWithComments[] {
    if (!timeRange.startDate && !timeRange.endDate) {
      return incidents;
    }
    
    const startDate = timeRange.startDate ? new Date(timeRange.startDate) : null;
    const endDate = timeRange.endDate ? new Date(timeRange.endDate) : null;
    
    return incidents.filter(incident => {
      const incidentDate = new Date(incident.created_at);
      
      if (startDate && incidentDate < startDate) return false;
      if (endDate && incidentDate > endDate) return false;
      
      return true;
    });
  }

  private calculateSystemHealthScore(
    stats: StoerungsStats,
    systemStatus: SystemStatusData[]
  ): number {
    let score = 100;
    
    // Deduct points for active incidents
    score -= stats.critical * 20; // Critical incidents heavily impact score
    score -= stats.high * 10;
    score -= stats.medium * 5;
    score -= stats.low * 2;
    
    // Deduct points for system status issues
    const errorSystems = systemStatus.filter(s => s.status === 'ERROR').length;
    const warningSystems = systemStatus.filter(s => s.status === 'WARNING').length;
    
    score -= errorSystems * 15;
    score -= warningSystems * 5;
    
    // Ensure score doesn't go below 0
    return Math.max(0, Math.round(score));
  }

  private getEmptyTrends(): StoerungenTrends {
    return {
      mttrTrend: {
        current: 0,
        previous: 0,
        change: 0,
        changePercent: 0
      },
      resolutionRate: {
        last24h: 0,
        last7days: 0,
        last30days: 0
      },
      severityTrends: {
        critical: { current: 0, trend: 'stable' },
        high: { current: 0, trend: 'stable' },
        medium: { current: 0, trend: 'stable' },
        low: { current: 0, trend: 'stable' }
      },
      systemHealthScore: 100
    };
  }

  private groupIncidentsByStatus(incidents: StoerungWithComments[]): Record<string, number> {
    return incidents.reduce((acc, incident) => {
      acc[incident.status] = (acc[incident.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private groupIncidentsBySeverity(incidents: StoerungWithComments[]): Record<string, number> {
    return incidents.reduce((acc, incident) => {
      acc[incident.severity] = (acc[incident.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private groupIncidentsBySystem(incidents: StoerungWithComments[]): Record<string, number> {
    return incidents.reduce((acc, incident) => {
      const system = incident.affected_system || 'Unbekannt';
      acc[system] = (acc[system] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getStatusDisplayName(status: string): string {
    const statusMap: Record<string, string> = {
      'NEW': 'Neu',
      'IN_PROGRESS': 'In Bearbeitung',
      'RESOLVED': 'Gelöst'
    };
    return statusMap[status] || status;
  }

  private getSeverityDisplayName(severity: string): string {
    const severityMap: Record<string, string> = {
      'CRITICAL': 'Kritisch',
      'HIGH': 'Hoch',
      'MEDIUM': 'Mittel',
      'LOW': 'Niedrig'
    };
    return severityMap[severity] || severity;
  }

  private getIncidentAge(createdAt: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - new Date(createdAt).getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `vor ${diffDays} Tag${diffDays > 1 ? 'en' : ''}`;
    } else if (diffHours > 0) {
      return `vor ${diffHours} Stunde${diffHours > 1 ? 'n' : ''}`;
    } else {
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      return `vor ${diffMinutes} Minute${diffMinutes > 1 ? 'n' : ''}`;
    }
  }
}

export default StoerungenDataIntegrationService;