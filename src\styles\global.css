@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.705 0.213 47.604);
  --primary-foreground: oklch(0.98 0.016 73.684);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.213 47.604);
  --chart-1: oklch(0.685 0.169 237.323);
  --chart-2: oklch(0.66 0.17 301);
  --chart-3: oklch(0.77 0.10 55);
  --chart-4: oklch(0.442 0.017 285.786);
  --chart-5: oklch(0.704 0.14 182.503);
  --chart-6: oklch(0.869 0.022 252.894);
  --chart-7: oklch(0.553 0.013 58.071);
  --chart-8: oklch(0.437 0.078 188.216);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.705 0.213 47.604);
  --sidebar-primary-foreground: oklch(0.98 0.016 73.684);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.213 47.604);
}

.dark {
  /* Hauptfarben - Dunkler Hintergrund mit hohem Kontrast */
  --background: oklch(0.08 0.005 285.823);
  /* Sehr dunkler Hintergrund */
  --foreground: oklch(0.95 0.01 0);
  /* Helles Weiß für Text */

  /* Karten und Popover - Leicht heller als Hintergrund */
  --card: oklch(0.12 0.008 285.885);
  /* Dunkle Karten */
  --card-foreground: oklch(0.95 0.01 0);
  --popover: oklch(0.12 0.008 285.885);
  --popover-foreground: oklch(0.95 0.01 0);

  /* Primärfarbe - Lapp Orange angepasst für Dark Mode */
  --primary: oklch(0.65 0.15 45);
  /* Lapp Orange für Dark Mode */
  --primary-foreground: oklch(0.98 0.01 0);
  /* Weiß auf Orange */

  /* Sekundäre Farben */
  --secondary: oklch(0.18 0.008 286.033);
  /* Dunkles Grau */
  --secondary-foreground: oklch(0.9 0.01 0);

  /* Gedämpfte Farben */
  --muted: oklch(0.15 0.008 286.033);
  --muted-foreground: oklch(0.65 0.02 286.067);
  /* Besserer Kontrast */

  /* Akzentfarben */
  --accent: oklch(0.18 0.008 286.033);
  --accent-foreground: oklch(0.9 0.01 0);

  /* Destruktive Farben */
  --destructive: oklch(0.65 0.2 25);
  /* Rotes Warnsignal */

  /* Rahmen und Eingabefelder */
  --border: oklch(0.25 0.01 286.32);
  /* Sichtbare Rahmen */
  --input: oklch(0.15 0.01 286.32);
  /* Dunkle Eingabefelder */
  --ring: oklch(0.65 0.15 45);
  /* Lapp Orange für Focus */

  /* Chart-Farben - Angepasst für Dark Mode */
  --chart-1: oklch(0.7 0.15 240);
  /* Blau */
  --chart-2: oklch(0.7 0.15 300);
  /* Lila */
  --chart-3: oklch(0.75 0.12 60);
  /* Gelb */
  --chart-4: oklch(0.6 0.1 180);
  /* Grün */
  --chart-5: oklch(0.65 0.12 15);
  /* Orange-Rot */

  /* Sidebar - Konsistent mit Haupttheme */
  --sidebar: oklch(0.1 0.008 285.885);
  /* Sehr dunkle Sidebar */
  --sidebar-foreground: oklch(0.9 0.01 0);
  --sidebar-primary: oklch(0.65 0.15 45);
  /* Lapp Orange */
  --sidebar-primary-foreground: oklch(0.98 0.01 0);
  --sidebar-accent: oklch(0.18 0.008 286.033);
  --sidebar-accent-foreground: oklch(0.9 0.01 0);
  --sidebar-border: oklch(0.25 0.01 286.32);
  --sidebar-ring: oklch(0.65 0.15 45);
}

@theme inline {
  --color-main: var(--main);
  --color-background: var(--background);
  --color-secondary-background: var(--secondary-background);
  --color-foreground: var(--foreground);
  --color-main-foreground: var(--main-foreground);
  --color-border: var(--border);
  --color-overlay: var(--overlay);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --spacing-boxShadowX: 4px;
  --spacing-boxShadowY: 4px;
  --spacing-reverseBoxShadowX: -4px;
  --spacing-reverseBoxShadowY: -4px;
  --radius-base: 5px;
  --shadow-shadow: var(--shadow);
  --font-weight-base: 500;
  --font-weight-heading: 700;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
}

.container {
  width: 7em;
  height: 7em;
  position: relative;
}

.button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #090909;
  background-color: transparent;
  background-image: linear-gradient(145deg, #171717, #444245);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #7d7c7e, inset -2px -2px 0px #1c1c1c;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container input {
  display: none;
}

.button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #262626, #606060);
  z-index: -1;
  box-shadow: 11px 11px 22px #141414, -11px -11px 22px #525252;
}

.button .icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.button .icon svg {
  height: 100%;
  width: 100%;
  fill: #a5a5a5;
}

/* The switch - the box around the slider */
#theme-toggle-button {
  font-size: 17px;
  position: relative;
  display: inline-block;
  width: 7em;
  cursor: pointer;
}

/* Hide default HTML checkbox */
#toggle {
  opacity: 0;
  width: 0;
  height: 0;
}

#container,
#patches,
#stars,
#button,
#sun,
#moon,
#cloud {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.25s;
}

/* night sky background */
#toggle:checked+svg #container {
  fill: #2b4360;
}

/* move button to right when checked */
#toggle:checked+svg #button {
  transform: translate(28px, 2.333px);
}

/* show/hide sun and moon based on checkbox state */
#sun {
  opacity: 1;
}

#toggle:checked+svg #sun {
  opacity: 0;
}

#moon {
  opacity: 0;
}

#toggle:checked+svg #moon {
  opacity: 1;
}

/* show or hide background items on checkbox state */
#cloud {
  opacity: 1;
}

#toggle:checked+svg #cloud {
  opacity: 0;
}

#stars {
  opacity: 0;
}

#toggle:checked+svg #stars {
  opacity: 1;
}

@layer base {
  body {
    @apply text-foreground font-base bg-background;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* P
ower Switch Component Styles */
.power-switch-input {
  display: none;
}

.power-switch {
  position: relative;
  width: 70px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  z-index: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgb(126, 126, 126);
  box-shadow: 0px 0px 3px rgb(2, 2, 2) inset;
  transition: all 0.3s ease;
}

.power-switch svg {
  width: 1.2em;
  transition: all 0.3s ease;
}

.power-switch svg path {
  fill: rgb(48, 48, 48);
  transition: fill 0.3s ease;
}

.power-switch-input:checked+.power-switch {
  box-shadow:
    0px 0px 1px #4ade80 inset,
    0px 0px 3px #4ade80 inset,
    0px 0px 8px rgba(74, 222, 128, 0.4),
    0px 0px 15px rgba(74, 222, 128, 0.2);
  border: 2px solid #4ade80;
  background-color: rgb(146, 180, 184);
}

.power-switch-input:checked+.power-switch svg {
  filter: drop-shadow(0px 0px 2px rgba(74, 222, 128, 0.6));
}

.power-switch-input:checked+.power-switch svg path {
  fill: #4ade80;
}

.power-switch-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.power-switch-disabled:hover {
  transform: none;
}

/* Wi
Fi Button Component Styles */
.wifi-button-container {
  width: 7em;
  height: 7em;
  position: relative;
}

.wifi-button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #090909;
  background-color: transparent;
  background-image: linear-gradient(145deg, #171717, #444245);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #7d7c7e, inset -2px -2px 0px #1c1c1c;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wifi-button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #262626, #606060);
  z-index: -1;
  box-shadow: 11px 11px 22px #141414, -11px -11px 22px #525252;
}

.wifi-button .wifi-icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.wifi-button .wifi-icon svg {
  height: 100%;
  width: 100%;
  fill: #a5a5a5;
  transition: fill 0.3s ease;
}

.wifi-button-container input:checked+.wifi-button {
  box-shadow: inset -2px -2px 0 #5e5e5e, inset 2px 2px 0 #1c1c1c;
  border: 4px solid rgba(77, 124, 255, 0.281);
  animation: animeBorder 0.3s linear alternate-reverse infinite;
}

.wifi-button-container input:checked+.wifi-button .wifi-icon svg {
  fill: rgb(77, 124, 255);
  animation: animeFill 0.3s linear alternate-reverse infinite;
}

@keyframes animeFill {
  to {
    fill: rgba(77, 124, 255, 0.642);
  }
}

@keyframes animeBorder {
  to {
    border-color: rgba(77, 124, 255, 0.137);
  }
}

/* Pow
er Button Component Styles */
.power-button-container {
  width: 7em;
  height: 7em;
  position: relative;
}

.power-button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #090909;
  background-color: transparent;
  background-image: linear-gradient(145deg, #171717, #444245);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #7d7c7e, inset -2px -2px 0px #1c1c1c;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.power-button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #262626, #606060);
  z-index: -1;
  box-shadow: 11px 11px 22px #141414, -11px -11px 22px #525252;
}

.power-button .power-icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.power-button .power-icon svg {
  height: 100%;
  width: 100%;
  fill: #a5a5a5;
  transition: fill 0.3s ease;
}

.power-button-container input:checked+.power-button {
  box-shadow: inset -2px -2px 0 #5e5e5e, inset 2px 2px 0 #1c1c1c;
  border: 4px solid rgba(255, 77, 77, 0.281);
  animation: animePowerBorder 0.3s linear alternate-reverse infinite;
}

.power-button-container input:checked+.power-button .power-icon svg {
  fill: rgb(255, 77, 77);
  animation: animePowerFill 0.3s linear alternate-reverse infinite;
}

@keyframes animePowerFill {
  to {
    fill: rgba(255, 77, 77, 0.642);
  }
}

@keyframes animePowerBorder {
  to {
    border-color: rgba(255, 77, 77, 0.137);
  }
}

* Switch Button Component Styles */ .switch-button-container {
  width: 7em;
  height: 7em;
  position: relative;
}

.switch-button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #e5e5e5;
  background-color: white;
  background-image: linear-gradient(145deg, #ffffff, #f5f5f5);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #ffffff, inset -2px -2px 0px #d0d0d0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.switch-button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #f8f8f8, #e0e0e0);
  z-index: -1;
  box-shadow: 11px 11px 22px #c0c0c0, -11px -11px 22px #ffffff;
}

.switch-button .switch-icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.switch-button .switch-icon svg {
  height: 100%;
  width: 100%;
  fill: #666666;
  transition: fill 0.3s ease;
}

.switch-button-container input:checked+.switch-button {
  background-color: #22c55e;
  background-image: linear-gradient(145deg, #22c55e, #16a34a);
  border: 4px solid #15803d;
  box-shadow:
    inset 2px 2px 0 #34d399,
    inset -2px -2px 0px #166534,
    0px 0px 20px rgba(34, 197, 94, 0.5);
}

.switch-button-container input:checked+.switch-button::before {
  background-image: linear-gradient(145deg, #22c55e, #16a34a);
  box-shadow: 11px 11px 22px rgba(34, 197, 94, 0.3), -11px -11px 22px rgba(34, 197, 94, 0.1);
}

.switch-button-container input:checked+.switch-button .switch-icon svg {
  fill: white;
}