 Ziel: Halte dich ausschließlich an Best-Practice-Regeln für den unten aufgeführten Tech-Stack.

1. Allgemeine Code-Qualität  
- TypeScript strict mode, noImplicitAny, exactOptionalPropertyTypes  
- ESLint: airbnb-base + @typescript-eslint + eslint-plugin-react + eslint-plugin-tailwindcss  
- Prettier: printWidth 80, singleQuote true, trailingComma all  
- Pfad-Alias (tsconfig): @main, @preload, @renderer, @shared  
- Commit-Hooks: husky + lint-staged → „npm run lint && npm run test“ muss grün sein  
- Conventional Commits + semantic-release für automatisches Versioning  

2. Electron (≥ v30) + Vite (≥ 5)  
- security: contextIsolation true, sandbox true, nodeIntegration false  
- preload: ausschließlich whiteliste APIs via contextBridge.exposeInMainWorld  
- Haupt-/Renderer-Prozess strikt trennen; kein Zugriff vom Renderer auf Node-APIs  
- env-Variablen über import.meta.env oder dotenv-expand laden (nie im Code hardcodieren)  
- Vite-Alias: { electron: '┆main/index.ts', preload: '┆preload/index.ts' }  
- use worker_threads für CPU-intensive Jobs statt blockierendem Main Process  
- sourcemap: inline im Dev-Mode, hidden im Prod-Build  
- auto-reload: vite-plugin-electron-renderer anstelle von electron-reload  

3. React (≥ 19.1) + TypeScript  
- Komponenten als Function Components; Hooks first-class (keine Klassen)  
- Dateinamen PascalCase.tsx, Hooks useCamelCase.ts  
- slice-based Feature-Ordner (z. B. features/todos) → UI, hooks, model in einem Verzeichnis  
- Zustand: Zustand oder Jotai; vermeide Redux-Boilerplate  
- Error Boundary pro Root-Layout  
- Suspense + React.lazy für Code-Splitting; lade modale Dialoge und Heavy-Charts asynchron  
- Tests: Vitest + Testing-Library; Hook-Tests via renderHook, Komponententests via vi.mock  

4. Tailwind CSS (v4)  
- nutze CSS-Variablen im :root\; Theme-Switch über data-attribute [data-theme="dark"]  
- Konfiguration minimal halten; erlaube nur custom colors / radius via @theme-Block  
- twMerge oder clsx zum Mergen dynamischer Klassen (nie string-concat)  
- Utility-First: keine custom SCSS außer für Global-Reset/Scrollbar etc.  
- Responsive: bevorzugt mobile-first, z. B. `p-4 sm:p-6 lg:p-8`  
- Dark Mode: `bg-neutral-100 dark:bg-neutral-900` statt eigene Klassen  

5. shadcn/ui  
- Komponenten via CLI kopieren, niemals als npm-Dependencie importieren  
- Varianten via class-variance-authority (cva), nicht via switch-Statements  
- Bei Anpassungen Utility-Klassen statt Inline-Styles verwenden  
- Barrierefreiheit: verwende die mitgelieferten aria-Attribute unverändert  
- Icons: lucide-react; immer mit `aria-hidden="true"` wenn rein dekorativ  

6. Prisma (≥ 6) + SQLite 3  
- Schema-Datei prisma/schema.prisma, File-Path `../../../data/app.db` relativ halten  
- naming: Tabellen snake_case, Felder camelCase; map-Attribute für Legacy-Felder  
- jede DB-Änderung über `prisma migrate`; dev.db niemals committen  
- `prisma.$transaction()` für Mehrfachschreibvorgänge  
- Seeding über prisma/seed.ts; verwende Faker v9 und `await prisma.$disconnect()` am Ende  
- SQLite-Leistung: `journal_mode = WAL`, `cache_size = -64000` in `PRAGMA` beim App-Start  
- für Full-Text-Suche: FTS5-Virtual-Table + `prisma.$queryRaw`  

7. Tests & Qualität  
- unit → Vitest (Coverage ≥ 80 %), e2e → Playwright Desktop (Chromium+WebKit)  
- Datenbank-Tests: sqlite-memory + sqlite-jest-wrapper oder prisma-testing-lib  
- CI: GitHub Actions caches node_modules & ~/.cache/electron; build & test Matrix (win-latest, ubuntu-latest, macos-latest)  
- CodeQL workflow aktivieren (GitHub Advanced Security)  

8. Build & Release  
- electron-vite build --minify true; Sourcemaps nur für Sentry Upload  
- Signieren: macOS notch `CSC_LINK`, Windows signtool; Werte als Secrets  
- Auto-Updater: electron-builder + GitHub Releases; disable in dev  
- Artifacts: < 80 MB dmg/exe, < 25 MB .AppImage  

9 . Akzeptanzkriterien
- Startskript npm run dev
- Lighthouse Desktop-Score ≥ 90 (renderer App as SPA).
- Kein ESLint-/Prettier-/Playwright-Fehler im CI-Log.

10 . Ressourcen / Docs
- Electron-Vite Guide 2025-03-16: https://electron-vite.org/guide/
- Prisma SQLite Dataguide 2025-01-01: https://www.prisma.io/dataguide/sqlite
- Tailwind v4 Inline-Theming RFC: https://tailwindcss.com/blog/tailwindcss-v4-1
- shadcn/ui Best Practices 2025-06-18: https://insight.akarinti.tech/best-practices-for-using-shadcn-ui-in-next-js-2134108553ae