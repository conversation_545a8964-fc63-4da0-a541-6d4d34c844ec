"use client"

import { TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, Cell, LabelList, <PERSON><PERSON><PERSON>s, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { TruckIcon } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from "@/components/ui/chart"

interface CSRChartProps {
  title: string
  description: string
  data: Array<{
    date: string
    value: number
    label?: string
  }>
  dataKey: string
  unit: string
  trend?: {
    value: number
    isPositive: boolean
  }
}

const chartConfig = {
  value: {
    label: "Wert",
    color: "hsl(221.2 83.2% 53.3%)", // Blau als Standard
  },
} satisfies ChartConfig

export function CSRChartTransport({ title, description, data, dataKey, unit, trend }: CSRChartProps) {
  return (
    <Card className="mb-8 group border-[#ff7a05]/30 bg-blue-500/5 hover:border-[#ff7a05] relative flex cursor-pointer flex-col overflow-hidden rounded-xl border shadow-xl transition-all duration-500">
      {/* Bento Grid Pattern Background */}
      <div className="absolute top-0 right-0 z-0 w-1/3 h-1/2 cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_80%_60%_at_80%_20%,#000_60%,transparent_90%)] bg-[size:16px_16px] bg-white"></div>

      {/* Large Background Icon */}
      <div className="text-blue-500/30 group-hover:text-blue-500 absolute right-1 bottom-3 scale-[4] transition-all duration-700 group-hover:scale-[4.2]">
        <TruckIcon className="size-6" />
      </div>

      <div className="relative z-10 flex flex-col">
        <CardHeader className="pb-1 pt-0">
          <CardTitle className="text-lg font-heading">{title}</CardTitle>
          <CardDescription className="text-xs">{description}</CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <ChartContainer config={chartConfig} className="h-[250px] w-[550px]">
            <BarChart accessibilityLayer data={data} margin={{ top: 15, right: 15, bottom: 15, left: 15 }}>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="date"
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                angle={-45}
                textAnchor="end"
                height={27}
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  try {
                    // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                    const date = new Date(value);
                    if (!isNaN(date.getTime())) {
                      return date.toLocaleDateString('de-DE', {
                        day: '2-digit',
                        month: '2-digit'
                      });
                    }
                    // Falls es kein gültiges Datum ist, zeige den String direkt
                    return String(value);
                  } catch {
                    return value;
                  }
                }}
              />
              <YAxis
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                axisLine={{ stroke: "#000000", strokeWidth: 2 }}
                tickFormatter={(value) => `${value}${unit}`}
              />
              <ChartTooltip
                content={<ChartTooltipContent
                  labelClassName="font-bold"
                  hideIndicator
                  formatter={(value, name) => [
                    `${value}${unit}`,
                    name
                  ]}
                />}
              />
              <Bar
                dataKey="value"
                radius={[4, 4, 0, 0]}
                stroke="#000000"
                strokeWidth={2}
              >
                <LabelList
                  position="top"
                  dataKey="value"
                  fillOpacity={1}
                  formatter={(value: number) => `${value}${unit}`}
                />
                {data.map((item, index) => (
                  <Cell
                    key={index}
                    fill={item.value >= 0 ? "hsl(142.1 76.2% 36.3%)" : "hsl(346.8 77.2% 49.8%)"}
                  />
                ))}
              </Bar>
            </BarChart>
          </ChartContainer>
        </CardContent>

        {trend && (
          <CardFooter className="flex-col items-start gap-1 text-xs pt-1 pb-2">
            <div className="flex gap-1 leading-none font-medium text-xs">
              {trend.isPositive ? (
                <>
                  Trend +{Math.abs(trend.value)}%
                  <TrendingUp className="h-3 w-3 text-green-500" />
                </>
              ) : (
                <>
                  Trend -{Math.abs(trend.value)}%
                  <TrendingDown className="h-3 w-3 text-red-500" />
                </>
              )}
            </div>
          </CardFooter>
        )}
      </div>

      {/* Bottom Gradient Bar */}
      <div className="from-purple-500 to-purple-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
    </Card>
  )
}