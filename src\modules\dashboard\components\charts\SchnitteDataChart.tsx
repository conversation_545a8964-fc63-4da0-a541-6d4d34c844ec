import React, { useEffect, useState, memo } from "react";
import apiService from "@/services/api.service";

// Definition des Datentyps für die Schnitte-Daten
interface SchnitteDataPoint {
  id?: number;
  Datum: string;
  M5_R_H1: number | null;
  M6_T_H1: number | null;
  M7_R_H1: number | null;
  M8_T_H1: number | null;
  M9_R_H1: number | null;
  M10_T_H1: number | null;
  M11_R_H1: number | null;
  M12_T_H1: number | null;
  M13_R_H1: number | null;
  M14_T_H1: number | null;
  M15_R_H1: number | null;
  M16_T_H1: number | null;
  M17_R_H1: number | null;
  M18_T_H1: number | null;
  M19_T_H1: number | null;
  M20_T_H1: number | null;
  M21_R_H1: number | null;
  M23_T_H1: number | null;
  M25_RR_H1: number | null;
  M26_T_H1: number | null;
  Sum_H1: number | null;
  M1_T_H3: number | null;
  M2_T_H3: number | null;
  M3_R_H3: number | null;
  M4_T_H3: number | null;
  M22_T_H3: number | null;
  M24_T_H3: number | null;
  M27_R_H3: number | null;
  Sum_H3: number | null;
}

import {
  // BarChart, // Aktuell nicht verwendet
  // Bar, // Aktuell nicht verwendet
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart,
  Line,
} from "recharts";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

/**
 * SchnitteDataChart Komponente
 * 
 * Zeigt die Schnittemaschinen-Kennzahlen als Liniendiagramm an.
 * Verwendet Daten aus der schnitte-Tabelle mit allen M-Spalten
 * 
 * Die X-Achse zeigt das Datum, die Y-Achse die Summen-Werte (Sum-H1 und Sum-H3)
 */

// Definition des Datentyps für die Chart-Daten
interface ChartDataPoint {
  name: string;
  date?: Date; // Hilfsdatum für die Sortierung
  "Sum-H1": number;
  "Sum-H3": number;
  total: number; // Gesamtsumme für bessere Übersicht
}

interface SchnitteDataChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: ChartDataPoint[];
  // Datumsbereich für die Filterung der Daten
  dateRange?: DateRange;
}
  
/**
 * Filtert die Daten nach dem angegebenen Datumsbereich
 * 
 * @param data Die zu filternden Daten
 * @param dateRange Der Datumsbereich für die Filterung
 * @returns Die gefilterten Daten
 */
const filterDataByDateRange = (data: ChartDataPoint[], dateRange: DateRange): ChartDataPoint[] => {
  if (!dateRange.from || !dateRange.to) return data;
  
  return data.filter(item => {
    // Wenn das Item kein Datum hat, können wir es nicht filtern
    if (!item.date) return true;
    
    // Überprüfe, ob das Datum im angegebenen Bereich liegt
    try {
      return isWithinInterval(item.date, {
        start: dateRange.from as Date, // Explizite Typumwandlung
        end: dateRange.to as Date // Explizite Typumwandlung
      });
    } catch (error) {
          return true; // Im Fehlerfall das Item beibehalten
        }
  });
};

export const SchnitteDataChart = memo(function SchnitteDataChart({ data: propData, dateRange }: SchnitteDataChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    "Sum-H1": {
      label: "Summe H1",
      color: "var(--chart-1)", // Kräftige Farbe im Neobrutalism-Stil
    },
    "Sum-H3": {
      label: "Summe H3",
      color: "var(--chart-2)",
    },
    total: {
      label: "Gesamt",
      color: "var(--chart-3)",
    }
  };

  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      // Filtere die Daten nach dem Datumsbereich, wenn vorhanden
      const filteredData = dateRange ? filterDataByDateRange(propData, dateRange) : propData;
      setChartData(filteredData);
    } else {
      // Lade Daten aus der Datenbank
      loadData();
    }
  }, [propData, dateRange]);

  /**
   * Lädt die Daten aus der schnitte-Tabelle der Datenbank
   * und filtert sie nach dem angegebenen Datumsbereich
   */
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.getSchnitteData();
      
      if (!result) {
        setChartData([]);
        setError('Keine Daten in der schnitte-Tabelle gefunden');
        return;
      }
        
      let processedData: ChartDataPoint[] = (result as SchnitteDataPoint[]).map((row: SchnitteDataPoint) => {
        let formattedDate = 'Unbekannt';
        let dateObj = new Date(0);
        
        if (row.Datum) {
          try {
            dateObj = new Date(row.Datum);
            formattedDate = dateObj.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
          } catch (e) {
            // Fehler beim Formatieren des Datums ignorieren
          }
        }
        
        const sumH1 = row.Sum_H1 ?? 0;
        const sumH3 = row.Sum_H3 ?? 0;
        
        return {
          name: formattedDate,
          date: dateObj,
          "Sum-H1": sumH1,
          "Sum-H3": sumH3,
          total: sumH1 + sumH3,
        };
      });
      
      processedData.sort((a, b) => (a.date?.getTime() ?? 0) - (b.date?.getTime() ?? 0));
      
      if (dateRange && dateRange.from && dateRange.to) {
        processedData = processedData.filter(item =>
          item.date && isWithinInterval(item.date, { start: dateRange.from!, end: dateRange.to! })
        );
      }
      
      const finalData: ChartDataPoint[] = processedData.map(item => ({
        name: item.name,
        "Sum-H1": item["Sum-H1"],
        "Sum-H3": item["Sum-H3"],
        total: item.total,
      }));
      
      setChartData(finalData || []);
    } catch (err) {
      setChartData([]);
      setError('Fehler beim Laden der Daten: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Berechne Gesamtwerte und Durchschnitte für den Footer
  const totalH1 = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item["Sum-H1"] || 0), 0) : 0;
  const totalH3 = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item["Sum-H3"] || 0), 0) : 0;
  const totalAll = totalH1 + totalH3;
  const avgH1 = chartData.length > 0 ? totalH1 / chartData.length : 0;
  const avgH3 = chartData.length > 0 ? totalH3 / chartData.length : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="text-red-500 font-bold">{error}</p>
        <p className="mt-2">{t("check_database_connection")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>SCHNITTE</CardTitle>
        <CardDescription>
          Anzahl der Schnitte gesamt Halle 1 und Halle 3
        </CardDescription>
      </CardHeader>
      <CardContent>
          <ChartContainer
            config={chartConfig}
            className="h-60 w-full"
          >
            <LineChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="name" 
                className="text-xs font-bold"
                tickLine={false}
                axisLine={false}
                tickMargin={1}
                height={30}
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  try {
                    // Formatiere das Datum als TT.MM
                    return String(value);
                  } catch {
                    return value;
                  }
                }}
              />
              <YAxis 
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                axisLine={{ stroke: "#000000", strokeWidth: 2 }}
                // Beschriftung für die Y-Achse
                label={{ 
                  value: "Anzahl Schnitte", 
                  angle: -90, 
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                  offset: -5
                }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelClassName="font-bold"
                    labelFormatter={(label) => `Datum: ${label}`}
                  />
                }
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Line
                type="monotone"
                dataKey="Sum-H1"
                name="Summe H1"
                fill={chartConfig["Sum-H1"].color}
                stroke={chartConfig["Sum-H1"].color}
                strokeWidth={3}
                dot={{ fill: "var(--chart-1)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
              />
              <Line
                type="monotone"
                dataKey="Sum-H3"
                name="Summe H3"
                fill={chartConfig["Sum-H3"].color}
                stroke={chartConfig["Sum-H3"].color}
                strokeWidth={3}
                dot={{ fill: "var(--chart-2)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
              />
              <Line
                type="monotone"
                dataKey="total"
                name="Gesamt"
                stroke={chartConfig.total.color}
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: "var(--chart-3)", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 5, stroke: "#000000", strokeWidth: 2 }}
              />
            </LineChart>
          </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex flex-col w-full text-sm">
          <div className="font-medium mb-1">Durchschnittliche Schitte über die ausgewählte Zeitperiode:</div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center">
              <span className="text-black">H1: {chartData.length > 0 ? avgH1.toFixed(1) : 'N/A'} | </span>
            </div>
            <div className="flex items-center">
              <span className="text-black">H3: {chartData.length > 0 ? avgH3.toFixed(1) : 'N/A'} | </span>
            </div>
            <div className="flex items-center">
              <span className="text-black">Gesamt: {chartData.length > 0 ? totalAll : 0}</span>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});