import React, { useState, useEffect } from 'react';
import { Play, Clock, CheckCircle, AlertTriangle, Database, Mail, FileSpreadsheet, Settings, Save, X, Calendar } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
// Card-Wrapper entfernen, damit die Komponente im Drawer sauber wirkt
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import CustomAccordion from './accordionWorkflow';
import { SAPWorkflowProcess, WorkflowConfig, WorkflowSchedule } from '@/types/workflow';
import { workflowService } from '@/services/workflowService';

interface ServicegradWorkflowCardProps {
  process: SAPWorkflowProcess;
  onExecute: (processId: string) => Promise<void>;
  isExecuting: boolean;
  initialTab?: 'overview' | 'settings';
}

// Default-Konfiguration für Servicegrad
const defaultConfig: WorkflowConfig = {
  id: 'servicegrad',
  name: 'Servicegrad Automatisierung',
  description: 'SAP Servicegrad Export mit automatischer Excel-Verarbeitung',
  databasePath: 'C:\\Users\\<USER>\\OneDrive\\Desktop\\Neuer Ordner\\sfm_dashboard.db',
  exportPath: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\SG',
  emailRecipients: ['<EMAIL>'],
  // SAP-Konfiguration
  sapExecutablePath: 'C:\\Program Files (x86)\\SAP\\FrontEnd\\SapGui\\sapshcut.exe',
  sapSystemId: 'PS4',
  sapClient: '009',
  sapLanguage: 'DE',
  dbPath: 'C:\\Users\\<USER>\\OneDrive\\Desktop\\Neuer Ordner\\sfm_dashboard.db',
  schedule: {
    enabled: false,
    frequency: 'daily',
    time: '08:00',
    dayOfWeek: 1,
    interval: 1
  },
  isActive: true,
  lastModified: new Date()
};

export function ServicegradWorkflowCard({ process, onExecute, isExecuting, initialTab = 'overview' }: ServicegradWorkflowCardProps) {
  const [executionProgress, setExecutionProgress] = useState(0);
  const [showSettings, setShowSettings] = useState(initialTab === 'settings');
  const [config, setConfig] = useState<WorkflowConfig>(defaultConfig);
  const [tempConfig, setTempConfig] = useState<WorkflowConfig>(defaultConfig);
  const [activeSettingsTab, setActiveSettingsTab] = useState<string>('general');

  // Lade Konfiguration beim Mount
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const loadedConfig = await workflowService.getWorkflowConfig(process.id);
      if (loadedConfig) {
        setConfig({ ...defaultConfig, ...loadedConfig });
        setTempConfig({ ...defaultConfig, ...loadedConfig });
      }
    } catch (error) {
      console.error('Fehler beim Laden der Workflow-Konfiguration:', error);
    }
  };

  const saveConfig = async () => {
    try {
      await workflowService.updateWorkflowConfig(process.id, tempConfig);
      setConfig(tempConfig);
      setShowSettings(false);
      console.log('Workflow-Konfiguration gespeichert:', tempConfig);
    } catch (error) {
      console.error('Fehler beim Speichern der Workflow-Konfiguration:', error);
    }
  };

  const cancelSettings = () => {
    setTempConfig(config);
    setShowSettings(false);
  };

  const getStatusIcon = () => {
    switch (process.status) {
      case 'running':
        return <Clock className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Database className="h-5 w-5 text-gray-400" />;
    }
  };

  const handleExecute = async () => {
    setExecutionProgress(0);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setExecutionProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 10;
      });
    }, 2000);

    try {
      await onExecute(process.id);
      setExecutionProgress(100);
    } catch (error) {
      console.error('Execution failed:', error);
    } finally {
      clearInterval(progressInterval);
      setTimeout(() => setExecutionProgress(0), 2000);
    }
  };

  const renderScheduleSettings = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label htmlFor="schedule-enabled" className="text-sm font-medium">
          Automatische Ausführung
        </Label>
        <Switch
          id="schedule-enabled"
          checked={tempConfig.schedule.enabled}
          onCheckedChange={(checked) =>
            setTempConfig(prev => ({
              ...prev,
              schedule: { ...prev.schedule, enabled: checked }
            }))
          }
        />
      </div>

      {tempConfig.schedule.enabled && (
        <>
          <div className="space-y-2">
            <Label htmlFor="frequency">Frequenz</Label>
            <Select
              value={tempConfig.schedule.frequency}
              onValueChange={(value: 'hourly' | 'daily' | 'weekly' | 'weekdays') =>
                setTempConfig(prev => {
                  // Map 'weekdays' (UI only) to 'weekly' (type-safe)
                  const nextFrequency: 'hourly' | 'daily' | 'weekly' =
                    value === 'weekdays' ? 'weekly' : value;

                  // Keep WorkflowConfig.schedule.dayOfWeek strictly number | undefined
                  // If previously an array snuck in, normalize to undefined or a single number
                  let normalizedDayOfWeek: number | undefined = prev.schedule?.dayOfWeek as any;

                  if (Array.isArray(normalizedDayOfWeek)) {
                    // choose first selected weekday if present, otherwise unset
                    normalizedDayOfWeek = normalizedDayOfWeek.length > 0 ? normalizedDayOfWeek[0] as number : undefined;
                  }

                  return {
                    ...prev,
                    schedule: {
                      ...prev.schedule,
                      frequency: nextFrequency,
                      dayOfWeek: normalizedDayOfWeek
                    }
                  };
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Stündlich</SelectItem>
                <SelectItem value="daily">Täglich</SelectItem>
                <SelectItem value="weekly">Wöchentlich</SelectItem>
                <SelectItem value="weekdays">Wochentage</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {tempConfig.schedule.frequency === 'hourly' && (
            <div className="space-y-2">
              <Label htmlFor="interval">Alle X Stunden</Label>
              <Input
                id="interval"
                type="number"
                min="1"
                max="24"
                value={tempConfig.schedule.interval || 1}
                onChange={(e) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, interval: parseInt(e.target.value) || 1 }
                  }))
                }
              />
            </div>
          )}

          {(['daily', 'weekly', 'weekdays'] as const).includes(tempConfig.schedule.frequency as any) && (
            <div className="space-y-2">
              <Label htmlFor="time">Uhrzeit</Label>
              <Input
                id="time"
                type="time"
                value={tempConfig.schedule.time || '08:00'}
                onChange={(e) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, time: e.target.value }
                  }))
                }
              />
            </div>
          )}

          {tempConfig.schedule.frequency === 'weekly' && (
            <div className="space-y-2">
              <Label htmlFor="dayOfWeek">Wochentag</Label>
              <Select
                value={tempConfig.schedule.dayOfWeek?.toString() || '1'}
                onValueChange={(value) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, dayOfWeek: parseInt(value) }
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Montag</SelectItem>
                  <SelectItem value="2">Dienstag</SelectItem>
                  <SelectItem value="3">Mittwoch</SelectItem>
                  <SelectItem value="4">Donnerstag</SelectItem>
                  <SelectItem value="5">Freitag</SelectItem>
                  <SelectItem value="6">Samstag</SelectItem>
                  <SelectItem value="0">Sonntag</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          {tempConfig.schedule.frequency === 'weekly' && (
            <div className="space-y-2">
              <Label>Wochentage</Label>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                {[
                  { label: "Montag", value: 1 },
                  { label: "Dienstag", value: 2 },
                  { label: "Mittwoch", value: 3 },
                  { label: "Donnerstag", value: 4 },
                  { label: "Freitag", value: 5 },
                  { label: "Samstag", value: 6 },
                  { label: "Sonntag", value: 0 },
                ].map((d) => {
                  const selected = Array.isArray((tempConfig.schedule as any).daysOfWeek)
                    ? (tempConfig.schedule as any).daysOfWeek.includes(d.value)
                    : false
                  return (
                    <button
                      key={d.value}
                      type="button"
                      className={`px-3 py-2 rounded-md border text-sm transition-colors ${
                        selected ? "bg-indigo-600 text-white border-indigo-600" : "bg-white text-gray-700 hover:bg-gray-50"
                      }`}
                      onClick={() =>
                        setTempConfig(prev => {
                          const current = Array.isArray((prev.schedule as any).daysOfWeek) ? (prev.schedule as any).daysOfWeek : []
                          const exists = current.includes(d.value)
                          const next = exists ? current.filter((v: number) => v !== d.value) : [...current, d.value]
                          return {
                            ...prev,
                            schedule: { ...(prev.schedule as any), daysOfWeek: next }
                          }
                        })
                      }
                    >
                      {d.label}
                    </button>
                  )
                })}
              </div>
              <p className="text-xs text-muted-foreground">Mehrfachauswahl möglich</p>
            </div>
          )}
        </>
      )}
    </div>
  );

  return (
    <div className="border-0 transition-all duration-200">
      <div className="px-6 pb-6 space-y-6">
        {/* Settings Panel */}
        {showSettings && (
          <>
            {/* Tabs oben lassen */}
            <Tabs value={activeSettingsTab} onValueChange={setActiveSettingsTab} className="flex flex-col gap-3">
              {/* Tabs-Bereich: weißer Hintergrund, nur der aktive Tab bekommt die farbige BG */}
              <TabsList className="bg-white p-1 rounded-lg border shadow-sm flex justify-center">
                <div className="inline-flex gap-1">
                  <TabsTrigger
                    value="general"
                    className="data-[state=active]:bg-blue-200 data-[state=active]:text-black data-[state=active]:shadow-sm"
                  >
                    SAP Prozess
                  </TabsTrigger>
                  <TabsTrigger
                    value="schedule"
                    className="data-[state=active]:bg-purple-200 data-[state=active]:text-black data-[state=active]:shadow-sm"
                  >
                    Zeitplan
                  </TabsTrigger>
                  <TabsTrigger
                    value="email"
                    className="data-[state=active]:bg-green-200 data-[state=active]:text-black data-[state=active]:shadow-sm"
                  >
                    E-Mail
                  </TabsTrigger>
                </div>
              </TabsList>

              {/* Card mit Accordion-Inhalt je Tab */}
              <div className="bg-white border rounded-lg p-4">
                <TabsContent value="general">
                  <CustomAccordion
                    sections={[
                      {
                        id: "eingabe",
                        title: "SAP /LSGIT/VS_DLV_CHECK Eingabe",
                        content: (
                          <div className="space-y-3 py-2">
                            <div className="space-y-2">
                              <Label htmlFor="eingabe-modus">Ladedatum</Label>
                              <Select
                                value={(tempConfig as any)?.customInputMode === "custom" ? "custom" : "default"}
                                onValueChange={(value: "default" | "custom") =>
                                  setTempConfig((prev) => ({
                                    ...prev,
                                    customInputMode: value,
                                  }) as any)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Modus wählen" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="default">Standard (Workflow-Standard nutzen)</SelectItem>
                                  <SelectItem value="custom">Benutzerdefiniert</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            { (tempConfig as any)?.customInputMode === "custom" && (
                              <div className="space-y-2">
                                <Label htmlFor="eingabe-datum">Ladedatum</Label>
                                <Input
                                  id="eingabe-datum"
                                  type="date"
                                  value={(tempConfig as any)?.customInputDate || ""}
                                  onChange={(e) =>
                                    setTempConfig((prev) => ({
                                      ...prev,
                                      customInputDate: e.target.value,
                                    }) as any)
                                  }
                                  placeholder="YYYY-MM-DD"
                                />
                              </div>
                            )}
                            { (tempConfig as any)?.customInputMode !== "custom" && (
                              <div className="text-xs text-gray-500">
                                Es wird der Standardwert aus dem Workflow-Prozess verwendet.
                              </div>
                            )}
                          </div>
                        ),
                        defaultOpen: true,
                      },
                      {
                        id: "export",
                        title: "SAP-Excel Export",
                        content: (
                          <div className="space-y-2 py-2">
                            <Label htmlFor="export-path">Verzeichnis</Label>
                            <Input
                              id="export-path"
                              value={tempConfig.exportPath || ""}
                              onChange={(e) =>
                                setTempConfig((prev) => ({ ...prev, exportPath: e.target.value }))
                              }
                              placeholder="\\adsgroup\Group\..."
                            />
                          </div>
                        ),
                      },
                      // Email-Einstellungen aus General entfernen und in eigenen Tab verschieben
                      {
                        id: "sap-general",
                        title: "SAP Grundeinstellungen",
                        content: (
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div className="space-y-2 md:col-span-3 py-2">
                              <Label htmlFor="sap-exe">SAP Shortcut Pfad</Label>
                              <Input
                                id="sap-exe"
                                value={tempConfig.sapExecutablePath || ""}
                                onChange={(e) =>
                                  setTempConfig((prev) => ({ ...prev, sapExecutablePath: e.target.value }))
                                }
                                placeholder="C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="sap-system">System-ID</Label>
                              <Input
                                id="sap-system"
                                value={tempConfig.sapSystemId || ""}
                                onChange={(e) =>
                                  setTempConfig((prev) => ({ ...prev, sapSystemId: e.target.value }))
                                }
                                placeholder="PS4"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="sap-client">Client</Label>
                              <Input
                                id="sap-client"
                                value={tempConfig.sapClient || ""}
                                onChange={(e) =>
                                  setTempConfig((prev) => ({ ...prev, sapClient: e.target.value }))
                                }
                                placeholder="009"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="sap-language">Sprache</Label>
                              <Input
                                id="sap-language"
                                value={tempConfig.sapLanguage || ""}
                                onChange={(e) =>
                                  setTempConfig((prev) => ({ ...prev, sapLanguage: e.target.value }))
                                }
                                placeholder="DE"
                              />
                            </div>
                          </div>
                        ),
                      },
                    ]}
                  />
                </TabsContent>

                <TabsContent value="schedule">
                  <div className="space-y-3">
                    {renderScheduleSettings()}
                  </div>
                </TabsContent>

                <TabsContent value="sap">
                  <div className="space-y-3">
                    {/* SAP-spezifische Felder, weiter unten im Accordion abbildbar, hier direkt belassen */}
                    <div className="space-y-2">
                      <Label htmlFor="sap-exe">SAP Shortcut Pfad</Label>
                      <Input
                        id="sap-exe"
                        value={tempConfig.sapExecutablePath || ''}
                        onChange={(e) =>
                          setTempConfig(prev => ({ ...prev, sapExecutablePath: e.target.value }))
                        }
                        placeholder="C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="space-y-2">
                        <Label htmlFor="sap-system">System-ID</Label>
                        <Input
                          id="sap-system"
                          value={tempConfig.sapSystemId || ''}
                          onChange={(e) =>
                            setTempConfig(prev => ({ ...prev, sapSystemId: e.target.value }))
                          }
                          placeholder="PS4"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="sap-client">Client</Label>
                        <Input
                          id="sap-client"
                          value={tempConfig.sapClient || ''}
                          onChange={(e) =>
                            setTempConfig(prev => ({ ...prev, sapClient: e.target.value }))
                          }
                          placeholder="009"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="sap-language">Sprache</Label>
                        <Input
                          id="sap-language"
                          value={tempConfig.sapLanguage || ''}
                          onChange={(e) =>
                            setTempConfig(prev => ({ ...prev, sapLanguage: e.target.value }))
                          }
                          placeholder="DE"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="db-path">Alternative DB-Pfad</Label>
                      <Input
                        id="db-path"
                        value={tempConfig.dbPath || ''}
                        onChange={(e) =>
                          setTempConfig(prev => ({ ...prev, dbPath: e.target.value }))
                        }
                        placeholder="Pfad zur lokalen DB (optional)"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="email">
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="email-recipients">Empfänger</Label>
                      <Input
                        id="email-recipients"
                        value={(tempConfig.emailRecipients || []).join(", ")}
                        onChange={(e) =>
                          setTempConfig((prev) => ({
                            ...prev,
                            emailRecipients: e.target.value
                              .split(",")
                              .map((s) => s.trim())
                              .filter(Boolean),
                          }))
                        }
                        placeholder="<EMAIL>, <EMAIL>"
                      />
                      <div className="text-xs text-gray-500">
                        Kommagetrennt.
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="email">
                  <div className="space-y-3">
                    <div className="space-y-2 py-4">
                      <Label htmlFor="email-recipients">CC</Label>
                      <Input
                        id="email-recipients"
                        value={(tempConfig.emailRecipients || []).join(", ")}
                        onChange={(e) =>
                          setTempConfig((prev) => ({
                            ...prev,
                            emailRecipients: e.target.value
                              .split(",")
                              .map((s) => s.trim())
                              .filter(Boolean),
                          }))
                        }
                        placeholder="<EMAIL>, <EMAIL>"
                      />
                      <div className="text-xs text-gray-500">
                        Kommagetrennt.
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="email">
                  <div className="space-y-3">
                    <div className="space-y-2 py-4">
                      <Label htmlFor="email-recipients">BCC</Label>
                      <Input
                        id="email-recipients"
                        value={(tempConfig.emailRecipients || []).join(", ")}
                        onChange={(e) =>
                          setTempConfig((prev) => ({
                            ...prev,
                            emailRecipients: e.target.value
                              .split(",")
                              .map((s) => s.trim())
                              .filter(Boolean),
                          }))
                        }
                        placeholder="<EMAIL>, <EMAIL>"
                      />
                      <div className="text-xs text-gray-500">
                        Kommagetrennt.
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </>
        )}

        {/* Workflow Steps */}
        {!showSettings && (
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-800 flex items-center gap-2">
              <FileSpreadsheet className="h-4 w-4" />
              Workflow-Schritte
            </h4>
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <Database className="h-4 w-4 text-blue-500" />
                <span>SAP-Datenexport ({process.tcode})</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <FileSpreadsheet className="h-4 w-4 text-green-500" />
                <span>Excel-Verarbeitung & Berechnung</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <Mail className="h-4 w-4 text-orange-500" />
                <span>E-Mail-Versand mit Bericht</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <Database className="h-4 w-4 text-purple-500" />
                <span>Datenbank-Speicherung</span>
              </div>
            </div>
          </div>
        )}

        {/* Process Details */}
        {!showSettings && (
          <div className="space-y-2 text-sm bg-white p-3 rounded border">
            <div className="flex justify-between">
              <span className="text-gray-600">Ziel-Tabelle:</span>
              <span className="font-mono text-blue-600">{process.dbTable}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Export-Verzeichnis:</span>
              <span className="font-mono text-xs text-gray-500 truncate max-w-[200px]" title={config.exportPath}>
                {config.exportPath}
              </span>
            </div>
            {config.schedule.enabled && (
              <div className="flex justify-between">
                <span className="text-gray-600">Zeitplan:</span>
                <span className="text-green-600">
                  {config.schedule.frequency === 'hourly' && `Alle ${config.schedule.interval}h`}
                  {config.schedule.frequency === 'daily' && `Täglich um ${config.schedule.time}`}
                  {config.schedule.frequency === 'weekly' && `Wöchentlich ${['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'][config.schedule.dayOfWeek || 1]} um ${config.schedule.time}`}
                </span>
              </div>
            )}
            {process.lastRun && (
              <div className="flex justify-between">
                <span className="text-gray-600">Letzter Lauf:</span>
                <span className="text-green-600">{process.lastRun.toLocaleString('de-DE')}</span>
              </div>
            )}
            {process.duration && (
              <div className="flex justify-between">
                <span className="text-gray-600">Dauer:</span>
                <span className="text-blue-600">{Math.round(process.duration / 60)} Min</span>
              </div>
            )}
          </div>
        )}

        {/* Progress Bar (only when running) */}
        {(process.status === 'running' || executionProgress > 0) && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Fortschritt</span>
              <span className="text-blue-600">{Math.round(executionProgress)}%</span>
            </div>
            <Progress value={executionProgress} className="h-2" />
          </div>
        )}

        {/* Export Path (if available) */}
        {process.exportPath && (
          <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            <strong>Letzte Export-Datei:</strong><br />
            <span className="font-mono break-all">{process.exportPath}</span>
          </div>
        )}
      </div>
    </div>
  );
}