import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { DateRange } from 'react-day-picker';
import { Activity, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { SystemStatus, SYSTEM_STATUS_COLORS } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';

interface SystemStatusHeatmapProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

export const SystemStatusHeatmap: React.FC<SystemStatusHeatmapProps> = React.memo(({ 
  dateRange, 
  refreshKey = 0 
}) => {
  const [systemStatuses, setSystemStatuses] = useState<SystemStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSystemStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await stoerungenService.getSystemStatus();
        setSystemStatuses(data);
      } catch (err) {
        console.error('Error fetching system status:', err);
        setError('Fehler beim Laden der Systemdaten');
        
        // Fallback: Empty array - real data should come from backend
        console.warn('SystemStatus API failed, showing empty state. Please ensure the SystemStatus table is populated.');
        setSystemStatuses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSystemStatus();
  }, [refreshKey]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OK':
        return 'bg-green-500';
      case 'WARNING':
        return 'bg-yellow-500';
      case 'ERROR':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OK':
        return <CheckCircle className="h-4 w-4 text-white" />;
      case 'WARNING':
        return <AlertTriangle className="h-4 w-4 text-white" />;
      case 'ERROR':
        return <XCircle className="h-4 w-4 text-white" />;
      default:
        return <Activity className="h-4 w-4 text-white" />;
    }
  };

  const getStatusCount = (status: string) => {
    return systemStatuses.filter(system => system.status === status).length;
  };

  const formatLastCheck = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Gerade eben';
    if (diffMinutes < 60) return `${diffMinutes}m`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h`;
    return `${Math.floor(diffMinutes / 1440)}d`;
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          System Status Heatmap
        </CardTitle>
        
        {/* Status Summary */}
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>OK ({getStatusCount('OK')})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span>Warnung ({getStatusCount('WARNING')})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Fehler ({getStatusCount('ERROR')})</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">{error}</p>
            <p className="text-xs text-yellow-600 mt-1">Zeige Beispieldaten zur Demonstration</p>
          </div>
        )}
        
        {/* Heatmap Grid */}
        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
          {systemStatuses.map((system) => (
            <div
              key={system.id}
              className={`
                relative p-3 rounded-lg shadow-sm border-2 transition-all duration-200 
                hover:shadow-md hover:scale-105 cursor-pointer
                ${getStatusColor(system.status)} 
                ${system.status === 'ERROR' ? 'animate-pulse' : ''}
              `}
              title={`${system.system_name}: ${system.status} (${formatLastCheck(system.last_check)})`}
            >
              <div className="flex flex-col items-center justify-center text-white min-h-[60px]">
                {getStatusIcon(system.status)}
                <span className="text-xs font-medium mt-1 text-center leading-tight">
                  {system.system_name}
                </span>
                <span className="text-xs opacity-80 mt-1">
                  {formatLastCheck(system.last_check)}
                </span>
              </div>
              
              {/* Status indicator border */}
              <div className={`
                absolute inset-0 rounded-lg border-2 
                ${system.status === 'ERROR' ? 'border-red-600' : ''}
                ${system.status === 'WARNING' ? 'border-yellow-600' : ''}
                ${system.status === 'OK' ? 'border-green-600' : ''}
              `}></div>
            </div>
          ))}
        </div>
        
        {systemStatuses.length === 0 && !loading && (
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Keine Systemdaten verfügbar</p>
          </div>
        )}
        
        {/* Legend */}
        <div className="mt-6 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Legende:</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span><strong>OK:</strong> System funktioniert normal</span>
            </div>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span><strong>Warnung:</strong> Leichte Probleme erkannt</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span><strong>Fehler:</strong> System nicht verfügbar</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

SystemStatusHeatmap.displayName = 'SystemStatusHeatmap';