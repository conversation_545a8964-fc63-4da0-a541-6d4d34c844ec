import { Home, Truck, Scissors, Drum, Shell, Warehouse, Factory, PackageCheck } from "lucide-react";
import { NavigationConfig } from "./types";

export const dashboardNavigationConfig: NavigationConfig = {
    menu: [
        {
            title: "Home",
            icon: <Home className="size-5 shrink-0" />,
            to: "/"
        },
        {
            title: "Dashboard",
            icon: <Home className="size-5 shrink-0" />,
            to: "/modules/dashboard"
        },
        {
            title: "Versand",
            icon: <Truck className="size-5 shrink-0" />,
            to: "/modules/dashboard/dispatch",
        },
        {
            title: "Wareneingang",
            // Korrigiertes Icon und Größe
            icon: <PackageCheck className="size-5 shrink-0" />,
            // Konsistente, kleingeschriebene URL wie gefordert
            to: "/modules/dashboard/incoming-goods",
        },
        {
            title: "CSR",
            icon: <Truck className="size-5 shrink-0" />,
            to: "/modules/dashboard/csr",
        },
        {
            title: "Ablängerei",
            icon: <Scissors className="size-5 shrink-0" />,
            to: "#",
            items: [
                {
                    title: "Schnitte",
                    description: "Kennzahlen über Schnitte und Schnittarten",
                    icon: <Scissors className="size-5 shrink-0" />,
                    to: "/modules/dashboard/cutting",
                },
                {
                    title: "Maschinen",
                    description: "Effizienz der Maschinen",
                    icon: <Factory className="size-5 shrink-0" />,
                    to: "/modules/dashboard/machines",
                },
            ],
        },
        {
            title: "Lagerauslastung",
            icon: <Warehouse className="size-5 shrink-0" />,
            to: "#",
            items: [
                {
                    title: "Automatisches Trommellager",
                    description: "Bestände und Bewegungen im Atomatischen Trommellager",
                    icon: <Drum className="size-5 shrink-0" />,
                    to: "/modules/dashboard/atrl",
                },
                {
                    title: "Automatisches Ringlager",
                    description: "Bestände und Bewegungen im Automatischen Ringlager",
                    icon: <Shell className="size-5 shrink-0" />,
                    to: "/modules/dashboard/aril",
                },
            ],
        },
    ]
};