import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { RouterProvider } from "@tanstack/react-router";
import { updateAppLanguage } from "@/helpers/language_helpers";
import { router } from "@/routes/router";
import Toaster from "@/components/ui/toaster";
import { AuthProvider } from "@/contexts/AuthContext";
import "./localization/i18n";

/**
 * Hauptkomponente der Anwendung
 * 
 * Verantwortlich für:
 * - Initialisierung der Sprache
 * - Bereitstellung des Routers
 */
function App() {
  const { i18n } = useTranslation();

  useEffect(() => {
    console.log("🚀 App-Initialisierung gestartet");
    
    // Initialisiere Sprache beim Start
    updateAppLanguage(i18n);
    
    console.log("✅ App vollständig initialisiert und funktional!");
  }, [i18n]);

  return (
    <React.StrictMode>
      <AuthProvider>
        <RouterProvider router={router} />
        <Toaster />
      </AuthProvider>
    </React.StrictMode>
  );
}

export default App;
