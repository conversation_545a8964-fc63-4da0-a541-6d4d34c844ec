/**
 * RAG-Enhanced Chat Service (HTTP-based)
 * 
 * Simplified frontend service that communicates with the backend RAG API
 * via HTTP requests instead of directly instantiating backend services.
 */

import { apiService } from '@/services/api.service';

/**
 * Chat message interface
 */
export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  sources?: Source[];
  confidence?: number;
  usedContext?: string[];
  ragEnhanced?: boolean;
}

/**
 * Source interface
 */
export interface Source {
  id: string;
  title: string;
  content: string;
  similarity: number;
  metadata?: any;
}

/**
 * Chat request interface
 */
export interface ChatRequest {
  message: string;
  useRAG?: boolean;
  includeInsights?: boolean;
  includeAnomalies?: boolean;
  conversationHistory?: ChatMessage[];
}

/**
 * Chat response interface
 */
export interface ChatResponse {
  response: string;
  sources: Source[];
  confidence: number;
  usedContext: string[];
  ragEnhanced: boolean;
  suggestions?: string[];
  metadata?: {
    processingTime: number;
    dataEnrichmentUsed: boolean;
    detectedIntents: any[];
    performanceMetrics: any;
  };
}

/**
 * RAG-Enhanced Chat Service Configuration
 */
export interface RAGChatServiceConfig {
  maxConversationHistory?: number;
  enableConversationContext?: boolean;
  defaultSuggestions?: string[];
  fallbackToBasicChat?: boolean;
}

/**
 * RAG-Enhanced Chat Service (HTTP-based)
 */
export class RAGChatService {
  readonly serviceName = 'RAGChatService';
  private chatConfig: RAGChatServiceConfig;

  constructor(config: RAGChatServiceConfig = {}) {
    this.chatConfig = {
      maxConversationHistory: 10,
      enableConversationContext: true,
      defaultSuggestions: [
        "Zeige mir die aktuellen KPIs",
        "Analysiere die Störungen heute",
        "Optimierungsvorschläge für Versand",
        "Status der Ablängerei"
      ],
      fallbackToBasicChat: true,
      ...config
    };
  }

  /**
   * Initialize the RAG Chat service (HTTP-based)
   */
  async initialize(): Promise<void> {
    // Test connection to RAG backend
    try {
      const response = await fetch('/api/rag/test');
      if (!response.ok) {
        throw new Error('RAG backend not available');
      }
      console.log('RAG Chat service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize RAG Chat service:', error);
      throw error;
    }
  }

  /**
   * Process a chat request with RAG enhancement
   */
  async processChat(request: ChatRequest): Promise<ChatResponse> {
    const startTime = Date.now();

    try {
      // If RAG is disabled, fall back to basic chat
      if (!request.useRAG) {
        return await this.processBasicChat(request);
      }

      // Search for relevant documents first
      const searchResponse = await fetch(
        `/api/rag/documents/search?query=${encodeURIComponent(request.message)}&limit=5`,
        {
          headers: {
            ...(localStorage.getItem('token') && {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            })
          }
        }
      );

      if (!searchResponse.ok) {
        console.warn('RAG search failed, falling back to basic chat');
        return await this.processBasicChat(request);
      }

      const searchData = await searchResponse.json();
      const sources: Source[] = searchData.data.results.map((result: any) => ({
        id: result.chunk.id,
        title: result.chunk.document.title,
        content: result.chunk.content,
        similarity: result.similarity,
        metadata: result.chunk.document
      }));

      // If no good sources found, fall back to basic chat
      if (sources.length === 0 || sources[0].similarity < 0.3) {
        console.warn('No relevant sources found, falling back to basic chat');
        return await this.processBasicChat(request);
      }

      // Build context from sources
      const context = sources.map(source => 
        `Source: ${source.title}\nContent: ${source.content}`
      ).join('\n\n');

      // Call backend chat with RAG context
      const chatResponse = await apiService.post('/chat/enhanced', {
        message: request.message,
        context: context,
        includeInsights: request.includeInsights,
        includeAnomalies: request.includeAnomalies,
        conversationHistory: request.conversationHistory?.slice(-this.chatConfig.maxConversationHistory!)
      });

      const processingTime = Date.now() - startTime;

      return {
        response: chatResponse.response,
        sources: sources,
        confidence: 0.8, // Default confidence for RAG-enhanced responses
        usedContext: sources.map(s => s.title),
        ragEnhanced: true,
        suggestions: this.generateSuggestions(request.message, sources),
        metadata: {
          processingTime,
          dataEnrichmentUsed: true,
          detectedIntents: [],
          performanceMetrics: {
            ragTime: processingTime,
            backendTime: 0
          }
        }
      };

    } catch (error) {
      console.error('RAG processing failed:', error);
      
      // Fallback to basic chat
      if (this.chatConfig.fallbackToBasicChat) {
        console.log('Falling back to basic chat');
        return await this.processBasicChat(request);
      } else {
        throw new Error('RAG processing failed and fallback is disabled');
      }
    }
  }

  /**
   * Process basic chat without RAG enhancement
   */
  private async processBasicChat(request: ChatRequest): Promise<ChatResponse> {
    try {
      const endpoint = request.includeInsights || request.includeAnomalies
        ? '/chat/enhanced'
        : '/chat';

      const response = await apiService.post(endpoint, {
        message: request.message,
        includeInsights: request.includeInsights,
        includeAnomalies: request.includeAnomalies,
        conversationHistory: request.conversationHistory?.slice(-this.chatConfig.maxConversationHistory!)
      });

      return {
        response: response.response,
        sources: [],
        confidence: 0.6, // Lower confidence for non-RAG responses
        usedContext: [],
        ragEnhanced: false,
        suggestions: this.chatConfig.defaultSuggestions,
        metadata: {
          processingTime: 0,
          dataEnrichmentUsed: false,
          detectedIntents: [],
          performanceMetrics: {}
        }
      };

    } catch (error) {
      console.error('Basic chat failed:', error);
      throw new Error('Chat service unavailable');
    }
  }

  /**
   * Generate contextual suggestions based on the message and sources
   */
  private generateSuggestions(message: string, sources: Source[]): string[] {
    const suggestions = [...this.chatConfig.defaultSuggestions!];
    
    // Add source-based suggestions
    if (sources.length > 0) {
      suggestions.push(`Mehr Details zu "${sources[0].title}"`);
      if (sources.length > 1) {
        suggestions.push(`Vergleiche mit "${sources[1].title}"`);
      }
    }

    return suggestions.slice(0, 4); // Limit to 4 suggestions
  }

  /**
   * Get service status
   */
  async getStatus(): Promise<{ isAvailable: boolean; message: string }> {
    try {
      const response = await fetch('/api/rag/test');
      return {
        isAvailable: response.ok,
        message: response.ok ? 'RAG service available' : 'RAG service unavailable'
      };
    } catch (error) {
      return {
        isAvailable: false,
        message: 'RAG service connection failed'
      };
    }
  }
}

export default RAGChatService;