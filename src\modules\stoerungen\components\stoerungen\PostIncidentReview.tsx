import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Stoerung, ActionItem } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { toast } from 'sonner';
import { Save, Plus, Trash2 } from 'lucide-react';

interface PostIncidentReviewProps {
  stoerung: Stoerung;
  onClose: () => void;
}

export const PostIncidentReview: React.FC<PostIncidentReviewProps> = ({ stoerung, onClose }) => {
  const [formData, setFormData] = useState<Partial<Stoerung>>(stoerung);
  const [newActionItem, setNewActionItem] = useState({ beschreibung: '', zustaendig: '', faellig_am: '' });

  const handleSave = async () => {
    try {
      await stoerungenService.updateStoerung(stoerung.id, formData);
      toast.success('Post-Incident Review erfolgreich gespeichert.');
      onClose();
    } catch (error) {
      toast.error('Fehler beim Speichern des Reviews.');
    }
  };

  const handleAddActionItem = () => {
      if(!newActionItem.beschreibung) {
          toast.error('Beschreibung für die Maßnahme ist ein Pflichtfeld.');
          return;
      }
      const newItems = [...(formData.action_items || []), { ...newActionItem, id: Date.now(), stoerung_id: stoerung.id, erledigt: false }];
      setFormData({...formData, action_items: newItems});
      setNewActionItem({ beschreibung: '', zustaendig: '', faellig_am: '' });
  }

  const handleToggleActionItem = (id: number) => {
      const newItems = formData.action_items?.map(item => item.id === id ? {...item, erledigt: !item.erledigt} : item);
      setFormData({...formData, action_items: newItems});
  }

  const handleDeleteActionItem = (id: number) => {
      const newItems = formData.action_items?.filter(item => item.id !== id);
      setFormData({...formData, action_items: newItems});
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Post-Incident Review für: {stoerung.title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="root_cause">Ursachenanalyse (Root Cause)</Label>
          <Textarea id="root_cause" value={formData.root_cause || ''} onChange={(e) => setFormData({...formData, root_cause: e.target.value})} rows={4} />
        </div>
        <div className="space-y-2">
          <Label htmlFor="resolution_steps">Lösungsschritte</Label>
          <Textarea id="resolution_steps" value={formData.resolution_steps || ''} onChange={(e) => setFormData({...formData, resolution_steps: e.target.value})} rows={4} />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lessons_learned">Lessons Learned</Label>
          <Textarea id="lessons_learned" value={formData.lessons_learned || ''} onChange={(e) => setFormData({...formData, lessons_learned: e.target.value})} rows={4} />
        </div>
        <div>
          <h4 className="font-semibold mb-2">Maßnahmen (Action Items)</h4>
          <div className="space-y-2">
              {formData.action_items?.map(item => (
                  <div key={item.id} className="flex items-center gap-2 p-2 border rounded-lg">
                      <Checkbox checked={item.erledigt} onCheckedChange={() => handleToggleActionItem(item.id)} />
                      <div className="flex-grow">
                          <p className={item.erledigt ? 'line-through' : ''}>{item.beschreibung}</p>
                          <p className="text-xs text-gray-500">{item.zustaendig} - Fällig: {item.faellig_am}</p>
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => handleDeleteActionItem(item.id)}><Trash2 className="h-4 w-4 text-red-500"/></Button>
                  </div>
              ))}
          </div>
          <div className="flex gap-2 mt-4">
              <Input placeholder="Neue Maßnahme" value={newActionItem.beschreibung} onChange={(e) => setNewActionItem({...newActionItem, beschreibung: e.target.value})} />
              <Input placeholder="Zuständig" value={newActionItem.zustaendig} onChange={(e) => setNewActionItem({...newActionItem, zustaendig: e.target.value})} />
              <Input type="date" value={newActionItem.faellig_am} onChange={(e) => setNewActionItem({...newActionItem, faellig_am: e.target.value})} />
              <Button onClick={handleAddActionItem}><Plus className="h-4 w-4"/></Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" onClick={onClose}>Abbrechen</Button>
        <Button onClick={handleSave}><Save className="h-4 w-4 mr-2"/>Review speichern</Button>
      </CardFooter>
    </Card>
  );
};