import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { IncomingPositionsChart } from "@/modules/dashboard/components/charts/IncomingPositionsChart";
import { ReturnsChart } from "@/modules/dashboard/components/charts/QMeldungenChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { PackageCheck } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import weColor from "@/assets/iconWe-color.png";
/**
 * Incoming-Goods-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Incoming-Goods-Bereich an:
 * - Eingehende Positionen (WE-Daten) als Balkendiagramm
 * - Retouren als Kreisdiagramm
 */
export default function IncomingGoodsPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 3, 15), // 15. April 2025
    to: new Date(2025, 4, 7), // 7. Mai 2025 (alle verfügbaren Daten)
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center">
          <img
            src={weColor}
            alt="Wareneingang"
            className="h-8 w-8 mr-2 object-contain"
          />
          <h1 className="text-3xl font-bold text-black">WARENEINGANG</h1>
        </div>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          {/* Eingehende Positionen */}
          <div className="xl:col-span-2">
            <ChartErrorBoundary>
              <IncomingPositionsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Retouren */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <ReturnsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}
