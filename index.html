<!doctype html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <!-- Basis für alle relativen Pfade im File-Modus -->
    <base href="./" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Leitstand-Light</title>
    <!-- CSP für Electron: erlaube lokale file:// Assets (Bilder/Schriften) und Dev-Server -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: file:; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:* http://127.0.0.1:*; style-src 'self' 'unsafe-inline' data:; img-src 'self' data: blob: file:; font-src 'self' data: blob: file:; connect-src 'self' data: blob: file: http://localhost:* http://127.0.0.1:* ws://localhost:* ws://127.0.0.1:*;">
    <!-- CSS wird von Vite automatisch injiziert -->
  </head>
  <body>
    <!-- Der Renderer erwartet '#app' laut Fehlermeldung. Passe ID zurück auf 'app'. -->
    <div id="app"></div>
    <script type="module" src="./src/renderer.tsx"></script>
  </body>
</html>
