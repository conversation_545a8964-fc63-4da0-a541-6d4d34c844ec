#!/usr/bin/env pwsh
# Portable Build Script for JOZI1 Lapp Dashboard

Write-Host "Starting portable build process..." -ForegroundColor Green

# 1. Backend vorbereiten (ohne TypeScript-Build) und nur Runtime-Dependencies installieren
Write-Host "Preparing backend (skip tsc)..." -ForegroundColor Yellow
Push-Location backend
# Installiere nur, falls node_modules fehlt oder leer ist
if (-not (Test-Path "node_modules") -or (Get-ChildItem "node_modules" -ErrorAction SilentlyContinue | Measure-Object).Count -eq 0) {
    Write-Host "Installing backend dependencies (production only)..." -ForegroundColor Yellow
    npm ci --omit=dev --silent
} else {
    Write-Host "backend/node_modules already present, skipping install" -ForegroundColor DarkYellow
}
# Prüfe, ob dist existiert – wenn nicht, breche mit Hinweis ab
if (-not (Test-Path "dist")) {
    Write-Host "ERROR: backend/dist nicht gefunden. Bitte ein vorhandenes, kompilertes Backend bereitstellen (dist/)." -ForegroundColor Red
    Pop-Location
    exit 1
}
Pop-Location

# 2. Assets kopieren
Write-Host "Copying assets..." -ForegroundColor Yellow
if (Test-Path "assets") {
    Copy-Item -Path "assets/*" -Destination "public/" -Recurse -Force
}
if (Test-Path "public/*.png") {
    Copy-Item -Path "public/*.png" -Destination "dist/assets/" -Force -ErrorAction SilentlyContinue
}
if (Test-Path "public/*.ico") {
    Copy-Item -Path "public/*.ico" -Destination "dist/assets/" -Force -ErrorAction SilentlyContinue
}

# 3. Frontend bauen
Write-Host "Building frontend..." -ForegroundColor Yellow
npx vite build --config vite.renderer.config.mts
npx vite build --config vite.main.config.ts
npx vite build --config vite.preload.config.ts

# 4. Portable Build erstellen
Write-Host "Creating portable build..." -ForegroundColor Yellow
npx electron-packager . LappDashboard --platform=win32 --arch=x64 --out=portable-build-new --overwrite

# electron-packager erzeugt in der Regel einen Ordner "LappDashboard-win32-x64" (ggf. mit Suffix)
$pkgDir = Get-ChildItem -Directory "portable-build-new" | Where-Object { $_.Name -like "LappDashboard-win32-x64*" } | Select-Object -First 1
if (-not $pkgDir) {
    Write-Host "ERROR: Konnte den erzeugten App-Ordner unter portable-build-new nicht finden." -ForegroundColor Red
    exit 1
}
# electron-packager erzeugt in der Regel einen Ordner "LappDashboard-win32-x64" oder "LappDashboard-win32-x64 (electron version)".
# Wir ermitteln den tatsächlich erstellten Ordner dynamisch:
$pkgDir = Get-ChildItem -Directory "portable-build-new" | Where-Object { $_.Name -like "LappDashboard-win32-x64*" } | Select-Object -First 1
if (-not $pkgDir) {
    Write-Host "ERROR: Konnte den erzeugten App-Ordner unter portable-build-new nicht finden." -ForegroundColor Red
    exit 1
}

# 5. Frontend-Dateien (dist) in resources/app kopieren
Write-Host "Copying frontend (dist) to portable resources/app..." -ForegroundColor Yellow

# Pfade robust mit Join-Path zusammensetzen
$portableRoot = $pkgDir.FullName
$portableResources = Join-Path -Path $portableRoot -ChildPath "resources"
$portableApp = Join-Path -Path $portableResources -ChildPath "app"

# resources/app leeren und neu anlegen (saubere Übernahme des Frontends)
if (Test-Path $portableApp) {
    Remove-Item -Path $portableApp -Recurse -Force -ErrorAction SilentlyContinue
}
New-Item -ItemType Directory -Force -Path $portableApp | Out-Null

# dist nach resources/app kopieren (index.html, assets, etc.)
if (Test-Path "dist") {
    Copy-Item -Path "dist\*" -Destination $portableApp -Recurse -Force
    Write-Host "Frontend dist copied to resources/app" -ForegroundColor Green
} else {
    Write-Host "WARN: dist Ordner fehlt. Bitte sicherstellen, dass Vite-Renderer-Build erfolgreich war." -ForegroundColor Yellow
}

# 6. Backend-Dateien in portable build kopieren
Write-Host "Copying backend files to portable build..." -ForegroundColor Yellow

$portableBackend = Join-Path -Path $portableApp -ChildPath "backend"

# 6.1 Minimal package.json in resources/app erzeugen, damit Electron das Main-Entry findet
try {
    $appPkgPath = Join-Path $portableApp "package.json"
    $appPkg = @{
        name = "app"
        main = "main/electron-main.js"
    } | ConvertTo-Json -Depth 5
    $appPkg | Out-File -FilePath $appPkgPath -Encoding UTF8 -Force
    Write-Host "Wrote resources/app/package.json with main=main/electron-main.js" -ForegroundColor Green
} catch {
    Write-Host "WARN: Failed writing resources/app/package.json: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Exe-Datei ermitteln (Name kann variieren):
$exe = Get-ChildItem -Path $portableRoot -Filter "*.exe" | Select-Object -First 1
$portableExe = $exe?.FullName

# Sicherstellen, dass Zielverzeichnisse existieren
New-Item -ItemType Directory -Force -Path $portableBackend | Out-Null

# Backend dist files
if (Test-Path "backend\dist") {
    Copy-Item -Path "backend\dist" -Destination (Join-Path $portableBackend "dist") -Recurse -Force
    Write-Host "Backend dist files copied" -ForegroundColor Green
}

# Backend production dependencies
Write-Host "Installing production dependencies..." -ForegroundColor Yellow
Push-Location backend
npm ci --omit=dev --silent
Pop-Location

if (Test-Path "backend\node_modules") {
    Copy-Item -Path "backend\node_modules" -Destination (Join-Path $portableBackend "node_modules") -Recurse -Force
    Write-Host "Backend node_modules copied" -ForegroundColor Green
}

# Backend package.json
if (Test-Path "backend\package.json") {
    Copy-Item -Path "backend\package.json" -Destination (Join-Path $portableBackend "package.json") -Force
    Write-Host "Backend package.json copied" -ForegroundColor Green
}

# Database files
if (Test-Path "backend\database") {
    Copy-Item -Path "backend\database" -Destination (Join-Path $portableBackend "database") -Recurse -Force
    Write-Host "Database files copied" -ForegroundColor Green
}

# Prisma files
if (Test-Path "backend\prisma") {
    Copy-Item -Path "backend\prisma" -Destination (Join-Path $portableBackend "prisma") -Recurse -Force
    Write-Host "Prisma files copied" -ForegroundColor Green
}

# Generate Prisma client in portable build
Write-Host "Generating Prisma client for portable build..." -ForegroundColor Yellow
Push-Location $portableBackend
try {
    npx prisma generate --silent
    Write-Host "Prisma client generated" -ForegroundColor Green
} catch {
    Write-Host "Warning: Prisma generate failed, but continuing..." -ForegroundColor Yellow
}
Pop-Location

# Sicherstellen, dass main/preload Bundles vorhanden sind
if (-not (Test-Path "dist\main\electron-main.js")) {
    Write-Host "WARN: dist\main\electron-main.js fehlt. Bitte Vite Main Build prüfen." -ForegroundColor Yellow
}
if (-not (Test-Path "dist\main\preload.js")) {
    Write-Host "WARN: dist\main\preload.js fehlt. Bitte Vite Preload Build prüfen." -ForegroundColor Yellow
}

Write-Host "Portable build completed successfully!" -ForegroundColor Green
Write-Host ("Build location: {0}" -f $portableRoot) -ForegroundColor Cyan
Write-Host ("Frontend served from: {0}" -f $portableApp) -ForegroundColor Cyan
Write-Host ("Backend placed at: {0}" -f $portableBackend) -ForegroundColor Cyan

# Optional: Start the app
$startApp = Read-Host "Start the portable app now? (y/N)"
if ($startApp -eq "y" -or $startApp -eq "Y") {

    # 1) Bevorzugt die eigentliche App-Exe im Root des App-Ordners: *Dashboard*.exe
    $exePreferred = Get-ChildItem -Path $portableRoot -Filter "*Dashboard*.exe" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($exePreferred) {
        $portableExe = $exePreferred.FullName
        $portableRoot = $exePreferred.DirectoryName
    }

    # 2) Falls nicht gefunden: Rekursiv suchen, aber nur im App-Root und NICHT in resources\app\backend\node_modules
    if (-not $portableExe) {
        Write-Host "INFO: Suche nach App-EXE im Portable-Ordner (ohne Backend-Binaries)..." -ForegroundColor Cyan
        $exclude = Join-Path $portableRoot "resources\app\backend\node_modules"
        $exeCandidates = Get-ChildItem -Path $portableRoot -Recurse -Filter "*.exe" -ErrorAction SilentlyContinue `
            | Where-Object {
                # ausschließen: Prisma/Engine/Node-Binaries
                $_.FullName -notlike "*resources\app\backend\node_modules\*"
            } `
            | Where-Object {
                # bevorzugt *Dashboard*.exe
                $_.Name -match "Dashboard.*\.exe"
            } `
            | Sort-Object -Property LastWriteTime -Descending

        if (-not $exeCandidates -or $exeCandidates.Count -eq 0) {
            # letzte Chance: irgendeine .exe im Root (aber weiterhin Node-Module ausschließen)
            $exeCandidates = Get-ChildItem -Path $portableRoot -Recurse -Filter "*.exe" -ErrorAction SilentlyContinue `
                | Where-Object { $_.FullName -notlike "*resources\app\backend\node_modules\*" } `
                | Sort-Object -Property LastWriteTime -Descending
        }

        if ($exeCandidates -and $exeCandidates.Count -gt 0) {
            $portableExe = $exeCandidates[0].FullName
            $portableRoot = $exeCandidates[0].DirectoryName
        }
    }

    if (-not $portableExe) {
        Write-Host "WARN: Konnte keine App-EXE finden. Bitte manuell prüfen unter: $portableRoot" -ForegroundColor Yellow
    } else {
        Write-Host ("Starting portable app: {0}" -f $portableExe) -ForegroundColor Green
        Start-Process -FilePath $portableExe -WorkingDirectory $portableRoot
    }
}