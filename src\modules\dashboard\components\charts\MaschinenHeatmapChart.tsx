import { useEffect, useState, memo } from "react";
import apiService from "@/services/api.service";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Definition des Datentyps für die Effizienz-Daten (für API-Kompatibilität)
interface EfficiencyDataPoint {
  Datum: string;
  Machine: string;
  sollSchnitte: number;
  sollSchnitteProTag: number; // Tageswert (21h)
  tagesSchnitte: number;
  istSchnitteProStunde: number;
  effizienzProzent: number;
}

/**
 * MaschinenHeatmapChart Komponente
 * 
 * Zeigt alle Maschinen-Effizienz-Kennzahlen als Heatmap an - die beste Lösung für große Datensätze.
 * <PERSON><PERSON><PERSON> als Zeilen, <PERSON><PERSON> als Spalten, Farbe zeigt die Effizienz-Intensität (0-100%).
 */

interface HeatmapDataPoint {
  machine: string;
  date: string;
  value: number; // Effizienz-Prozent (0-100)
  formattedDate: string;
  sollSchnitte: number;
  sollSchnitteProTag: number; // Tageswert (21h)
  tagesSchnitte: number;
}

interface MaschinenHeatmapChartProps {
  data?: HeatmapDataPoint[];
  dateRange?: DateRange;
}

const filterDataByDateRange = (data: HeatmapDataPoint[], dateRange: DateRange): HeatmapDataPoint[] => {
  if (!dateRange.from || !dateRange.to) return data;

  return data.filter(item => {
    try {
      const itemDate = new Date(item.date);
      return isWithinInterval(itemDate, {
        start: dateRange.from as Date,
        end: dateRange.to as Date
      });
    } catch (error) {
          return false;
        }});
};

export const MaschinenHeatmapChart = memo(function MaschinenHeatmapChart({ data: propData, dateRange }: MaschinenHeatmapChartProps) {
  const [heatmapData, setHeatmapData] = useState<HeatmapDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Definiere alle Maschinen in logischer Reihenfolge
  const allMachines = [
    // H1-Maschinen
    "M5-R-H1", "M6-T-H1", "M7-R-H1", "M8-T-H1", "M9-R-H1",
    "M10-T-H1", "M11-R-H1", "M12-T-H1", "M13-R-H1", "M14-T-H1",
    "M15-R-H1", "M16-T-H1", "M17-R-H1", "M18-T-H1", "M19-T-H1",
    "M20-T-H1", "M21-R-H1", "M23-T-H1", "M25-RR-H1", "M26-T-H1",
    // H3-Maschinen
    "M1-T-H3", "M2-T-H3", "M3-R-H3", "M4-T-H3",
    "M22-T-H3", "M24-T-H3", "M27-R-H3"
  ];

  useEffect(() => {
    if (propData) {
      const filteredData = dateRange ? filterDataByDateRange(propData, dateRange) : propData;
      setHeatmapData(filteredData);
    } else {
      loadData();
    }
  }, [propData, dateRange]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await apiService.getMaschinenEfficiency();

      if (!result || !Array.isArray(result) || result.length === 0) {
        setError('Keine Daten in der maschinen_efficiency-Tabelle gefunden');
        setHeatmapData([]);
        return;
      }



      // Konvertiere Effizienz-Daten zu Heatmap-Format
      const heatmapPoints: HeatmapDataPoint[] = [];

      // Verarbeite die Daten für die Heatmap
      if (result && Array.isArray(result)) {
        // Gruppiere Daten nach Datum und Maschine
        for (const item of result) {
          if (!item.Datum || !item.Machine) {
            continue;
          }

          // Formatiere das Datum für die Anzeige
          const dateParts = item.Datum.split('-');
          const formattedDate = `${dateParts[2]}.${dateParts[1]}.${dateParts[0]}`;

          // Verwende die vom Backend bereitgestellten Effizienzwerte direkt
          // Begrenze die Effizienz auf maximal 200% für die Anzeige
          const efficiency = Math.min(item.effizienzProzent || 0, 200);

          // Verwende den Maschinennamen direkt aus der API
          const machineName = item.Machine;

          heatmapPoints.push({
            machine: machineName,
            date: item.Datum,
            value: efficiency,
            formattedDate,
            sollSchnitte: item.sollSchnitte || 0,
            sollSchnitteProTag: item.sollSchnitteProTag || 0,
            tagesSchnitte: item.tagesSchnitte || 0
          });
        }
      }

      // Sortiere nach Datum
      heatmapPoints.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      // Filtere nach Datumsbereich falls angegeben
      let finalData = heatmapPoints;
      if (dateRange && dateRange.from && dateRange.to) {
        finalData = heatmapPoints.filter(item => {
          try {
            const itemDate = new Date(item.date);
            return isWithinInterval(itemDate, {
              start: dateRange.from as Date,
              end: dateRange.to as Date
            });
          } catch {
            return true;
          }
        });
      }

      setHeatmapData(finalData || []);
    } catch (err) {
      setHeatmapData([]);
      setError('Fehler beim Laden der Daten: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Funktion zur Farbberechnung basierend auf Effizienz (0-200%)
  const getEfficiencyColor = (efficiency: number): string => {
    if (efficiency <= 0) return 'rgba(255, 255, 255, 0.8)'; // Keine Daten
    if (efficiency < 50) return 'rgba(220, 38, 38, 0.3)';   // Rot für niedrige Effizienz
    if (efficiency < 80) return 'rgba(245, 158, 11, 0.4)';  // Orange für mittlere Effizienz
    if (efficiency <= 100) return 'rgba(22, 163, 74, 0.5)'; // Grün für normale Effizienz
    return 'rgba(37, 99, 235, 0.5)';                        // Blau für überdurchschnittliche Effizienz
  };

  // Funktion für Tooltip-Farben basierend auf Effizienz
  const getTooltipColors = (efficiency: number) => {
    if (efficiency <= 0) {
      return {
        bgClass: 'bg-gray-50',
        borderClass: 'border-gray-200',
        textClass: 'text-gray-600',
        valueClass: 'text-gray-800'
      };
    }
    if (efficiency < 50) {
      return {
        bgClass: 'bg-red-50',
        borderClass: 'border-red-200',
        textClass: 'text-red-700',
        valueClass: 'text-red-800'
      };
    }
    if (efficiency < 80) {
      return {
        bgClass: 'bg-orange-50',
        borderClass: 'border-orange-200',
        textClass: 'text-orange-700',
        valueClass: 'text-orange-800'
      };
    }
    if (efficiency <= 100) {
      return {
        bgClass: 'bg-green-50',
        borderClass: 'border-green-200',
        textClass: 'text-green-700',
        valueClass: 'text-green-800'
      };
    }
    return {
      bgClass: 'bg-blue-50',
      borderClass: 'border-blue-200',
      textClass: 'text-blue-700',
      valueClass: 'text-blue-800'
    };
  };

  // Gruppiere Daten nach Datum für die Spalten
  const uniqueDates = [...new Set(heatmapData.map(d => d.formattedDate))];

  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">Lädt Effizienz-Heatmap...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center">
        <p className="text-red-500 font-bold">{error}</p>
      </div>
    );
  }

  return (
    <Card className="text-black border border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="space-y-6">
            <CardTitle className="text-xl">
              Datentabelle: {allMachines.length} Maschinen über {uniqueDates.length} Zeitpunkte
            </CardTitle>
            <CardDescription>
              Hover über Zellen für mehr Details
            </CardDescription>
          </div>
          <div className="p-3 bg-gray-50 border border-black rounded-lg">
            <div className="text-sm font-bold mb-2">Effizienz-Legende:</div>
            <div className="flex items-center gap-4 text-xs flex-wrap">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gray-200 border border-black"></div>
                <span>Keine Daten</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border border-black" style={{ backgroundColor: 'rgba(220, 38, 38, 0.3)' }}></div>
                <span>Niedrig {'(< 50%)'}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border border-black" style={{ backgroundColor: 'rgba(245, 158, 11, 0.4)' }}></div>
                <span>Mittel (50-80%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border border-black" style={{ backgroundColor: 'rgba(22, 163, 74, 0.5)' }}></div>
                <span>Normal (80-100%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border border-black" style={{ backgroundColor: 'rgba(37, 99, 235, 0.5)' }}></div>
                <span>Überdurchschnittlich ({'>'}100%)</span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="w-full overflow-x-auto">
          <div className="min-w-max">
            {/* Header mit Datum */}
            <div className="grid grid-cols-[120px_repeat(auto-fit,minmax(60px,1fr))] gap-1 mb-2">
              <div className="text-xs font-bold p-2 bg-gray-100 border border-black">
                Maschine
              </div>
              {uniqueDates.map((date) => (
                <div key={date} className="text-xs font-bold p-1 bg-gray-100 border border-black text-center">
                  {date}
                </div>
              ))}
            </div>

            {/* Heatmap Grid */}
            <div className="space-y-1">
              {allMachines.map((machine) => (
                <div key={machine} className="grid grid-cols-[120px_repeat(auto-fit,minmax(60px,1fr))] gap-1">
                  {/* Maschinen-Label */}
                  <div className={`text-xs font-medium p-2 border border-black ${machine.includes('H3') ? 'bg-red-50' : 'bg-blue-50'
                    }`}>
                    {machine}
                  </div>

                  {/* Effizienz-Zellen */}
                  {uniqueDates.map((date) => {
                    const dataPoint = heatmapData.find(d => d.machine === machine && d.formattedDate === date);
                    const efficiency = dataPoint?.value ?? 0;
                    const bgColor = getEfficiencyColor(efficiency);
                    const tooltipColors = getTooltipColors(efficiency);

                    return (
                      <TooltipProvider key={`${machine}-${date}`}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className="text-xs p-1 border border-black text-center cursor-pointer hover:border-2 hover:border-yellow-400 font-medium"
                              style={{ backgroundColor: bgColor }}
                            >
                              {efficiency > 0 ? `${Math.round(efficiency)}%` : '-'}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className={`bg-white p-6 border-2 ${tooltipColors.borderClass} rounded-lg shadow-xl w-[200px] max-w-none h-[230px] max-h-none`}>
                            <div className="space-y-3">
                              <div className={`border-b ${tooltipColors.borderClass} pb-2`}>
                                <p className={`font-bold text-lg text-center ${tooltipColors.textClass}`}>{`${machine} am ${date}`}</p>
                              </div>
                              <div className="space-y-2">
                                <div className={`flex justify-between items-center py-1 px-2 ${tooltipColors.bgClass} rounded`}>
                                  <span className="font-medium text-gray-700">Soll-Schnitte:</span>
                                  <span className={`font-bold ${tooltipColors.valueClass}`}>{dataPoint?.sollSchnitteProTag.toFixed(0) ?? 'N/A'}</span>
                                </div>
                                <div className={`flex justify-between items-center py-1 px-2 ${tooltipColors.bgClass} rounded`}>
                                  <span className="font-medium text-gray-700">Ist-Schnitte erreicht:</span>
                                  <span className={`font-bold ${tooltipColors.valueClass}`}>{dataPoint?.tagesSchnitte.toFixed(0) ?? 'N/A'}</span>
                                </div>
                                <div className={`flex justify-between items-center py-1 px-2 ${tooltipColors.bgClass} rounded border ${tooltipColors.borderClass}`}>
                                  <span className="font-medium text-gray-700">Effizienz:</span>
                                  <span className={`font-bold text-xl ${tooltipColors.valueClass}`}>{efficiency.toFixed(1)}%</span>
                                </div>
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })}
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});