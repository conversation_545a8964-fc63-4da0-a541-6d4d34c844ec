import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, RadialBarChart, RadialBar, PolarAngleAxis } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '@/components/ui/chart';
import { DateRange } from 'react-day-picker';
import { BarChart3, TrendingUp, AlertTriangle, Clock, ShieldCheck, Timer } from 'lucide-react';
import { SEVERITY_COLORS, Stoerung, StoerungsStats } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { motion } from 'framer-motion';

interface StoerungsStatsProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

interface StoerungsStatsChartProps extends StoerungsStatsProps { }

interface StoerungsKpiCardsProps extends StoerungsStatsProps { }

// Custom hook to share data logic between components
const useStoerungsStats = (dateRange?: DateRange, refreshKey: number = 0) => {
  const [stats, setStats] = useState<StoerungsStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAndCalculateStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const stoerungen = await stoerungenService.getStoerungen();

        let filteredStoerungen = Array.isArray(stoerungen) ? stoerungen : [];

        if (dateRange?.from && dateRange?.to) {
          filteredStoerungen = filteredStoerungen.filter(stoerung => {
            const createdDate = new Date(stoerung.created_at);
            return createdDate >= dateRange.from! && createdDate <= dateRange.to!;
          });
        }

        if (filteredStoerungen.length === 0) {
          setStats({ total: 0, active: 0, resolved: 0, critical: 0, high: 0, medium: 0, low: 0, avg_mttr_minutes: 0, avg_mtta_minutes: 0, first_time_fix_rate: 0, resolution_rate_24h: 0 });
          return;
        }

        const total = filteredStoerungen.length;
        const active = filteredStoerungen.filter(s => s.status !== 'GELÖST' && s.status !== 'ABGESCHLOSSEN').length;
        const resolved = total - active;

        const critical = filteredStoerungen.filter(s => s.severity === 'CRITICAL').length;
        const high = filteredStoerungen.filter(s => s.severity === 'HIGH').length;
        const medium = filteredStoerungen.filter(s => s.severity === 'MEDIUM').length;
        const low = filteredStoerungen.filter(s => s.severity === 'LOW').length;

        const resolvedWithMTTR = filteredStoerungen.filter(s => s.status === 'GELÖST' && s.mttr_minutes != null && s.mttr_minutes > 0);
        const avg_mttr_minutes = resolvedWithMTTR.length > 0 ? Math.round(resolvedWithMTTR.reduce((sum, s) => sum + s.mttr_minutes!, 0) / resolvedWithMTTR.length) : 0;

        const acknowledgedWithMTTA = filteredStoerungen.filter(s => s.mtta_minutes != null && s.mtta_minutes > 0);
        const avg_mtta_minutes = acknowledgedWithMTTA.length > 0 ? Math.round(acknowledgedWithMTTA.reduce((sum, s) => sum + s.mtta_minutes!, 0) / acknowledgedWithMTTA.length) : 0;

        const resolvedWithoutEscalation = resolvedWithMTTR.filter(s => !s.escalation_level || s.escalation_level === 'L1');
        const first_time_fix_rate = resolvedWithMTTR.length > 0 ? Math.round((resolvedWithoutEscalation.length / resolvedWithMTTR.length) * 100) : 0;

        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const resolution_rate_24h = filteredStoerungen.filter(s => s.status === 'GELÖST' && s.resolved_at && new Date(s.resolved_at) >= yesterday).length;

        setStats({ total, active, resolved, critical, high, medium, low, avg_mttr_minutes, avg_mtta_minutes, first_time_fix_rate, resolution_rate_24h });

      } catch (err) {
        console.error('Error fetching or calculating stats:', err);
        setError('Fehler beim Laden der Statistiken');
      } finally {
        setLoading(false);
      }
    };

    fetchAndCalculateStats();
  }, [refreshKey, dateRange]);

  return { stats, loading, error };
};

// KPI Cards Component
export const StoerungsKpiCards: React.FC<StoerungsKpiCardsProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const { stats, loading, error } = useStoerungsStats(dateRange, refreshKey);

  if (loading) {
    return <>{[...Array(6)].map((_, index) => <Card key={index} className="p-3"><div className="animate-pulse"><div className="h-4 rounded mb-2"></div><div className="h-6 rounded"></div></div></Card>)}</>;
  }

  if (!stats) {
    return null;
  }

  const kpiData = [
    { label: 'Gesamt Störungen', value: stats.total, icon: AlertTriangle, color: 'text-blue-600', bgColor: 'bg-blue-50' },
    { label: 'Aktive Störungen', value: stats.active, icon: TrendingUp, color: 'text-red-600', bgColor: 'bg-red-50' },
    { label: 'Gelöste Störungen', value: stats.resolved, icon: BarChart3, color: 'text-green-600', bgColor: 'bg-green-50' },
    { label: 'Ø MTTR', value: stoerungenService.formatMTTR(stats.avg_mttr_minutes), icon: Clock, color: 'text-orange-600', bgColor: 'bg-orange-50' },
    { label: 'Ø MTTA', value: stoerungenService.formatMTTR(stats.avg_mtta_minutes), icon: Timer, color: 'text-indigo-600', bgColor: 'bg-indigo-50' },
    { label: 'Erstlösungsrate', value: `${stats.first_time_fix_rate}%`, icon: ShieldCheck, color: 'text-cyan-600', bgColor: 'bg-cyan-50' },
  ];

  return (
    <>
      {kpiData.map((kpi, index) => {
        const IconComponent = kpi.icon;
        return (
          <div
            key={index}
            className={`group border-[#ff7a05]/30 hover:border-[#ff7a05]/50 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border px-4 py-4 shadow-md transition-all duration-500 ${kpi.bgColor} min-w-0`}
          >
            <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#ff7a0520_1px,transparent_1px),linear-gradient(to_bottom,#ff7a0520_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
            <div className="text-[#ff7a05]/10 group-hover:text-[#ff7a05]/20 absolute right-1 bottom-1 scale-[2] transition-all duration-700 group-hover:scale-[2.2]">
              <IconComponent className="h-8 w-8" />
            </div>
            <div className="relative z-10 flex h-full flex-col justify-between">
              <div>
                <div className="bg-[#ff7a05]/10 text-[#ff7a05] shadow-[#ff7a05]/10 group-hover:bg-[#ff7a05]/20 group-hover:shadow-[#ff7a05]/20 mb-2 flex h-8 w-8 items-center justify-center rounded-full shadow transition-all duration-500">
                  <IconComponent className="h-4 w-4" />
                </div>
                <p className={`text-xs font-medium opacity-80 mb-1 ${kpi.color}`}>{kpi.label}</p>
                <p className={`text-lg font-bold ${kpi.color}`}>{kpi.value}</p>
              </div>
            </div>
            <div className="from-[#ff7a05] to-[#ff7a05]/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
          </div>
        );
      })}
    </>
  );
});

StoerungsKpiCards.displayName = 'StoerungsKpiCards';

// ... rest of the file remains the same ...
