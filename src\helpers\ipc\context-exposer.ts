import { exposeWindowContext } from "./window/window-context";
import { exposeTeamsContext } from "./teams/teams-context";

/**
 * Stellt alle Kontexte im Renderer-Prozess zur Verfügung
 * Database Context entfernt - Frontend verwendet direkt API Service
 * Theme Context entfernt - App verwendet festes Light-Theme
 */
export default function exposeContexts() {
  const electronAPI = {
    window: exposeWindowContext(),
    teams: exposeTeamsContext(),
  };

  console.log('Alle Kontexte wurden im Renderer-Prozess verfügbar gemacht (Database IPC, Theme entfernt, Teams hinzugefügt).');
  return electronAPI;
}
