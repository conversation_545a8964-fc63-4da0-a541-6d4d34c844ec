export enum StoerungStatus {
  NEU = 'NEU',
  IN_BEARBEITUNG = 'IN_BEARBEITUNG',
  ESKALIERT_L2 = 'ESKALIERT_L2',
  ESKALIERT_L3 = 'ESKALIERT_L3',
  GELÖST = 'GELÖST',
  ABGESCHLOSSEN = 'ABGESCHLOSSEN',
  REVIEW = 'REVIEW'
}

export enum Eskalationslevel {
  L1 = 'L1',
  L2 = 'L2',
  L3 = 'L3',
  TEAMLEAD = 'TEAMLEAD',
  MANAGEMENT = 'MANAGEMENT'
}

export interface ActionItem {
    id: number;
    stoerung_id: number;
    beschreibung: string;
    zustaendig: string;
    faellig_am: string;
    erledigt: boolean;
}

export interface Stoerung {
  id: number;
  title: string;
  description?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: StoerungStatus;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  acknowledged_at?: string;
  acknowledged_by?: string;
  escalation_level?: Eskalationslevel;
  resolution_steps?: string;
  root_cause?: string;
  lessons_learned?: string;
  action_items?: ActionItem[];
  runbook_ids?: number[];
  mttr_minutes?: number;
  mtta_minutes?: number;
  tags?: string[];
  comments?: StoerungComment[];
}

export interface StoerungComment {
  id: number;
  stoerung_id: number;
  user_id?: string;
  comment: string;
  created_at: string;
}

export interface StoerungCreateData {
  title: string;
  description?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status?: StoerungStatus;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  tags?: string[];
  send_protocol?: boolean;
}

export interface StoerungUpdateData {
  title?: string;
  description?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status?: StoerungStatus;
  category?: string;
  affected_system?: string;
  location?: string;
  assigned_to?: string;
  tags?: string[];
  escalation_level?: Eskalationslevel;
  resolution_steps?: string;
  root_cause?: string;
  lessons_learned?: string;
  action_items?: ActionItem[];
  runbook_ids?: number[];
}

export interface StoerungsStats {
  total: number;
  active: number;
  resolved: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  avg_mttr_minutes: number;
  avg_mtta_minutes: number;
  first_time_fix_rate: number;
  resolution_rate_24h: number;
}

export interface SystemStatus {
  id: number;
  system_name: string;
  status: 'OK' | 'WARNING' | 'ERROR';
  last_check: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface SystemStatusUpdate {
  system_name: string;
  status: 'OK' | 'WARNING' | 'ERROR';
  metadata?: any;
}

export const SEVERITY_COLORS = {
  LOW: '#10b981',
  MEDIUM: '#f59e0b',
  HIGH: '#ef4444',
  CRITICAL: '#dc2626',
} as const;

export const STATUS_COLORS = {
  [StoerungStatus.NEU]: '#3b82f6',
  [StoerungStatus.IN_BEARBEITUNG]: '#f59e0b',
  [StoerungStatus.ESKALIERT_L2]: '#9333ea',
  [StoerungStatus.ESKALIERT_L3]: '#c026d3',
  [StoerungStatus.GELÖST]: '#16a34a',
  [StoerungStatus.ABGESCHLOSSEN]: '#10b981',
  [StoerungStatus.REVIEW]: '#64748b',
} as const;

export const SYSTEM_STATUS_COLORS = {
  OK: '#10b981',
  WARNING: '#f59e0b',
  ERROR: '#ef4444',
} as const;