import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Save, AlertTriangle, MessageSquare } from 'lucide-react';
import { StoerungCreateData, StoerungStatus } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';

interface StoerungsFormProps {
  onClose: () => void;
  onSubmit: () => void;
}

export const StoerungsForm: React.FC<StoerungsFormProps> = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState<StoerungCreateData>({
    title: '',
    description: '',
    severity: 'MEDIUM',
    status: StoerungStatus.NEU,
    category: '',
    affected_system: '',
    location: '',
    reported_by: '',
    assigned_to: '',
    tags: [],
    send_protocol: false,
  });

  const [sendProtocol, setSendProtocol] = useState(false);
  const [sendToTeams, setSendToTeams] = useState(true); // Teams standardmäßig aktiviert
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [teamsStatus, setTeamsStatus] = useState<string | null>(null);

  const severityOptions = [
    { value: 'LOW', label: 'Niedrig', color: 'text-green-600' },
    { value: 'MEDIUM', label: 'Mittel', color: 'text-yellow-600' },
    { value: 'HIGH', label: 'Hoch', color: 'text-orange-600' },
    { value: 'CRITICAL', label: 'Kritisch', color: 'text-red-600' },
  ];

  const categoryOptions = [
    'System',
    'Hardware',
    'Software',
    'Prozess',
    'Netzwerk',
    'Drucker',
    'Sonstiges',
  ];

  const affectedSystemOptions = [
    'FTS',
    'ARiL',
    'ATrL',
    'SAP',
    'ITM',
    'Schrumpfanlage',
    'Förderanlagen',
    'Schneidmaschinen',
    'Terminal',
    'Sonstiges',
  ];

  const locationOptions = [
    'Ludwigsburg',
    'Stuttgart',
    'Hannover',
    'Illingen',
  ];

  const handleInputChange = (field: keyof StoerungCreateData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleProtocolChange = (checked: boolean) => {
    setSendProtocol(checked);
  };

  const handleTeamsChange = (checked: boolean) => {
    setSendToTeams(checked);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      setError('Titel ist erforderlich');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setTeamsStatus(null);

      const submitData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description?.trim() || undefined,
        category: formData.category?.trim() || undefined,
        affected_system: formData.affected_system?.trim() || undefined,
        location: formData.location?.trim() || undefined,
        reported_by: formData.reported_by?.trim() || undefined,
        assigned_to: formData.assigned_to?.trim() || undefined,
        send_protocol: sendProtocol,
      };

      // Störung in der Datenbank erstellen
      await stoerungenService.createStoerung(submitData);

      // An Microsoft Teams senden, falls aktiviert
      if (sendToTeams && window.electronAPI?.teams) {
        try {
          setTeamsStatus('Sende an Microsoft Teams...');
          // Prepare Teams data with proper typing
          const teamsData = {
            title: submitData.title,
            severity: submitData.severity,
            status: submitData.status || StoerungStatus.NEU, // Provide default value if undefined
            send_protocol: sendProtocol,
            ...(submitData.description && { description: submitData.description }),
            ...(submitData.category && { category: submitData.category }),
            ...(submitData.affected_system && { affected_system: submitData.affected_system }),
            ...(submitData.location && { location: submitData.location }),
            ...(submitData.reported_by && { reported_by: submitData.reported_by }),
          };

          const teamsResult = await window.electronAPI.teams.sendStoerung(teamsData);

          if (teamsResult.success) {
            setTeamsStatus('✅ Erfolgreich an Teams gesendet');
          } else {
            setTeamsStatus(`⚠️ Teams-Fehler: ${teamsResult.error}`);
            console.warn('Teams sending failed:', teamsResult.error);
          }
        } catch (teamsError) {
          console.error('Teams integration error:', teamsError);
          setTeamsStatus('⚠️ Teams-Integration nicht verfügbar');
        }
      }

      // Kurz warten, damit der Benutzer die Teams-Statusmeldung sehen kann
      setTimeout(() => {
        onSubmit();
      }, sendToTeams ? 2000 : 0);

    } catch (err) {
      console.error('Error creating störung:', err);
      setError('Fehler beim Erstellen der Störung');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="flex items-center gap-2 mb-6">
        <AlertTriangle className="h-6 w-6 text-red-600" />
        <h2 className="text-2xl font-bold">Neue Störung erfassen</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title">Titel *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Kurze Beschreibung der Störung"
            required
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Beschreibung</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Detaillierte Beschreibung der Störung"
            rows={3}
          />
        </div>

        {/* Severity */}
        <div className="space-y-2">
          <Label htmlFor="severity">Schweregrad</Label>
          <Select value={formData.severity} onValueChange={(value) => handleInputChange('severity', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {severityOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <span className={option.color}>{option.label}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category */}
        <div className="space-y-2">
          <Label htmlFor="category">Kategorie</Label>
          <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Kategorie wählen" />
            </SelectTrigger>
            <SelectContent>
              {categoryOptions.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Affected System */}
        <div className="space-y-2">
          <Label htmlFor="affected_system">Betroffenes System</Label>
          <Select value={formData.affected_system} onValueChange={(value) => handleInputChange('affected_system', value)}>
            <SelectTrigger>
              <SelectValue placeholder="System wählen" />
            </SelectTrigger>
            <SelectContent>
              {affectedSystemOptions.map((system) => (
                <SelectItem key={system} value={system}>
                  {system}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Location */}
        <div className="space-y-2">
          <Label htmlFor="location">Standort</Label>
          <Select value={formData.location} onValueChange={(value) => handleInputChange('location', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Standort wählen" />
            </SelectTrigger>
            <SelectContent>
              {locationOptions.map((location) => (
                <SelectItem key={location} value={location}>
                  {location}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Reported by */}
        <div className="space-y-2">
          <Label htmlFor="reported_by">Gemeldet von</Label>
          <Input
            id="reported_by"
            value={formData.reported_by}
            onChange={(e) => handleInputChange('reported_by', e.target.value)}
            placeholder="Name des Melders"
          />
        </div>

        {/* Störungsprotokoll versenden */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="send-protocol"
              checked={sendProtocol}
              onCheckedChange={handleProtocolChange}
            />
            <Label
              htmlFor="send-protocol"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Störungsprotokoll versenden
            </Label>
          </div>
          <p className="text-sm text-gray-600">
            Aktivieren Sie diese Option, um automatisch ein Störungsprotokoll per E-Mail zu versenden.
          </p>
        </div>

        {/* An Microsoft Teams senden */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="send-to-teams"
              checked={sendToTeams}
              onCheckedChange={handleTeamsChange}
            />
            <Label
              htmlFor="send-to-teams"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              An Microsoft Teams senden
            </Label>
          </div>
          <p className="text-sm text-gray-600">
            Sendet eine Benachrichtigung über die neue Störung an den konfigurierten Teams-Kanal.
          </p>
          {teamsStatus && (
            <p className={`text-sm ${teamsStatus.includes('✅') ? 'text-green-600' : 'text-orange-600'}`}>
              {teamsStatus}
            </p>
          )}
        </div>

        {/* Submit buttons */}
        <div className="flex gap-2 pt-4">
          <Button type="submit" variant="accept" disabled={loading} className="">
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Speichere...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Störung erfassen
              </>
            )}
          </Button>

          <Button type="button" variant="ghost" onClick={onClose}>
            Abbrechen
          </Button>

          {/* Teams Test Button */}
          {sendToTeams && window.electronAPI?.teams && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  setTeamsStatus('Teste Teams-Verbindung...');
                  const result = await window.electronAPI.teams.testConnection();
                  if (result.success) {
                    setTeamsStatus('✅ Teams-Verbindung erfolgreich');
                  } else {
                    setTeamsStatus(`❌ Teams-Test fehlgeschlagen: ${result.error}`);
                  }
                } catch (error) {
                  setTeamsStatus('❌ Teams nicht verfügbar');
                }
              }}
              disabled={loading}
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              Teams testen
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};