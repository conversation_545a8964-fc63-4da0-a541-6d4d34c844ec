/**
 * Cutting optimization types and interfaces
 */

export interface CuttingOrder {
  id: string;
  requiredLength: number;
  quantity: number;
  priority: 'high' | 'medium' | 'low';
  material?: string;
  tolerance?: number;
}

export interface AvailableDrum {
  id: string;
  availableLength?: number;
  remainingLength?: number;
  material?: string;
  diameter?: number;
}

export interface CuttingRequest {
  orders: CuttingOrder[];
  availableDrums: AvailableDrum[];
  constraints?: CuttingConstraints;
}

export interface CuttingConstraints {
  maxCutsPerDrum?: number;
  maxWasteLength?: number;
  allowPartialFulfillment?: boolean;
  prioritizeHighPriority?: boolean;
}

export interface Cut {
  orderId: string;
  length: number;
  startPosition: number;
  endPosition: number;
  priority?: 'high' | 'medium' | 'low';
}

export interface DrumAllocation {
  drumId: string;
  cuts: Cut[];
  remainingLength: number;
  utilization: number;
}

export interface CuttingStep {
  stepNumber: number;
  drumId: string;
  cuts: Cut[];
  estimatedTime: number;
}

export interface CuttingPlan {
  drumAllocations: DrumAllocation[];
  cuttingSequence: CuttingStep[];
  totalWaste: number;
  efficiency: number;
  estimatedTime: number;
}

export interface CuttingAlternative {
  id: string;
  plan: CuttingPlan;
  score: number;
  advantages: string[];
  disadvantages: string[];
}