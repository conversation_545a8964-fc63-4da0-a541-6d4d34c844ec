import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Stoerung } from '@/types/stoerungen.types';
import { Runbook } from '@/types/runbooks.types';
import { RunbookList } from '@/modules/runbooks/components';
import stoerungenService from '@/services/stoerungen.service';
import { toast } from 'sonner';

interface LinkRunbookDialogProps {
  stoerung: Stoerung;
  onClose: () => void;
}

export const LinkRunbookDialog: React.FC<LinkRunbookDialogProps> = ({ stoerung, onClose }) => {
  const handleSelectRunbook = async (runbook: Runbook) => {
    try {
      const updatedRunbookIds = [...(stoerung.runbook_ids || []), runbook.id];
      await stoerungenService.updateStoerung(stoerung.id, { runbook_ids: updatedRunbookIds });
      toast.success(`Runbook "${runbook.title}" erfolgreich mit Störung verlinkt.`);
      onClose();
    } catch (error) {
      toast.error('Fehler beim Verlinken des Runbooks.');
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Runbook für "{stoerung.title}" verlinken</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <RunbookList onSelect={handleSelectRunbook} />
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="secondary">Abbrechen</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};