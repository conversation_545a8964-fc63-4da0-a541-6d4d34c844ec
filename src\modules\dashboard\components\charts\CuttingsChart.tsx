import React, { useEffect, useState, memo } from "react";
import apiService from "@/services/api.service";

// AblaengereiDataPoint ist nicht mehr nötig, da getCuttingChartData bereits formatierte Daten liefert

import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
} from "recharts";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CircleSlash2 } from "lucide-react";

/**
 * CuttingsChart Komponente
 * 
 * Zeigt die Schneidemaschinen-Kennzahlen als Balkendiagramm an.
 * Verwendet Daten aus der Ablaengerei-Tabelle (cutTT, cutTR, cutRR)
 * 
 * Die X-Achse zeigt das Datum, die Y-Achse die Anzahl der Schnitte
 */

// Definition des Datentyps für die Schnitt-Daten im Chart
interface CuttingDataPoint {
  name: string;
  date?: Date; // Hilfsdatum für die Sortierung
  cutTT: number;
  cutTR: number;
  cutRR: number; // Korrigiert von cuteRR zu cutRR, um mit der Datenbank übereinzustimmen
  pickCut: number; // Neue Spalte für PickCut
}

interface CuttingsChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: CuttingDataPoint[];
  // Datumsbereich für die Filterung der Daten
  dateRange?: DateRange;
}

/**
 * Filtert die Daten nach dem angegebenen Datumsbereich
 * 
 * @param data Die zu filternden Daten
 * @param dateRange Der Datumsbereich für die Filterung
 * @returns Die gefilterten Daten
 */
const filterDataByDateRange = (data: CuttingDataPoint[], dateRange: DateRange): CuttingDataPoint[] => {
  if (!dateRange.from || !dateRange.to) return data;

  return data.filter(item => {
    // Wenn das Item kein Datum hat, können wir es nicht filtern
    if (!item.date) return true;

    // Überprüfe, ob das Datum im angegebenen Bereich liegt
    try {
      return isWithinInterval(item.date, {
        start: dateRange.from as Date, // Explizite Typumwandlung
        end: dateRange.to as Date // Explizite Typumwandlung
      });
    } catch (error) {
          return true; // Im Fehlerfall das Item beibehalten
        }
  });
};

export const CuttingsChart = memo(function CuttingsChart({ data: propData, dateRange }: CuttingsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<CuttingDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    cutTT: {
      label: t("Trommel-Trommel"),
      color: "var(--chart-1)", // Kräftige Farbe im Neobrutalism-Stil
    },
    cutTR: {
      label: t("Trommel-Ring"),
      color: "var(--chart-2)",
    },
    cutRR: {
      label: t("Ring-Ring"),
      color: "var(--chart-3)",
    },
    pickCut: {
      label: t("PickCut"),
      color: "var(--chart-4)",
    }
  };

  // Farben für die verschiedenen Balken im Neobrutalism-Stil
  const colors = ["var(--chart-1)", "var(--chart-2)", "var(--chart-3)", "var(--chart-4)"];

  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      // Filtere die Daten nach dem Datumsbereich, wenn vorhanden
      const filteredData = dateRange ? filterDataByDateRange(propData, dateRange) : propData;
      setChartData(filteredData);
    } else {
      // Lade Daten aus der Datenbank
      loadData();
    }
  }, [propData, dateRange]);

  /**
   * Lädt die Daten aus der Ablaengerei-Tabelle der Datenbank
   * und filtert sie nach dem angegebenen Datumsbereich
   */
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await apiService.getCuttingChartData();

      if (!result) {
        setChartData([]);
        setError('Keine Cutting-Daten verfügbar');
        return;
      }

      // Die Daten sind bereits vom Service gefiltert und formatiert
      // Filtere nur nach Datumsbereich, wenn vorhanden
      let processedData = result as CuttingDataPoint[];

      if (dateRange && dateRange.from && dateRange.to) {
        processedData = processedData.filter(item => {
          if (!item.date) return true;

          try {
            return isWithinInterval(new Date(item.date), {
              start: dateRange.from as Date,
              end: dateRange.to as Date
            });
          } catch (error) {
            return true;
          }
        });
      }
      setChartData(processedData || []);
    } catch (err) {
      setChartData([]);
      setError('Fehler beim Laden der Daten aus der Datenbank: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Berechne Gesamtwerte und Durchschnitte für den Footer
  const totalTT = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.cutTT || 0), 0) : 0;
  const totalTR = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.cutTR || 0), 0) : 0;
  const totalRR = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.cutRR || 0), 0) : 0;
  const totalAll = totalTT + totalTR + totalRR;
  const avgTT = chartData.length > 0 ? totalTT / chartData.length : 0;
  const avgTR = chartData.length > 0 ? totalTR / chartData.length : 0;
  const avgRR = chartData.length > 0 ? totalRR / chartData.length : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="text-red-500 font-bold">{error}</p>
        <p className="mt-2">{t("check_database_connection")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>SCHNITTARTEN</CardTitle>
        <CardDescription>
          Übersicht der Trommel uns Ring Schnitte
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="h-60 w-full"
        >
          <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="name"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              angle={-45}
              textAnchor="end"
              height={80}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {
                try {
                  // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                  const date = new Date(value);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleDateString('de-DE', {
                      day: '2-digit',
                      month: '2-digit'
                    });
                  }
                  // Falls es kein gültiges Datum ist, zeige den String direkt
                  return String(value);
                } catch {
                  return value;
                }
              }}
            />
            <YAxis
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              // Beschriftung für die Y-Achse
              label={{
                value: "Anzahl Schnitte",
                angle: -90,
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => `Datum: ${label}`}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="cutTT"
              name="Trommel-Trommel"
              fill={colors[0]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="cutTR"
              name="Trommel-Ring"
              fill={colors[1]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="cutRR"
              name="Ring-Ring"
              fill={colors[2]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="pickCut"
              name="PickCut"
              fill="var(--chart-1)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              <span className="text-black text-md flex items-center"> <CircleSlash2 className="mr-2 h-4 w-4" /> : TT: {chartData.length > 0 ? avgTT.toFixed(1) : 'N/A'} | </span>
              <span className="text-black text-md flex items-center">TR: {chartData.length > 0 ? avgTR.toFixed(1) : 'N/A'} | </span>
              <span className="text-black text-md flex items-center">RR: {chartData.length > 0 ? avgRR.toFixed(1) : 'N/A'} </span>
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              <span className="text-black text-md flex items-center">Gesamt: {chartData.length > 0 ? totalAll : 0} | </span>
              {chartData.length > 0
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank`
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
