import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Edit, Trash2, ShieldAlert, RefreshCw, Save } from 'lucide-react';
import { EskalationsRegel, CreateEskalationsRegelRequest, UpdateEskalationsRegelRequest } from '@/types/bereitschafts';
import { Eskalationslevel } from '@/types/stoerungen.types';
import { StoerungStatus } from '@/types/stoerungen.types';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import { toast } from 'sonner';

export const BereitschaftsEskalationsmatrix: React.FC = () => {
  const [regeln, setRegeln] = useState<EskalationsRegel[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRegel, setEditingRegel] = useState<EskalationsRegel | null>(null);
  const [formData, setFormData] = useState<Partial<CreateEskalationsRegelRequest>>({});

  useEffect(() => {
    fetchRegeln();
  }, []);

  const fetchRegeln = async () => {
    try {
      setLoading(true);
      const data = await bereitschaftsService.getEskalationsregeln();
      setRegeln(data);
    } catch (error) {
      toast.error('Fehler beim Laden der Eskalationsregeln');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (regel?: EskalationsRegel) => {
    if (regel) {
      setEditingRegel(regel);
      setFormData(JSON.parse(JSON.stringify(regel))); // Deep copy to avoid state mutation issues
    } else {
      setEditingRegel(null);
      setFormData({
        name: '',
        beschreibung: '',
        aktiv: true,
        prioritaet: 10,
        bedingungen: {},
        aktion: {},
      });
    }
    setIsDialogOpen(true);
  };

  const handleSave = async () => {
    if (!formData.name) {
      toast.error('Name der Regel ist ein Pflichtfeld.');
      return;
    }

    try {
      if (editingRegel) {
        await bereitschaftsService.updateEskalationsregel(editingRegel.id, formData as UpdateEskalationsRegelRequest);
        toast.success('Regel erfolgreich aktualisiert');
      } else {
        await bereitschaftsService.createEskalationsregel(formData as CreateEskalationsRegelRequest);
        toast.success('Regel erfolgreich erstellt');
      }
      fetchRegeln();
      setIsDialogOpen(false);
    } catch (error) {
      toast.error('Fehler beim Speichern der Regel.');
    }
  };

  const handleDelete = async (id: number) => {
    if (confirm('Möchten Sie diese Regel wirklich löschen?')) {
      try {
        await bereitschaftsService.deleteEskalationsregel(id);
        toast.success('Regel erfolgreich gelöscht');
        fetchRegeln();
      } catch (error) {
        toast.error('Fehler beim Löschen der Regel.');
      }
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center py-8"><RefreshCw className="h-6 w-6 animate-spin" /></div>;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5 text-orange-600" />
            Eskalationsmatrix
          </CardTitle>
          <Button onClick={() => handleOpenDialog()}>
            <Plus className="h-4 w-4 mr-2" />
            Neue Regel
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Priorität</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Aktiv</TableHead>
              <TableHead className="text-right">Aktionen</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {regeln.map((regel) => (
              <TableRow key={regel.id}>
                <TableCell>{regel.prioritaet}</TableCell>
                <TableCell>{regel.name}</TableCell>
                <TableCell>{regel.aktiv ? 'Ja' : 'Nein'}</TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button variant="ghost" size="sm" onClick={() => handleOpenDialog(regel)}><Edit className="h-4 w-4" /></Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(regel.id)} className="text-red-600 hover:text-red-700"><Trash2 className="h-4 w-4" /></Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingRegel ? 'Regel bearbeiten' : 'Neue Regel erstellen'}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">Name</Label>
              <Input id="name" value={formData.name || ''} onChange={(e) => setFormData({...formData, name: e.target.value})} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="beschreibung" className="text-right">Beschreibung</Label>
              <Textarea id="beschreibung" value={formData.beschreibung || ''} onChange={(e) => setFormData({...formData, beschreibung: e.target.value})} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="prioritaet" className="text-right">Priorität</Label>
                <Input id="prioritaet" type="number" value={formData.prioritaet || 10} onChange={(e) => setFormData({...formData, prioritaet: parseInt(e.target.value) || 10})} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="aktiv" className="text-right">Aktiv</Label>
                <Switch id="aktiv" checked={formData.aktiv} onCheckedChange={(checked) => setFormData({...formData, aktiv: checked})} />
            </div>
            
            <div>
                <h4 className="font-semibold mb-2">Bedingungen</h4>
                <div className="grid gap-4 pl-4 border-l-2">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="zeit" className="text-right">Zeit (Minuten)</Label>
                        <Input id="zeit" type="number" value={formData.bedingungen?.zeit_nach_erstellung_minuten || ''} onChange={(e) => setFormData({...formData, bedingungen: {...formData.bedingungen, zeit_nach_erstellung_minuten: parseInt(e.target.value) || undefined}})} className="col-span-3" />
                    </div>
                </div>
            </div>

            <div>
                <h4 className="font-semibold mb-2">Aktion</h4>
                <div className="grid gap-4 pl-4 border-l-2">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="neues_level" className="text-right">Neues Level</Label>
                        <Select value={formData.aktion?.neues_eskalationslevel} onValueChange={(value) => setFormData({...formData, aktion: {...formData.aktion, neues_eskalationslevel: value as Eskalationslevel}})}>
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Level auswählen" />
                            </SelectTrigger>
                            <SelectContent>
                                {Object.values(Eskalationslevel).map((level: string) => <SelectItem key={level} value={level}>{level}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                     <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="email" className="text-right">E-Mail senden an</Label>
                        <Input id="email" value={formData.aktion?.benachrichtige_email?.join(', ') || ''} onChange={(e) => setFormData({...formData, aktion: {...formData.aktion, benachrichtige_email: e.target.value.split(',').map(s => s.trim())}})} className="col-span-3" placeholder="komma-getrennte E-Mails" />
                    </div>
                </div>
            </div>

          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="secondary">Abbrechen</Button>
            </DialogClose>
            <Button onClick={handleSave}><Save className="h-4 w-4 mr-2" />Speichern</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};