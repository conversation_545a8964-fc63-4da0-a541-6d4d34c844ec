"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bereitschaftsRepository = exports.BereitschaftsRepository = void 0;
const client_1 = require("@prisma/client");
const date_fns_1 = require("date-fns");
const prisma = new client_1.PrismaClient();
class BereitschaftsRepository {
    // Personen verwalten
    async getAllPersonen() {
        return await prisma.bereitschaftsPerson.findMany({
            where: { aktiv: true },
            orderBy: { reihenfolge: 'asc' },
            include: {
                bereitschaftsWochen: {
                    where: { aktiv: true },
                    orderBy: { wochenStart: 'desc' },
                    take: 5
                }
            }
        });
    }
    async getPersonById(id) {
        return await prisma.bereitschaftsPerson.findUnique({
            where: { id },
            include: {
                bereitschaftsWochen: {
                    where: { aktiv: true },
                    orderBy: { wochenStart: 'desc' }
                },
                bereitschaftsAusnahmen: {
                    where: { aktiv: true },
                    orderBy: { von: 'desc' }
                }
            }
        });
    }
    async createPerson(data) {
        var _a, _b;
        const maxReihenfolge = await prisma.bereitschaftsPerson.aggregate({
            _max: { reihenfolge: true }
        });
        return await prisma.bereitschaftsPerson.create({
            data: {
                ...data,
                reihenfolge: (_a = data.reihenfolge) !== null && _a !== void 0 ? _a : ((_b = maxReihenfolge._max.reihenfolge) !== null && _b !== void 0 ? _b : 0) + 1
            }
        });
    }
    async updatePerson(id, data) {
        return await prisma.bereitschaftsPerson.update({
            where: { id },
            data
        });
    }
    async deletePerson(id) {
        return await prisma.bereitschaftsPerson.update({
            where: { id },
            data: { aktiv: false }
        });
    }
    async updatePersonenReihenfolge(personenIds) {
        const updates = personenIds.map((id, index) => prisma.bereitschaftsPerson.update({
            where: { id },
            data: { reihenfolge: index + 1 }
        }));
        return await prisma.$transaction(updates);
    }
    // Wochen verwalten
    async getWochenPlan(startDate, anzahlWochen) {
        const endDate = (0, date_fns_1.addWeeks)(startDate, anzahlWochen);
        return await prisma.bereitschaftsWoche.findMany({
            where: {
                aktiv: true,
                wochenStart: {
                    gte: startDate,
                    lt: endDate
                }
            },
            include: {
                person: true
            },
            orderBy: { wochenStart: 'asc' }
        });
    }
    async getAktuelleBereitschaft() {
        const heute = new Date();
        return await prisma.bereitschaftsWoche.findFirst({
            where: {
                aktiv: true,
                von: { lte: heute },
                bis: { gt: heute }
            },
            include: {
                person: true
            }
        });
    }
    async createWoche(data) {
        return await prisma.bereitschaftsWoche.create({
            data,
            include: {
                person: true
            }
        });
    }
    async updateWoche(id, data) {
        return await prisma.bereitschaftsWoche.update({
            where: { id },
            data,
            include: {
                person: true
            }
        });
    }
    async deleteWoche(id) {
        return await prisma.bereitschaftsWoche.update({
            where: { id },
            data: { aktiv: false }
        });
    }
    // Automatische Wochenplanung generieren
    async generiereWochenplan(startDate, anzahlWochen) {
        var _a, _b;
        const personen = await this.getAllPersonen();
        if (personen.length === 0) {
            throw new Error('Keine aktiven Bereitschaftspersonen gefunden');
        }
        const konfiguration = await this.getKonfiguration();
        const wechselTag = (_a = konfiguration === null || konfiguration === void 0 ? void 0 : konfiguration.wechselTag) !== null && _a !== void 0 ? _a : 5; // Freitag
        const wechselUhrzeit = (_b = konfiguration === null || konfiguration === void 0 ? void 0 : konfiguration.wechselUhrzeit) !== null && _b !== void 0 ? _b : '08:00';
        const wochen = [];
        for (let i = 0; i < anzahlWochen; i++) {
            const wochenStart = (0, date_fns_1.addWeeks)((0, date_fns_1.startOfWeek)(startDate, { weekStartsOn: 1 }), i);
            const wochenEnde = (0, date_fns_1.addWeeks)(wochenStart, 1);
            // Berechne Freitag der Woche
            const freitag = (0, date_fns_1.addDays)(wochenStart, wechselTag - 1);
            const naechsterFreitag = (0, date_fns_1.addDays)(freitag, 7);
            // Rotiere durch die Personen
            const personIndex = i % personen.length;
            const person = personen[personIndex];
            wochen.push({
                personId: person.id,
                wochenStart,
                wochenEnde,
                von: freitag,
                bis: naechsterFreitag,
                aktiv: true
            });
        }
        // Lösche bestehende Wochen im Zeitraum
        await prisma.bereitschaftsWoche.updateMany({
            where: {
                wochenStart: {
                    gte: (0, date_fns_1.startOfWeek)(startDate, { weekStartsOn: 1 }),
                    lt: (0, date_fns_1.addWeeks)((0, date_fns_1.startOfWeek)(startDate, { weekStartsOn: 1 }), anzahlWochen)
                }
            },
            data: { aktiv: false }
        });
        // Erstelle neue Wochen
        const erstellteWochen = await Promise.all(wochen.map(woche => this.createWoche(woche)));
        return erstellteWochen;
    }
    // Ausnahmen verwalten
    async getAllAusnahmen() {
        return await prisma.bereitschaftsAusnahme.findMany({
            where: { aktiv: true },
            include: {
                person: true
            },
            orderBy: { von: 'desc' }
        });
    }
    async createAusnahme(data) {
        return await prisma.bereitschaftsAusnahme.create({
            data,
            include: {
                person: true
            }
        });
    }
    async updateAusnahme(id, data) {
        return await prisma.bereitschaftsAusnahme.update({
            where: { id },
            data,
            include: {
                person: true
            }
        });
    }
    async deleteAusnahme(id) {
        return await prisma.bereitschaftsAusnahme.update({
            where: { id },
            data: { aktiv: false }
        });
    }
    // Konfiguration verwalten
    async getKonfiguration() {
        const config = await prisma.bereitschaftsKonfiguration.findFirst({
            orderBy: { createdAt: 'desc' }
        });
        // Standardkonfiguration falls keine existiert
        if (!config) {
            return await this.createKonfiguration({});
        }
        // Map database fields to expected frontend fields
        return {
            id: config.id,
            wechselTag: config.wechsel_tag,
            wechselUhrzeit: config.wechselUhrzeit,
            rotationAktiv: config.rotationAktiv,
            benachrichtigungTage: config.benachrichtigungTage,
            emailBenachrichtigung: config.emailBenachrichtigung,
            createdAt: config.createdAt.toISOString(),
            updatedAt: config.updatedAt.toISOString()
        };
    }
    async createKonfiguration(data) {
        var _a, _b, _c, _d, _e;
        const created = await prisma.bereitschaftsKonfiguration.create({
            data: {
                wechsel_tag: (_a = data.wechselTag) !== null && _a !== void 0 ? _a : 5,
                wechselUhrzeit: (_b = data.wechselUhrzeit) !== null && _b !== void 0 ? _b : '08:00',
                rotationAktiv: (_c = data.rotationAktiv) !== null && _c !== void 0 ? _c : true,
                benachrichtigungTage: (_d = data.benachrichtigungTage) !== null && _d !== void 0 ? _d : 2,
                emailBenachrichtigung: (_e = data.emailBenachrichtigung) !== null && _e !== void 0 ? _e : true
            }
        });
        // Return mapped result
        return {
            id: created.id,
            wechselTag: created.wechsel_tag,
            wechselUhrzeit: created.wechselUhrzeit,
            rotationAktiv: created.rotationAktiv,
            benachrichtigungTage: created.benachrichtigungTage,
            emailBenachrichtigung: created.emailBenachrichtigung,
            createdAt: created.createdAt.toISOString(),
            updatedAt: created.updatedAt.toISOString()
        };
    }
    async updateKonfiguration(data) {
        const existingConfig = await prisma.bereitschaftsKonfiguration.findFirst({
            orderBy: { createdAt: 'desc' }
        });
        if (!existingConfig) {
            throw new Error('Keine Konfiguration gefunden');
        }
        // Map frontend fields to database fields
        const updateData = {};
        if (data.wechselTag !== undefined)
            updateData.wechsel_tag = data.wechselTag;
        if (data.wechselUhrzeit !== undefined)
            updateData.wechselUhrzeit = data.wechselUhrzeit;
        if (data.rotationAktiv !== undefined)
            updateData.rotationAktiv = data.rotationAktiv;
        if (data.benachrichtigungTage !== undefined)
            updateData.benachrichtigungTage = data.benachrichtigungTage;
        if (data.emailBenachrichtigung !== undefined)
            updateData.emailBenachrichtigung = data.emailBenachrichtigung;
        const updated = await prisma.bereitschaftsKonfiguration.update({
            where: { id: existingConfig.id },
            data: updateData
        });
        // Return mapped result
        return {
            id: updated.id,
            wechselTag: updated.wechsel_tag,
            wechselUhrzeit: updated.wechselUhrzeit,
            rotationAktiv: updated.rotationAktiv,
            benachrichtigungTage: updated.benachrichtigungTage,
            emailBenachrichtigung: updated.emailBenachrichtigung,
            createdAt: updated.createdAt.toISOString(),
            updatedAt: updated.updatedAt.toISOString()
        };
    }
    // Hilfsmethoden
    async getPersonenInZeitraum(von, bis) {
        return await prisma.bereitschaftsWoche.findMany({
            where: {
                aktiv: true,
                OR: [
                    {
                        von: { lte: bis },
                        bis: { gte: von }
                    }
                ]
            },
            include: {
                person: true
            },
            orderBy: { von: 'asc' }
        });
    }
    async validateWochenplan(wochen) {
        const errors = [];
        // Prüfe auf Überschneidungen
        for (let i = 0; i < wochen.length; i++) {
            for (let j = i + 1; j < wochen.length; j++) {
                const woche1 = wochen[i];
                const woche2 = wochen[j];
                if (woche1.von < woche2.bis && woche1.bis > woche2.von) {
                    errors.push(`Überschneidung zwischen Woche ${i + 1} und ${j + 1}`);
                }
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.BereitschaftsRepository = BereitschaftsRepository;
exports.bereitschaftsRepository = new BereitschaftsRepository();
