"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoerungenRepository = void 0;
// Simple in-memory cache for störungen
const simpleCache = new Map();
const getCached = (key) => {
    const entry = simpleCache.get(key);
    if (entry && entry.expires > Date.now()) {
        return entry.data;
    }
    simpleCache.delete(key);
    return null;
};
const setCache = (key, data, ttlMs = 300000) => {
    simpleCache.set(key, { data, expires: Date.now() + ttlMs });
};
const clearCachePattern = (pattern) => {
    for (const key of simpleCache.keys()) {
        if (key.includes(pattern)) {
            simpleCache.delete(key);
        }
    }
};
class StoerungenRepository {
    constructor(prisma) {
        this.prisma = prisma;
    }
    static getInstance(prisma) {
        if (!StoerungenRepository.instance) {
            StoerungenRepository.instance = new StoerungenRepository(prisma);
        }
        return StoerungenRepository.instance;
    }
    async createStoerung(data) {
        const tagsJson = data.tags ? JSON.stringify(data.tags) : null;
        const stoerung = await this.prisma.stoerungen.create({
            data: {
                ...data,
                tags: tagsJson,
                updated_at: new Date(),
            },
        });
        // Fetch comments separately
        const comments = await this.prisma.stoerungsComments.findMany({
            where: { stoerung_id: stoerung.id },
            orderBy: { created_at: 'desc' },
        });
        clearCachePattern('stoerungen');
        return this.formatStoerung({ ...stoerung, comments });
    }
    async getStoerungen(options) {
        const cacheKey = `stoerungen:list:${JSON.stringify(options || {})}`;
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const where = {};
        if (options === null || options === void 0 ? void 0 : options.status)
            where.status = options.status;
        if (options === null || options === void 0 ? void 0 : options.severity)
            where.severity = options.severity;
        if (options === null || options === void 0 ? void 0 : options.category)
            where.category = options.category;
        if (options === null || options === void 0 ? void 0 : options.affected_system)
            where.affected_system = options.affected_system;
        const stoerungen = await this.prisma.stoerungen.findMany({
            where,
            orderBy: { created_at: 'desc' },
            take: options === null || options === void 0 ? void 0 : options.limit,
            skip: options === null || options === void 0 ? void 0 : options.offset,
        });
        // Fetch comments for each störung
        const stoerungsWithComments = await Promise.all(stoerungen.map(async (stoerung) => {
            const comments = await this.prisma.stoerungsComments.findMany({
                where: { stoerung_id: stoerung.id },
                orderBy: { created_at: 'desc' },
            });
            return { ...stoerung, comments };
        }));
        const formatted = stoerungsWithComments.map(this.formatStoerung);
        setCache(cacheKey, formatted, 300000); // 5 minutes TTL
        return formatted;
    }
    async getStoerungById(id) {
        const cacheKey = `stoerungen:detail:${id}`;
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const stoerung = await this.prisma.stoerungen.findUnique({
            where: { id },
        });
        if (!stoerung)
            return null;
        // Fetch comments separately
        const comments = await this.prisma.stoerungsComments.findMany({
            where: { stoerung_id: stoerung.id },
            orderBy: { created_at: 'desc' },
        });
        const formatted = this.formatStoerung({ ...stoerung, comments });
        setCache(cacheKey, formatted, 300000);
        return formatted;
    }
    async updateStoerung(id, data) {
        const updateData = { ...data };
        if (data.tags) {
            updateData.tags = JSON.stringify(data.tags);
        }
        if (data.status === 'RESOLVED' && !data.resolved_at) {
            updateData.resolved_at = new Date();
        }
        const stoerung = await this.prisma.stoerungen.update({
            where: { id },
            data: updateData,
        });
        // Fetch comments separately
        const comments = await this.prisma.stoerungsComments.findMany({
            where: { stoerung_id: stoerung.id },
            orderBy: { created_at: 'desc' },
        });
        clearCachePattern('stoerungen');
        return this.formatStoerung({ ...stoerung, comments });
    }
    async deleteStoerung(id) {
        try {
            await this.prisma.stoerungen.delete({
                where: { id },
            });
            clearCachePattern('stoerungen');
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    async addComment(data) {
        const comment = await this.prisma.stoerungsComments.create({
            data,
        });
        clearCachePattern('stoerungen');
        return comment;
    }
    async getStoerungsStats() {
        const cacheKey = 'stoerungen:stats';
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const [total, active, resolved, bySeverity, avgMttr, recent] = await Promise.all([
            this.prisma.stoerungen.count(),
            this.prisma.stoerungen.count({ where: { status: { in: ['NEW', 'IN_PROGRESS'] } } }),
            this.prisma.stoerungen.count({ where: { status: 'RESOLVED' } }),
            this.prisma.stoerungen.groupBy({
                by: ['severity'],
                _count: { severity: true },
            }),
            this.prisma.stoerungen.aggregate({
                _avg: { mttr_minutes: true },
                where: { status: 'RESOLVED', mttr_minutes: { not: null } },
            }),
            this.prisma.stoerungen.count({
                where: {
                    status: 'RESOLVED',
                    resolved_at: {
                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24h
                    },
                },
            }),
        ]);
        const severityCounts = bySeverity.reduce((acc, item) => {
            acc[item.severity.toLowerCase()] = item._count.severity;
            return acc;
        }, {});
        const stats = {
            total,
            active,
            resolved,
            critical: severityCounts.critical || 0,
            high: severityCounts.high || 0,
            medium: severityCounts.medium || 0,
            low: severityCounts.low || 0,
            avg_mttr_minutes: Math.round(avgMttr._avg.mttr_minutes || 0),
            resolution_rate_24h: recent,
        };
        setCache(cacheKey, stats, 900000); // 15 minutes TTL
        return stats;
    }
    async getSystemStatus() {
        const cacheKey = 'system:status:all';
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const statuses = await this.prisma.systemStatus.findMany({
            orderBy: { system_name: 'asc' },
        });
        const formatted = statuses.map(status => ({
            ...status,
            status: status.status,
            metadata: status.metadata ? JSON.parse(status.metadata) : null,
        }));
        setCache(cacheKey, formatted, 30000); // 30 seconds TTL for live data
        return formatted;
    }
    async updateSystemStatus(data) {
        const metadataJson = data.metadata ? JSON.stringify(data.metadata) : null;
        // First try to find existing record
        const existingStatus = await this.prisma.systemStatus.findFirst({
            where: { system_name: data.system_name }
        });
        let status;
        if (existingStatus) {
            // Update existing record
            status = await this.prisma.systemStatus.update({
                where: { id: existingStatus.id },
                data: {
                    status: data.status,
                    metadata: metadataJson,
                    last_check: new Date(),
                    updated_at: new Date(),
                },
            });
        }
        else {
            // Create new record
            status = await this.prisma.systemStatus.create({
                data: {
                    system_name: data.system_name,
                    status: data.status,
                    metadata: metadataJson,
                    updated_at: new Date(),
                },
            });
        }
        clearCachePattern('system:status');
        return {
            ...status,
            status: status.status,
            metadata: status.metadata ? JSON.parse(status.metadata) : null,
        };
    }
    formatStoerung(stoerung) {
        let tags = [];
        try {
            if (stoerung.tags) {
                if (typeof stoerung.tags === 'string') {
                    // Try to parse as JSON first
                    try {
                        tags = JSON.parse(stoerung.tags);
                    }
                    catch (_a) {
                        // If JSON parsing fails, split by comma
                        tags = stoerung.tags.split(',').map((tag) => tag.trim()).filter(Boolean);
                    }
                }
                else if (Array.isArray(stoerung.tags)) {
                    tags = stoerung.tags;
                }
            }
        }
        catch (error) {
            console.error('Error parsing tags for störung:', stoerung.id, error);
            tags = [];
        }
        return {
            ...stoerung,
            tags,
        };
    }
}
exports.StoerungenRepository = StoerungenRepository;
