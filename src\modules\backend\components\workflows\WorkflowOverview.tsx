import React from 'react';
import { Activity, Database, Clock, CheckCircle, AlertTriangle, TrendingUp, Zap, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useWorkflows } from '@/hooks/useWorkflows';

export function WorkflowOverview() {
  const { processes, stats, loading, error } = useWorkflows();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  const getSystemHealthStatus = () => {
    const runningProcesses = processes.filter(p => p.status === 'running').length;
    const errorProcesses = processes.filter(p => p.status === 'error').length;
    const totalProcesses = processes.length;
    
    if (errorProcesses > totalProcesses / 2) return { status: 'critical', color: 'red' };
    if (errorProcesses > 0 || runningProcesses > 0) return { status: 'warning', color: 'yellow' };
    return { status: 'healthy', color: 'green' };
  };

  const systemHealth = getSystemHealthStatus();
  const successRate = stats ? (stats.totalExecutions > 0 ? (stats.successfulExecutions / stats.totalExecutions) * 100 : 0) : 0;

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* System Status Overview (Top 3 Cards im Bento-Stil - erweitert inspiriert von PerformanceDashboard) */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* System Status Bento Card */}
        <div className="group relative border-2 rounded-xl px-6 pt-6 pb-6 shadow-md bg-white hover:border-blue-400/60 transition-all duration-200 overflow-hidden">
          {/* Pattern nur rechts oben (kleiner Quadrant) */}
          <div className="absolute right-0 top-0 z-0 h-24 w-32 md:h-28 md:w-36 bg-[linear-gradient(to_right,rgba(59,130,246,0.18)_1px,transparent_1px),linear-gradient(to_bottom,rgba(59,130,246,0.18)_1px,transparent_1px)] bg-[size:16px_16px] rounded-bl-xl"></div>
          {/* sanfter Bottom-Glow */}
          <div className="pointer-events-none absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-blue-500/15 to-transparent blur-xl"></div>
          <div className="relative z-10">
            <div className="bg-blue-500/10 text-blue-600 shadow-blue-500/10 group-hover:bg-blue-500/25 group-hover:shadow-blue-500/25 mb-4 flex h-10 w-10 items-center justify-center rounded-full shadow transition-all duration-150">
              <Activity className="h-5 w-5" />
            </div>
            <h3 className="mb-2 text-lg font-semibold tracking-tight">System Status</h3>
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                systemHealth.color === 'green' ? 'bg-green-500' :
                systemHealth.color === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
              } animate-pulse`}></div>
              <span className="font-medium capitalize">
                {systemHealth.status === 'healthy' ? 'Gesund' :
                 systemHealth.status === 'warning' ? 'Warnung' : 'Kritisch'}
              </span>
            </div>
            <div className="mt-4 space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Aktive Prozesse:</span>
                <span className="font-semibold">{processes.filter(p => p.status === 'running').length}</span>
              </div>
              <div className="flex justify-between">
                <span>Fehlerhafte Prozesse:</span>
                <span className="font-semibold text-red-600">{processes.filter(p => p.status === 'error').length}</span>
              </div>
            </div>
          </div>
          {/* Dekoratives Icon unten rechts (Teil des Icons, sehr dezent) */}
          <div aria-hidden className="pointer-events-none absolute -right-2 -bottom-4 opacity-10 group-hover:opacity-20 transition-opacity duration-150 text-blue-500">
            {/* Kreis als Basis */}
            <div className="relative">
              <div className="h-12 w-12 rounded-full border-2 border-current opacity-40"></div>
              {/* innerer Halbkreis */}
              <div className="absolute right-1 bottom-1 h-8 w-8 rounded-full border-2 border-current opacity-30"></div>
              {/* kleiner Balken als “Füllstand” */}
              <div className="absolute right-2 bottom-2 h-6 w-2 rounded bg-current opacity-30"></div>
            </div>
          </div>
        </div>

        {/* Performance Bento Card */}
        <div className="group relative border-2 rounded-xl px-6 pt-6 pb-6 shadow-md bg-white hover:border-green-400/60 transition-all duration-200 overflow-hidden">
          {/* Pattern nur rechts oben */}
          <div className="absolute right-0 top-0 z-0 h-24 w-32 md:h-28 md:w-36 bg-[linear-gradient(to_right,rgba(34,197,94,0.18)_1px,transparent_1px),linear-gradient(to_bottom,rgba(34,197,94,0.18)_1px,transparent_1px)] bg-[size:16px_16px] rounded-bl-xl"></div>
          {/* sanfter Bottom-Glow */}
          <div className="pointer-events-none absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-green-500/15 to-transparent blur-xl"></div>
          <div className="relative z-10">
            <div className="bg-green-500/10 text-green-600 shadow-green-500/10 group-hover:bg-green-500/25 group-hover:shadow-green-500/25 mb-4 flex h-10 w-10 items-center justify-center rounded-full shadow transition-all duration-150">
              <TrendingUp className="h-5 w-5" />
            </div>
            <h3 className="mb-2 text-lg font-semibold tracking-tight">Performance</h3>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Erfolgsrate</span>
                  <span className="font-semibold">{successRate.toFixed(1)}%</span>
                </div>
                {/* Fixe Höhe -> KEIN ResponsiveContainer nötig, daher nur plain Progress */}
                <Progress value={successRate} className="h-2" />
              </div>
              <div className="flex justify-between text-sm">
                <span>Ø Ausführungszeit:</span>
                <span className="font-semibold">{formatDuration(stats?.averageDuration || 0)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Gesamt Ausführungen:</span>
                <span className="font-semibold">{stats?.totalExecutions || 0}</span>
              </div>
            </div>
          </div>
          {/* Dekoratives Icon unten rechts (Teil des Icons, sehr dezent) */}
          <div aria-hidden className="pointer-events-none absolute -right-2 -bottom-4 opacity-10 group-hover:opacity-20 transition-opacity duration-150 text-green-500">
            <div className="relative">
              {/* “Chart”-Form mit Kreis und Balken */}
              <div className="h-10 w-10 rounded-full border-2 border-current opacity-40"></div>
              <div className="absolute right-1 bottom-1 h-7 w-2 rounded bg-current opacity-30"></div>
              <div className="absolute right-3 bottom-3 h-4 w-2 rounded bg-current opacity-20"></div>
              <div className="absolute right-5 bottom-2 h-5 w-2 rounded bg-current opacity-25"></div>
            </div>
          </div>
        </div>

        {/* Letzte Aktivität Bento Card */}
        <div className="group relative border-2 rounded-xl px-6 pt-6 pb-6 shadow-md bg-white hover:border-orange-400/60 transition-all duration-200 overflow-hidden">
          {/* Pattern nur rechts oben */}
          <div className="absolute right-0 top-0 z-0 h-24 w-32 md:h-28 md:w-36 bg-[linear-gradient(to_right,rgba(249,115,22,0.18)_1px,transparent_1px),linear-gradient(to_bottom,rgba(249,115,22,0.18)_1px,transparent_1px)] bg-[size:16px_16px] rounded-bl-xl"></div>
          {/* sanfter Bottom-Glow */}
          <div className="pointer-events-none absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-orange-500/15 to-transparent blur-xl"></div>
          <div className="relative z-10">
            <div className="bg-orange-500/10 text-orange-600 shadow-orange-500/10 group-hover:bg-orange-500/25 group-hover:shadow-orange-500/25 mb-4 flex h-10 w-10 items-center justify-center rounded-full shadow transition-all duration-150">
              <Clock className="h-5 w-5" />
            </div>
            {/* kleine, dezente Deko unten rechts – kein großes Icon */}
            <div aria-hidden className="pointer-events-none absolute right-2 bottom-2 opacity-10 group-hover:opacity-20 transition-opacity duration-150 text-orange-500">
              <span className="block h-2 w-2 rounded-full bg-current" />
            </div>
            <h3 className="mb-2 text-lg font-semibold tracking-tight">Letzte Aktivität</h3>
            <div className="space-y-2 text-sm">
              {stats?.lastExecution ? (
                <>
                  <p className="text-gray-600">Letzte Ausführung:</p>
                  <p className="font-medium">{stats.lastExecution.toLocaleString('de-DE')}</p>
                </>
              ) : (
                <p className="text-gray-500">Keine Ausführungen</p>
              )}
            </div>
          </div>
          {/* Dekoratives Icon unten rechts (Teil des Icons, sehr dezent) */}
          <div aria-hidden className="pointer-events-none absolute -right-2 -bottom-4 opacity-10 group-hover:opacity-20 transition-opacity duration-150 text-orange-500">
            <div className="relative">
              {/* Uhr-ähnliches Icon */}
              <div className="h-12 w-12 rounded-full border-2 border-current opacity-40"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-0.5 w-4 bg-current opacity-30 translate-x-1"></div>
                <div className="absolute h-4 w-0.5 bg-current opacity-30 -translate-y-1"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Systems */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* SAP Workflows */}
        <Card className="border-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-600" />
              SAP Workflows
            </CardTitle>
            <CardDescription>
              Automatisierte SAP-Datenexport-Prozesse
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Prozesse:</span>
                <span className="ml-2 font-medium">{processes.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Aktiviert:</span>
                <span className="ml-2 font-medium">{processes.filter(p => p.isActive).length}</span>
              </div>
            </div>
            
            <div className="space-y-2">
              {processes.slice(0, 3).map((process) => (
                <div key={process.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm font-medium">{process.name}</span>
                  <div className="flex items-center gap-2">
                    {/* Aktivierungsstatus Badge */}
                    <Badge 
                      variant="outline"
                      className={
                        process.isActive 
                          ? 'bg-green-100 text-green-800 border-green-300' 
                          : 'bg-gray-100 text-gray-600 border-gray-300'
                      }
                    >
                      {process.isActive ? 'Aktiviert' : 'Deaktiviert'}
                    </Badge>
                    {/* Ausführungsstatus Badge */}
                    <Badge 
                      variant={process.status === 'completed' ? 'secondary' : 
                              process.status === 'error' ? 'destructive' : 'outline'}
                      className={process.status === 'completed' ? 'bg-blue-100 text-blue-800' : ''}
                    >
                      {process.status === 'running' ? 'Läuft' :
                       process.status === 'completed' ? 'Bereit' :
                       process.status === 'error' ? 'Fehler' : 'Bereit'}
                    </Badge>
                  </div>
                </div>
              ))}
              {processes.length > 3 && (
                <p className="text-xs text-gray-500 text-center">
                  +{processes.length - 3} weitere Prozesse
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Future Workflow Systems */}
        <Card className="border-2 border-dashed border-gray-300">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-500">
              <Zap className="h-5 w-5" />
              Weitere Workflow-Systeme
            </CardTitle>
            <CardDescription>
              Zukünftige Automatisierungsprozesse
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8">
              <Settings className="h-8 w-8 mx-auto mb-3 text-gray-400" />
              <p className="text-sm text-gray-500 mb-4">
                Hier können weitere Workflow-Systeme integriert werden
              </p>
              <div className="space-y-2 text-xs text-gray-400">
                <p>• Email-Automatisierung</p>
                <p>• Datenbank-Synchronisation</p>
                <p>• Report-Generierung</p>
                <p>• API-Integrationen</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Schnellaktionen
          </CardTitle>
          <CardDescription>
            Häufig verwendete Workflow-Operationen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" size="sm">
              <CheckCircle className="h-4 w-4 mr-2" />
              Alle Status prüfen
            </Button>
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              System-Health-Check
            </Button>
            <Button variant="outline" size="sm">
              <Clock className="h-4 w-4 mr-2" />
              Letzte Logs anzeigen
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Konfiguration prüfen
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}