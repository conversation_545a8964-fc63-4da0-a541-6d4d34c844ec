import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import {
  Activity,
  Database,
  Zap,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import apiService from "@/services/api.service";

interface PerformanceMetrics {
  overview: {
    healthScore: number;
    totalQueries: number;
    averageQueryTime: number;
    cacheHitRate: number;
    systemUptime: number;
  };
  database: {
    totalQueries: number;
    averageQueryTime: number;
    slowQueries: number;
    failedQueries: number;
  };
  cache: {
    hitRate: number;
    missRate: number;
    memoryUsage: number;
    totalEntries: number;
  };
  system: {
    memoryUsage: {
      heapUsed: number;
      heapTotal: number;
      external: number;
      rss: number;
    };
    cpuUsage: number;
    uptime: number;
    eventLoopLag: number;
  };
}

export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const [healthResponse, statsResponse, cacheResponse] = await Promise.all([
        apiService.get<any>("/performance/health"),
        apiService.get<any>("/performance/stats"),
        apiService.get<any>("/performance/cache")
      ]);

      // Extract data from API responses
      const health = healthResponse.data || healthResponse;
      const stats = statsResponse.data || statsResponse;
      const cache = cacheResponse.data || cacheResponse;



      // Get system info from Electron's process if available
      const getSystemInfo = () => {
        try {
          if (typeof window !== 'undefined' && (window as any).electronAPI) {
            // Use Electron's exposed API if available
            return {
              uptime: (window as any).electronAPI.getUptime?.() || 0,
              memoryUsage: (window as any).electronAPI.getMemoryUsage?.() || {
                heapUsed: 0,
                heapTotal: 0,
                external: 0,
                rss: 0
              }
            };
          } else if (typeof process !== 'undefined') {
            // Direct access to process (if nodeIntegration is enabled)
            return {
              uptime: process.uptime(),
              memoryUsage: process.memoryUsage()
            };
          }
        } catch (e) {
          console.warn('Could not access system info:', e);
        }
        return {
          uptime: 0,
          memoryUsage: { heapUsed: 0, heapTotal: 0, external: 0, rss: 0 }
        };
      };

      const systemInfo = getSystemInfo();

      setMetrics({
        overview: {
          healthScore: health.status === 'healthy' ? 95 : health.status === 'warning' ? 75 : 45,
          totalQueries: health.performance?.totalRequests || stats.totalRequests || 0,
          averageQueryTime: health.performance?.averageResponseTime || stats.averageResponseTime || 0,
          cacheHitRate: health.cache?.hitRate || cache.stats?.hitRate || 0,
          systemUptime: systemInfo.uptime
        },
        database: {
          totalQueries: health.performance?.totalRequests || stats.totalRequests || 0,
          averageQueryTime: health.performance?.averageResponseTime || stats.averageResponseTime || 0,
          slowQueries: stats.slowQueries || 0,
          failedQueries: stats.failedRequests || 0
        },
        cache: {
          hitRate: health.cache?.hitRate || cache.stats?.hitRate || 0,
          missRate: cache.stats?.missRate || 0,
          memoryUsage: health.cache?.totalSize || cache.stats?.totalSize || 0,
          totalEntries: health.cache?.totalEntries || cache.stats?.totalEntries || 0
        },
        system: {
          memoryUsage: systemInfo.memoryUsage,
          cpuUsage: 0, // Not available from current endpoints
          uptime: systemInfo.uptime,
          eventLoopLag: 0 // Not available from current endpoints
        }
      });
    } catch (err) {
      setError("Fehler beim Laden der Performance-Metriken");
      console.error("Performance metrics error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    // Longer interval to reduce load when backend is offline
    const interval = setInterval(fetchMetrics, 60000); // Update every 60 seconds
    return () => clearInterval(interval);
  }, []);

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500";
    if (score >= 70) return "text-yellow-500";
    return "text-red-500";
  };

  const getHealthScoreBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 70) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };



  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="h-20 bg-gray-200 rounded-t-lg"></CardHeader>
            <CardContent className="h-32 bg-gray-100"></CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <Card className="border-red-200">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>{error || "Performance-Daten nicht verfügbar"}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.12,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  const PerformanceCard = ({
    children,
    className,
    icon,
    title,
    color = "blue"
  }: {
    children: React.ReactNode;
    className?: string;
    icon: React.ReactNode;
    title: string;
    color?: string;
  }) => (
    <motion.div
      variants={itemVariants}
      whileHover={{ 
        scale: 1.02,
        y: -4
      }}
      transition={{ 
        duration: 0.15,
        ease: "easeInOut"
      }}
      className={cn(
        'group border-primary/10 bg-background hover:border-primary/30 hover:shadow-lg relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border px-6 pt-6 pb-6 shadow-md transition-all duration-150',
        className,
      )}
    >
      <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      <div className={cn(
        "text-primary/5 group-hover:text-primary/15 absolute right-1 bottom-3 scale-[6] transition-all duration-200 group-hover:scale-[6.5]",
        color === "blue" && "text-blue-500/5 group-hover:text-blue-500/15",
        color === "green" && "text-green-500/5 group-hover:text-green-500/15",
        color === "purple" && "text-purple-500/5 group-hover:text-purple-500/15",
        color === "orange" && "text-orange-500/5 group-hover:text-orange-500/15"
      )}>
        {icon}
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        <div>
          <motion.div 
            className={cn(
              "bg-primary/10 text-primary shadow-primary/10 group-hover:bg-primary/25 group-hover:shadow-primary/25 mb-4 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-150",
              color === "blue" && "bg-blue-500/10 text-blue-600 shadow-blue-500/10 group-hover:bg-blue-500/25 group-hover:shadow-blue-500/25",
              color === "green" && "bg-green-500/10 text-green-600 shadow-green-500/10 group-hover:bg-green-500/25 group-hover:shadow-green-500/25",
              color === "purple" && "bg-purple-500/10 text-purple-600 shadow-purple-500/10 group-hover:bg-purple-500/25 group-hover:shadow-purple-500/25",
              color === "orange" && "bg-orange-500/10 text-orange-600 shadow-orange-500/10 group-hover:bg-orange-500/25 group-hover:shadow-orange-500/25"
            )}
            whileHover={{ 
              scale: 1.02,
              rotate: 4
            }}
            transition={{ 
              duration: 0.15,
              ease: "easeInOut"
            }}
          >
            {icon}
          </motion.div>
          <h3 className="mb-2 text-xl font-semibold tracking-tight">{title}</h3>
          {children}
        </div>
      </div>
      <div className={cn(
        "from-primary to-primary/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-150 group-hover:blur-sm group-hover:h-2",
        color === "blue" && "from-blue-500 to-blue-500/30",
        color === "green" && "from-green-500 to-green-500/30",
        color === "purple" && "from-purple-500 to-purple-500/30",
        color === "orange" && "from-orange-500 to-orange-500/30"
      )} />
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Metrics Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* System Health Score */}
        <PerformanceCard
          icon={<Activity className="h-5 w-5" />}
          title="Health Score"
          color="blue"
        >
          <p className="text-muted-foreground text-sm mb-4">System-Gesamtzustand</p>
          <div className="text-center space-y-3">
            <div className={`text-3xl font-bold ${getHealthScoreColor(Number(metrics.overview?.healthScore || 0))}`}>
              {metrics.overview?.healthScore || 0}%
            </div>
            {getHealthScoreBadge(Number(metrics.overview?.healthScore || 0))}
            <Progress
              value={Number(metrics.overview?.healthScore || 0)}
              className="h-2"
            />
            <div className="text-xs text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>Abfragen:</span>
                <span className={metrics.database?.totalQueries ? "text-green-600" : "text-red-600"}>
                  {metrics.database?.totalQueries ? "✓" : "✗"}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Cache:</span>
                <span className={Number(metrics.cache?.hitRate || 0) > 0 ? "text-green-600" : "text-red-600"}>
                  {Number(metrics.cache?.hitRate || 0) > 0 ? "✓" : "✗"}
                </span>
              </div>
            </div>
          </div>
        </PerformanceCard>
        {/* Database Performance */}
        <PerformanceCard
          icon={<Database className="h-5 w-5" />}
          title="Database"
          color="green"
        >
          <p className="text-muted-foreground text-sm mb-4">Abfrageleistung & Zuverlässigkeit</p>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Ø Abfragezeit</span>
              <span className="font-bold text-green-600">
                {Number(metrics.database?.averageQueryTime || 0).toFixed(1)}ms
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Abfragen</span>
              <span className="font-bold">{(metrics.database?.totalQueries || 0).toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Fehler</span>
              <div className="flex items-center gap-1">
                {Number(metrics.database?.failedQueries || 0) === 0 ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                )}
                <span className={Number(metrics.database?.failedQueries || 0) === 0 ? "text-green-600" : "text-red-600"}>
                  {metrics.database?.failedQueries || 0}
                </span>
              </div>
            </div>
          </div>
        </PerformanceCard>

        {/* Cache Performance */}
        <PerformanceCard
          icon={<Zap className="h-5 w-5" />}
          title="Cache"
          color="purple"
        >
          <p className="text-muted-foreground text-sm mb-4">Trefferquote & Speicherauslastung</p>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Trefferquote</span>
              <div className="flex items-center gap-1">
                {Number(metrics.cache?.hitRate || 0) >= 80 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={`font-bold ${Number(metrics.cache?.hitRate || 0) >= 80 ? "text-green-600" : "text-red-600"}`}>
                  {Number(metrics.cache?.hitRate || 0).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Speicher</span>
              <span className="font-bold text-purple-600">
                {formatBytes(metrics.cache?.memoryUsage || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Einträge</span>
              <span className="font-bold">{(metrics.cache?.totalEntries || 0).toLocaleString()}</span>
            </div>
          </div>
        </PerformanceCard>

        {/* System Resources */}
        <PerformanceCard
          icon={<Clock className="h-5 w-5" />}
          title="System"
          color="orange"
        >
          <p className="text-muted-foreground text-sm mb-4">Systemressourcen</p>
          <div className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Speicher</span>
                <span className="font-bold text-orange-600">
                  {formatBytes(metrics.system?.memoryUsage?.heapUsed || 0)}
                </span>
              </div>
              <Progress
                value={metrics.system?.memoryUsage?.heapTotal ?
                  (metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) * 100 : 0}
                className="h-2"
              />
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Event Loop</span>
              <span className={`font-bold ${Number(metrics.system?.eventLoopLag || 0) < 10 ? "text-green-600" : "text-red-600"}`}>
                {Number(metrics.system?.eventLoopLag || 0).toFixed(1)}ms
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Uptime</span>
              <span className="font-bold">{formatUptime(metrics.system?.uptime || 0)}</span>
            </div>
          </div>
        </PerformanceCard>
      </motion.div>
    </div>
  );
};