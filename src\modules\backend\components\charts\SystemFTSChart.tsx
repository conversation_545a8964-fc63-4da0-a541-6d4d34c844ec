import React, { useEffect, useState, memo } from "react";
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, ResponsiveContainer } from "recharts";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import apiService from "@/services/api.service";
import { SystemFTSDataPoint } from "@/types/database.types";

interface SystemFTSChartProps {
  dateRange?: DateRange;
}

export const SystemFTSChart = memo(function SystemFTSChart({ dateRange }: SystemFTSChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<SystemFTSDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const chartConfig = {
    verfuegbarkeitFTS: { 
      label: "FTS Verfügbarkeit", 
      color: "#ff7a05" // SFM Orange
    }
  };

  useEffect(() => {
    loadData();
  }, [dateRange]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("SystemFTSChart: Starte Datenladung...");

      // Konvertiere DateRange zu String-Format für API
      const startDate = dateRange?.from ? 
        `${dateRange.from.getFullYear()}-${String(dateRange.from.getMonth() + 1).padStart(2, '0')}-${String(dateRange.from.getDate()).padStart(2, '0')}` 
        : undefined;
      const endDate = dateRange?.to ? 
        `${dateRange.to.getFullYear()}-${String(dateRange.to.getMonth() + 1).padStart(2, '0')}-${String(dateRange.to.getDate()).padStart(2, '0')}` 
        : undefined;

      console.log("SystemFTSChart: API-Aufruf mit Parametern:", { startDate, endDate });
      
      // Echte Backend API aufrufen
      const result = await apiService.getSystemFTSData(startDate, endDate);
      
      console.log("SystemFTSChart: Erhaltene Daten von Backend:", result);
      console.log("SystemFTSChart: Anzahl Datensätze:", result.length);

      // Transformiere Backend-Daten für Chart (Format Datum umwandeln)
      const transformedData = result.map(item => ({
        ...item,
        // Konvertiere YYYY-MM-DD zu DD.MM.YYYY für Chart-Anzeige
        Datum: formatDateForChart(item.Datum)
      }));

      setChartData(transformedData);
      
    } catch (error) {
      console.error("SystemFTSChart: Fehler beim Laden der Daten:", error);
      setError(`Fehler beim Laden der FTS-Verfügbarkeitsdaten: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
    } finally {
      setLoading(false);
    }
  };

  // Hilfsfunktion: YYYY-MM-DD zu DD.MM.YYYY formatieren
  const formatDateForChart = (dateStr: string): string => {
    try {
      // Wenn es bereits im DD.MM.YYYY Format ist, unverändert zurückgeben
      if (dateStr.includes('.')) {
        return dateStr;
      }
      
      // YYYY-MM-DD zu DD.MM.YYYY konvertieren
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return dateStr; // Bei ungültigen Daten original zurückgeben
      }
      
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      
      return `${day}.${month}.${year}`;
    } catch (e) {
      console.warn('SystemFTSChart: Fehler beim Formatieren des Datums:', dateStr, e);
      return dateStr;
    }
  };

  if (loading) {
    return (
      <Card className="text-black">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">{t("loading")}...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="text-black">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-red-600">{error}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Berechne Durchschnittswerte für Footer
  const avgAvailability = chartData.length > 0 
    ? chartData.reduce((sum, item) => sum + item.verfuegbarkeitFTS, 0) / chartData.length 
    : 0;
  const minAvailability = chartData.length > 0 
    ? Math.min(...chartData.map(item => item.verfuegbarkeitFTS))
    : 0;
  const maxAvailability = chartData.length > 0 
    ? Math.max(...chartData.map(item => item.verfuegbarkeitFTS))
    : 0;

  return (
    <Card className="text-black border-rounded-md border-shadow-md border-[#ff7a05]">
      <CardHeader>
        <CardTitle>FTS VERFÜGBARKEIT</CardTitle>
        <CardDescription>
          Verfügbarkeit des Fahrerlosen Transportsystems aus Datenbank
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis
                dataKey="Datum"
                className="text-xs font-bold"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => {
                  try {
                    if (!value) return '-';
                    
                    // Format: DD.MM.YYYY -> DD.MM
                    if (value.includes('.') && value.split('.').length >= 2) {
                      const [day, month] = value.split('.');
                      return `${day.padStart(2, '0')}.${month.padStart(2, '0')}`;
                    }
                    
                    return value.substring(0, 5);
                  } catch {
                    return String(value).substring(0, 5) || '-';
                  }
                }}
              />
              <YAxis
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                axisLine={{ stroke: "#000000", strokeWidth: 2 }}
                domain={[0, 1]} // Feste Y-Achse von 0% bis 100%
                tickFormatter={(value) => `${(Number(value) * 100).toFixed(0)}%`}
                label={{
                  value: "Verfügbarkeit (%)",
                  angle: -90,
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                  offset: -5
                }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelClassName="font-bold"
                    labelFormatter={(label) => `Datum: ${label}`}
                    formatter={(value, name) => [
                      `${(Number(value) * 100).toFixed(1)}%`,
                      name
                    ]}
                  />
                }
              />
              <Line
                type="monotone"
                dataKey="verfuegbarkeitFTS"
                name={chartConfig.verfuegbarkeitFTS.label}
                stroke={chartConfig.verfuegbarkeitFTS.color}
                strokeWidth={3}
                dot={{ 
                  fill: chartConfig.verfuegbarkeitFTS.color, 
                  strokeWidth: 2, 
                  stroke: "#000000",
                  r: 4 
                }}
                activeDot={{ 
                  r: 6, 
                  stroke: "#000000", 
                  strokeWidth: 2,
                  fill: chartConfig.verfuegbarkeitFTS.color
                }}
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: {(avgAvailability * 100).toFixed(1)}% |
              Min: {(minAvailability * 100).toFixed(1)}% |
              Max: {(maxAvailability * 100).toFixed(1)}%
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              {chartData.length > 0
                ? `Basierend auf ${chartData.length} Datensätzen aus Datenbank`
                : 'Keine FTS-Verfügbarkeitsdaten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});