import { useState } from "react";
import { DateRange } from "react-day-picker";
import { Activity, BarChart3, AlertTriangle } from "lucide-react";
import { SystemFTSChart } from "../components/charts/SystemFTSChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import { PerformanceDashboard, ErrorMonitoring } from "@/modules/stoerungen/components";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";

export default function SystemPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 5, 1), // 1. Juni 2025
    to: new Date(2025, 5, 12), // 12. Juni 2025
  });
  const [activeTab, setActiveTab] = useState("performance");

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift */}
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-black flex items-center gap-2">
          <Activity className="h-8 w-8" />
          SYSTEM
        </h1>
      </div>

      {/* Smooth Tabs mit System-Bereichen */}
      <div className="space-y-6">
        <div className="flex justify-center">
          <SmoothTab
            items={[
              {
                id: "performance",
                title: "System Performance",
                icon: BarChart3,
                color: "bg-blue-500 hover:bg-blue-600",
              },
              {
                id: "errors",
                title: "System Error Monitoring",
                icon: AlertTriangle,
                color: "bg-red-500 hover:bg-red-600",
              },
              {
                id: "verfugbarkeit",
                title: "Anlagen Verfügbarkeit",
                icon: Activity,
                color: "bg-green-500 hover:bg-green-600",
              },
            ]}
            defaultTabId="performance"
            onChange={handleTabChange}
            hideCardContent={true}
          />
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "performance" && (
            <ChartErrorBoundary>
              <PerformanceDashboard />
            </ChartErrorBoundary>
          )}

          {activeTab === "errors" && (
            <ChartErrorBoundary>
              <ErrorMonitoring />
            </ChartErrorBoundary>
          )}

          {activeTab === "verfugbarkeit" && (
            <div className="space-y-4">
              <div className="flex justify-end">
                <DateRangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  label="Datumsauswahl"
                />
              </div>
              <ChartErrorBoundary>
                <SystemFTSChart dateRange={dateRange} />
              </ChartErrorBoundary>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}