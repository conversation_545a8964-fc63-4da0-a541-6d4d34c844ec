import React, { useState, useEffect } from 'react';
import { Area, AreaChart, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface TrendData {
  timestamp: string;
  value: number;
  period: string;
}

interface PerformanceTrendsChartProps {
  timeRange: '5min' | '1hour' | '24hours' | '7days' | '30days';
}

export function PerformanceTrendsChart({ timeRange }: PerformanceTrendsChartProps) {
  const [responseTimeData, setResponseTimeData] = useState<TrendData[]>([]);
  const [successRateData, setSuccessRateData] = useState<TrendData[]>([]);
  const [cacheHitData, setCacheHitData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeMetric, setActiveMetric] = useState<'response_time' | 'success_rate' | 'cache_hit_rate'>('response_time');

  const chartConfig = {
    value: {
      label: "Wert",
      color: "oklch(0.685 0.169 237.323)",
    },
  } satisfies ChartConfig;

  useEffect(() => {
    const fetchTrends = async () => {
      setLoading(true);
      try {
        const period = timeRange === '5min' || timeRange === '1hour' ? 'hour' :
          timeRange === '24hours' ? 'hour' : 'day';
        const limit = timeRange === '5min' ? 12 : timeRange === '1hour' ? 24 :
          timeRange === '24hours' ? 24 : timeRange === '7days' ? 7 : 30;

        // For now, generate mock trend data since the trends endpoint doesn't exist in the basic performance routes
        const mockTrendData = Array.from({ length: limit }, (_, i) => {
          const timestamp = new Date(Date.now() - (limit - i) * (period === 'hour' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000));
          return {
            timestamp: timestamp.toLocaleTimeString('de-DE', {
              hour: '2-digit',
              minute: '2-digit',
              ...(period === 'day' && { day: '2-digit', month: '2-digit' })
            }),
            value: Math.round(Math.random() * 100 + 50), // Mock data
            period: period
          };
        });

        setResponseTimeData(mockTrendData.map(d => ({ ...d, value: Math.round(Math.random() * 2000 + 500) })));
        setSuccessRateData(mockTrendData.map(d => ({ ...d, value: Math.round(Math.random() * 20 + 80) })));
        setCacheHitData(mockTrendData.map(d => ({ ...d, value: Math.round(Math.random() * 30 + 60) })));
      } catch (error) {
        console.error('Error fetching trends:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrends();
  }, [timeRange]);

  const getCurrentData = () => {
    switch (activeMetric) {
      case 'response_time':
        return responseTimeData;
      case 'success_rate':
        return successRateData;
      case 'cache_hit_rate':
        return cacheHitData;
      default:
        return responseTimeData;
    }
  };

  const getMetricInfo = () => {
    switch (activeMetric) {
      case 'response_time':
        return {
          title: 'Antwortzeit Trend',
          description: 'Durchschnittliche Antwortzeit über Zeit',
          unit: 'ms',
          color: 'oklch(0.685 0.169 237.323)',
          goodThreshold: 2000,
          isHigherBetter: false
        };
      case 'success_rate':
        return {
          title: 'Erfolgsrate Trend',
          description: 'Erfolgsrate der Anfragen über Zeit',
          unit: '%',
          color: 'oklch(0.66 0.17 301)',
          goodThreshold: 90,
          isHigherBetter: true
        };
      case 'cache_hit_rate':
        return {
          title: 'Cache Trefferrate Trend',
          description: 'Cache-Effizienz über Zeit',
          unit: '%',
          color: 'oklch(0.77 0.10 55)',
          goodThreshold: 70,
          isHigherBetter: true
        };
      default:
        return {
          title: 'Performance Trend',
          description: 'Performance-Metriken über Zeit',
          unit: '',
          color: 'oklch(0.685 0.169 237.323)',
          goodThreshold: 0,
          isHigherBetter: true
        };
    }
  };

  const calculateTrend = (data: TrendData[]) => {
    if (data.length < 2) return 'stable';

    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));

    const firstAvg = firstHalf.reduce((sum, item) => sum + item.value, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + item.value, 0) / secondHalf.length;

    const change = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (Math.abs(change) < 5) return 'stable';
    return change > 0 ? 'up' : 'down';
  };

  const currentData = getCurrentData();
  const metricInfo = getMetricInfo();
  const trend = calculateTrend(currentData);

  const getTrendIcon = () => {
    const isGoodTrend = metricInfo.isHigherBetter ? trend === 'up' : trend === 'down';

    if (trend === 'up') {
      return <TrendingUp className={`h-4 w-4 ${isGoodTrend ? 'text-green-600' : 'text-red-600'}`} />;
    } else if (trend === 'down') {
      return <TrendingDown className={`h-4 w-4 ${isGoodTrend ? 'text-green-600' : 'text-red-600'}`} />;
    } else {
      return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendBadge = () => {
    const isGoodTrend = metricInfo.isHigherBetter ? trend === 'up' : trend === 'down';

    return (
      <Badge
        variant={isGoodTrend ? "secondary" : trend === 'stable' ? "outline" : "destructive"}
        className={`flex items-center gap-1 ${isGoodTrend ? "bg-green-100 text-green-800" : ""}`}
      >
        {getTrendIcon()}
        {trend === 'up' ? 'Steigend' : trend === 'down' ? 'Fallend' : 'Stabil'}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Lade Trend-Daten...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {metricInfo.title}
              {getTrendBadge()}
            </CardTitle>
            <CardDescription>{metricInfo.description}</CardDescription>
          </div>
        </div>

        {/* Metric Selector */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={() => setActiveMetric('response_time')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${activeMetric === 'response_time'
              ? 'bg-blue-100 text-blue-800 border border-blue-200'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            Antwortzeit
          </button>
          <button
            onClick={() => setActiveMetric('success_rate')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${activeMetric === 'success_rate'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            Erfolgsrate
          </button>
          <button
            onClick={() => setActiveMetric('cache_hit_rate')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${activeMetric === 'cache_hit_rate'
              ? 'bg-purple-100 text-purple-800 border border-purple-200'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            Cache Trefferrate
          </button>
        </div>
      </CardHeader>
      <CardContent className="overflow-hidden">
        <div className="h-[260px]">
          <ChartContainer config={chartConfig}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                accessibilityLayer
                data={currentData}
                margin={{
                  left: 12,
                  right: 12,
                  top: 8,
                  bottom: 72,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="timestamp"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{ fontSize: 10 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tick={{ fontSize: 10 }}
                  width={52}
                  label={{
                    value: metricInfo.unit,
                    angle: -90,
                    position: 'insideLeft',
                    style: { fontSize: '10px' }
                  }}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent
                    formatter={(value) => [`${value}${metricInfo.unit}`, metricInfo.title]}
                  />}
                />
                <defs>
                  <linearGradient id="fillValue" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor={metricInfo.color}
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor={metricInfo.color}
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <Area
                  dataKey="value"
                  type="natural"
                  fill="url(#fillValue)"
                  fillOpacity={0.4}
                  stroke={metricInfo.color}
                  strokeWidth={1.5}
                />
              </AreaChart>
            </ResponsiveContainer>
          </ChartContainer>
        </div>
      </CardContent>
      <CardFooter className="pt-2">
        {currentData.length > 0 && (
          <div className="flex w-full items-start gap-2 text-sm">
            <div className="grid gap-2">
              <div className="flex items-center gap-2 leading-none font-medium">
                {trend === 'up' ? 'Steigender Trend' : trend === 'down' ? 'Fallender Trend' : 'Stabiler Trend'}
                {getTrendIcon()}
              </div>
              <div className="text-muted-foreground flex flex-wrap items-center gap-2 leading-none">
                Aktuell: {currentData[currentData.length - 1]?.value}{metricInfo.unit} |
                Durchschnitt: {Math.round(currentData.reduce((sum, item) => sum + item.value, 0) / currentData.length)}{metricInfo.unit} |
                {metricInfo.isHigherBetter ? 'Max' : 'Min'}: {metricInfo.isHigherBetter
                  ? Math.max(...currentData.map(d => d.value))
                  : Math.min(...currentData.map(d => d.value))
                }{metricInfo.unit}
              </div>
            </div>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}