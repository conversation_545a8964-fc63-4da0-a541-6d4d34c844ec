/**
 * AI Module Main Dashboard Page
 * 
 * Main dashboard integrating all AI services with navigation,
 * service status monitoring, and configuration management.
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from '@tanstack/react-router';
import {
  Activity,
  Settings,
  TrendingUp,
  Package,
  Scissors,
  Warehouse,
  Truck,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Bot,
  BarChart3
} from 'lucide-react';

// Import für das knowledgeBase.png Icon
import knowledgeBaseIcon from '../../../assets/knowledgeBase.png';
import cuttingOptimizerIcon from '../../../assets/cuttingOptimizer.png';
import inventoryIntelligenceIcon from '../../../assets/inventory.png';
import inventoryOptimizerIcon from '../../../assets/inventoryOptimizer.png';
import supplyChainAnalyticsIcon from '../../../assets/supply-chain.png';
import automatedReportingIcon from '../../../assets/reports.png';
import predictiveAnalyticsIcon from '../../../assets/predictive.png';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import SmoothTab from '@/components/Animation/kokonutui/smooth-tab';
import BentoCard from '@/components/ui/bento-card';

import { AIServiceStatus } from '../services/base/AIBaseService';
import { useAIServiceHealth } from '../hooks/useAIServiceHealth';
import { useRAGStats, RAGStats } from '../hooks/useRAGStats';

interface AIModuleCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  route: string;
  color: string;
  status: 'healthy' | 'warning' | 'error' | 'loading';
  metrics?: {
    successRate: number;
    avgResponseTime: number;
    totalRequests: number;
  };
  ragStats?: RAGStats;
}

const AIDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { serviceStatuses, isLoading, error, refreshStatuses } = useAIServiceHealth();
  const { stats: ragStats, isLoading: isLoadingRAG, error: ragError } = useRAGStats();
  const [selectedTab, setSelectedTab] = useState('overview');

  // AI Module Cards Configuration
  const aiModules: AIModuleCard[] = [
    {
      id: 'rag',
      title: 'JASZ Knowledge Base',
      description: 'Verwalte zusätzliches Wissen für JASZ',
      icon: () => <img src={knowledgeBaseIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/rag-management',
      color: '#6366f1',
      status: getServiceStatus('rag', serviceStatuses),
      metrics: getServiceMetrics('rag', serviceStatuses),
      ragStats: ragStats || undefined
    },
    {
      id: 'cutting',
      title: 'JASZ Cutting Optimizer',
      description: 'AI-gestützte Optimierung von Schneidmustern',
      icon: () => <img src={cuttingOptimizerIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/cutting-optimization',
      color: '#f59e0b',
      status: getServiceStatus('cutting', serviceStatuses),
      metrics: getServiceMetrics('cutting', serviceStatuses)
    },
    {
      id: 'inventory',
      title: 'JASZ Inventory Intelligence',
      description: 'Intelligente Bestandsanalyse und Vorhersagen',
      icon: () => <img src={inventoryIntelligenceIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/inventory-intelligence',
      color: '#10b981',
      status: getServiceStatus('inventory', serviceStatuses),
      metrics: getServiceMetrics('inventory', serviceStatuses)
    },
    {
      id: 'warehouse',
      title: 'JASZ Stock Optimizer',
      description: 'Optimierung von Lagerplätzen und Kommissionierung',
      icon: () => <img src={inventoryOptimizerIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/warehouse-optimization',
      color: '#8b5cf6',
      status: getServiceStatus('warehouse', serviceStatuses),
      metrics: getServiceMetrics('warehouse', serviceStatuses)
    },
    {
      id: 'supply-chain',
      title: 'JASZ Supply Chain Analytics',
      description: 'Lieferketten-Analyse und Optimierung',
      icon: () => <img src={supplyChainAnalyticsIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/supply-chain-analytics',
      color: '#ef4444',
      status: getServiceStatus('supply-chain', serviceStatuses),
      metrics: getServiceMetrics('supply-chain', serviceStatuses)
    },
    {
      id: 'reporting',
      title: 'JASZ Automated Reports',
      description: 'AI-generierte Berichte und Insights',
      icon: () => <img src={automatedReportingIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/reporting',
      color: '#06b6d4',
      status: getServiceStatus('reporting', serviceStatuses),
      metrics: getServiceMetrics('reporting', serviceStatuses)
    },
    {
      id: 'analytics',
      title: 'JASZ Predictive Analytics',
      description: 'Vorhersagemodelle und KPI-Monitoring',
      icon: () => <img src={predictiveAnalyticsIcon} alt="Knowledge Base" className="h-10 w-10" />,
      route: '/modules/ai/predictive-analytics',
      color: '#84cc16',
      status: getServiceStatus('analytics', serviceStatuses),
      metrics: getServiceMetrics('analytics', serviceStatuses)
    }
  ];

  // Auto-refresh service statuses
  useEffect(() => {
    const interval = setInterval(() => {
      refreshStatuses();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [refreshStatuses]);

  const handleModuleNavigation = (route: string) => {
    navigate({ to: route });
  };

  const getOverallSystemHealth = () => {
    if (isLoading) return { status: 'loading', percentage: 0 };

    const totalServices = aiModules.length;
    const healthyServices = aiModules.filter(module => module.status === 'healthy').length;
    const warningServices = aiModules.filter(module => module.status === 'warning').length;
    const errorServices = aiModules.filter(module => module.status === 'error').length;

    const percentage = (healthyServices / totalServices) * 100;

    if (errorServices > 0) return { status: 'error', percentage };
    if (warningServices > 0) return { status: 'warning', percentage };
    return { status: 'healthy', percentage };
  };

  const systemHealth = getOverallSystemHealth();

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Bot className="h-8 w-8 text-indigo-600" />
              JASZ AI-Dashboard
            </h1>
            <p className="text-gray-600 mt-1">
              Zentrale Verwaltung aller AI-Services und Funktionen
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={refreshStatuses}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Activity className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Status aktualisieren
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate({ to: '/modules/ai/settings' })}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Einstellungen
            </Button>
          </div>
        </div>

        {/* Main Content Tabs */}
        <div className="space-y-6">
          <SmoothTab
            items={[
              {
                id: 'overview',
                title: 'Übersicht',
                color: 'bg-indigo-500 hover:bg-indigo-600'
              },
              {
                id: 'services',
                title: 'Services',
                color: 'bg-purple-500 hover:bg-purple-600'
              },
              {
                id: 'analytics',
                title: 'Analytics',
                color: 'bg-emerald-500 hover:bg-emerald-600'
              }
            ]}
            defaultTabId="overview"
            onChange={setSelectedTab}
          />

          {/* Tab Content */}
          <div className="space-y-6">
            {selectedTab === 'overview' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {aiModules.map((module) => (
                  <motion.div
                    key={module.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <AIModuleCard
                      module={module}
                      onNavigate={handleModuleNavigation}
                    />
                  </motion.div>
                ))}
              </div>
            )}

            {selectedTab === 'services' && (
              <AIServiceStatusTable
                modules={aiModules}
                onRefresh={refreshStatuses}
                isLoading={isLoading}
              />
            )}

            {selectedTab === 'analytics' && (
              <AIAnalyticsDashboard
                serviceStatuses={serviceStatuses}
                modules={aiModules}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper functions
function getServiceStatus(serviceId: string, statuses: Record<string, AIServiceStatus>): 'healthy' | 'warning' | 'error' | 'loading' {
  const status = statuses[serviceId];
  if (!status) return 'loading';

  if (!status.isHealthy) return 'error';
  if (status.performance && status.performance.successRate < 0.9) return 'warning';
  return 'healthy';
}

function getServiceMetrics(serviceId: string, statuses: Record<string, AIServiceStatus>) {
  const status = statuses[serviceId];
  if (!status?.performance) return undefined;

  return {
    successRate: status.performance.successRate * 100,
    avgResponseTime: status.performance.averageResponseTime,
    totalRequests: status.performance.totalRequests
  };
}

// AI Module Card Component
interface AIModuleCardProps {
  module: AIModuleCard;
  onNavigate: (route: string) => void;
}

const AIModuleCard: React.FC<AIModuleCardProps> = ({ module, onNavigate }) => {
  const IconComponent = module.icon;

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <BentoCard
      title={module.title}
      description={module.description}
      icon={<IconComponent />}
      color={module.color}
      onClick={() => onNavigate(module.route)}
      showArrow={true}
    >
      {/* Status Badge */}
      <div className="flex justify-end items-center mb-2">
        <StatusBadge status={module.status} />
      </div>

      {/* RAG-specific Stats */}
      {module.id === 'rag' && module.ragStats && (
        <div className="grid grid-cols-3 gap-1 text-xs">
          <div className="text-center">
            <div className="font-semibold text-indigo-600 text-sm">
              {module.ragStats.totalDocuments}
            </div>
            <div className="text-gray-500 text-xs">Dokumente</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-blue-600 text-sm">
              {module.ragStats.totalChunks}
            </div>
            <div className="text-gray-500 text-xs">Chunks</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-purple-600 text-sm">
              {formatFileSize(module.ragStats.storageSize)}
            </div>
            <div className="text-gray-500 text-xs">Speicher</div>
          </div>
        </div>
      )}

      {/* Default Metrics for other modules */}
      {module.id !== 'rag' && module.metrics && (
        <div className="grid grid-cols-3 gap-1 text-xs">
          <div className="text-center">
            <div className="font-semibold text-green-600 text-sm">
              {Math.round(module.metrics.successRate)}%
            </div>
            <div className="text-gray-500 text-xs">Erfolg</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-blue-600 text-sm">
              {Math.round(module.metrics.avgResponseTime)}ms
            </div>
            <div className="text-gray-500 text-xs">Zeit</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-purple-600 text-sm">
              {module.metrics.totalRequests}
            </div>
            <div className="text-gray-500 text-xs">Anfragen</div>
          </div>
        </div>
      )}

      {/* Loading state for RAG */}
      {module.id === 'rag' && !module.ragStats && (
        <div className="grid grid-cols-3 gap-1 text-xs">
          <div className="text-center">
            <div className="font-semibold text-gray-400 text-sm">...</div>
            <div className="text-gray-500 text-xs">Dokumente</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-400 text-sm">...</div>
            <div className="text-gray-500 text-xs">Chunks</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-400 text-sm">...</div>
            <div className="text-gray-500 text-xs">Speicher</div>
          </div>
        </div>
      )}
    </BentoCard>
  );
};

// Status Badge Component
interface StatusBadgeProps {
  status: 'healthy' | 'warning' | 'error' | 'loading';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const config = {
    healthy: {
      variant: 'default' as const,
      className: 'bg-green-100 text-green-800',
      icon: CheckCircle,
      text: 'Aktiv'
    },
    warning: {
      variant: 'secondary' as const,
      className: 'bg-yellow-100 text-yellow-800',
      icon: AlertCircle,
      text: 'Warnung'
    },
    error: {
      variant: 'destructive' as const,
      className: '',
      icon: AlertCircle,
      text: 'Fehler'
    },
    loading: {
      variant: 'outline' as const,
      className: '',
      icon: Clock,
      text: 'Laden...'
    }
  };

  const { variant, className, icon: Icon, text } = config[status];

  return (
    <Badge variant={variant} className={className}>
      <Icon className={`h-3 w-3 mr-1 ${status === 'loading' ? 'animate-spin' : ''}`} />
      {text}
    </Badge>
  );
};

// Service Status Table Component
interface AIServiceStatusTableProps {
  modules: AIModuleCard[];
  onRefresh: () => void;
  isLoading: boolean;
}

const AIServiceStatusTable: React.FC<AIServiceStatusTableProps> = ({
  modules,
  onRefresh,
  isLoading
}) => {
  return (
    <Card className={`w-full border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-300`} >
      <CardHeader>
        <div className="flex items-center justify-between text-2xl">
          <div>
            <CardTitle>Service-Status Details</CardTitle>
            <CardDescription className="text-gray-600">
              Detaillierte Übersicht aller KI-Services
            </CardDescription>
          </div>
          <Button
            variant="outline"
            onClick={onRefresh}
            disabled={isLoading}
            size="sm"
          >
            <Activity className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Aktualisieren
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {modules.map((module) => (
            <div
              key={module.id}
              className="flex items-center justify-between p-4 border rounded-lg bg-white"
            >
              <div className="flex items-center gap-3">
                <module.icon
                  className="h-5 w-5"
                  style={{ color: module.color }}
                />
                <div>
                  <div className="font-medium">{module.title}</div>
                  <div className="text-sm text-gray-500">{module.description}</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                {/* RAG-specific metrics */}
                {module.id === 'rag' && module.ragStats && (
                  <div className="flex items-center gap-4 text-sm">
                    <div className="text-center">
                      <div className="font-semibold">
                        {module.ragStats.totalDocuments}
                      </div>
                      <div className="text-gray-500 text-xs">Dokumente</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">
                        {module.ragStats.totalChunks}
                      </div>
                      <div className="text-gray-500 text-xs">Chunks</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">
                        {module.ragStats.totalKnowledgeBases}
                      </div>
                      <div className="text-gray-500 text-xs">Wissensbereiche</div>
                    </div>
                  </div>
                )}

                {/* Default metrics for other modules */}
                {module.id !== 'rag' && module.metrics && (
                  <div className="flex items-center gap-4 text-sm">
                    <div className="text-center">
                      <div className="font-semibold">
                        {Math.round(module.metrics.successRate)}%
                      </div>
                      <div className="text-gray-500 text-xs">Erfolg</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">
                        {Math.round(module.metrics.avgResponseTime)}ms
                      </div>
                      <div className="text-gray-500 text-xs">Zeit</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">
                        {module.metrics.totalRequests}
                      </div>
                      <div className="text-gray-500 text-xs">Anfragen</div>
                    </div>
                  </div>
                )}

                <StatusBadge status={module.status} />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Analytics Dashboard Component
interface AIAnalyticsDashboardProps {
  serviceStatuses: Record<string, AIServiceStatus>;
  modules: AIModuleCard[];
}

const AIAnalyticsDashboard: React.FC<AIAnalyticsDashboardProps> = ({
  serviceStatuses,
  modules
}) => {
  const totalRequests = modules.reduce((sum, module) =>
    sum + (module.metrics?.totalRequests || 0), 0
  );

  const avgSuccessRate = modules.length > 0
    ? modules.reduce((sum, module) =>
      sum + (module.metrics?.successRate || 0), 0
    ) / modules.length
    : 0;

  const avgResponseTime = modules.length > 0
    ? modules.reduce((sum, module) =>
      sum + (module.metrics?.avgResponseTime || 0), 0
    ) / modules.length
    : 0;

  // RAG-specific stats
  const ragModule = modules.find(m => m.id === 'rag');
  const totalDocuments = ragModule?.ragStats?.totalDocuments || 0;
  const totalChunks = ragModule?.ragStats?.totalChunks || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <BentoCard
        title="Gesamt-Anfragen"
        description="Alle Services kombiniert"
        icon={<BarChart3 />}
        color="#3b82f6"
      >
        <div className="text-2xl font-bold text-blue-600">
          {totalRequests.toLocaleString()}
        </div>
      </BentoCard>

      <BentoCard
        title="Durchschn. Erfolgsrate"
        description="Über alle Services"
        icon={<CheckCircle />}
        color="#10b981"
      >
        <div className="text-2xl font-bold text-green-600">
          {Math.round(avgSuccessRate)}%
        </div>
      </BentoCard>

      <BentoCard
        title="Durchschn. Antwortzeit"
        description="Über alle Services"
        icon={<Clock />}
        color="#8b5cf6"
      >
        <div className="text-2xl font-bold text-purple-600">
          {Math.round(avgResponseTime)}ms
        </div>
      </BentoCard>

      <BentoCard
        title="RAG Dokumente"
        description="Wissensdatenbank"
        icon={<FileText />}
        color="#6366f1"
      >
        <div className="text-2xl font-bold text-indigo-600">
          {totalDocuments.toLocaleString()}
        </div>
      </BentoCard>

      <BentoCard
        title="RAG Chunks"
        description="Textabschnitte"
        icon={<Package />}
        color="#f59e0b"
      >
        <div className="text-2xl font-bold text-amber-600">
          {totalChunks.toLocaleString()}
        </div>
      </BentoCard>
    </div>
  );
};

export default AIDashboardPage;