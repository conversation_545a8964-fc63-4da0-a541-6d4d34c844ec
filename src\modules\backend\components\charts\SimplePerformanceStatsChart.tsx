import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface QueryPerformance {
  avg: number;
  count: number;
  successRate: number;
}

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  intentAccuracy: number;
  queryPerformance: {
    stoerungen: QueryPerformance;
    dispatch: QueryPerformance;
    cutting: QueryPerformance;
  };
}

interface SimplePerformanceStatsChartProps {
  stats: PerformanceStats | null;
}

export function SimplePerformanceStatsChart({ stats }: SimplePerformanceStatsChartProps) {
  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Query Performance</CardTitle>
          <CardDescription>Keine Daten verfügbar</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Keine Performance-Daten verfügbar</div>
        </CardContent>
      </Card>
    );
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    const seconds = Math.floor(ms / 1000);
    return `${seconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Query Performance nach Datenquelle</CardTitle>
        <CardDescription>
          Antwortzeiten und Erfolgsraten der verschiedenen Datenquellen
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-3">
        <div className="space-y-2">
          {Object.entries(stats.queryPerformance).map(([source, metrics]) => (
            <div key={source} className="flex items-center justify-between p-2 border rounded text-sm">
              <div className="flex items-center gap-2">
                <div className="font-medium text-sm">
                  {source === 'stoerungen' ? 'Störungen' : 
                   source === 'dispatch' ? 'Versand' : 
                   source === 'cutting' ? 'Ablängerei' : source}
                </div>
                <Badge
                  variant={metrics.successRate >= 0.9 ? "secondary" : metrics.successRate >= 0.7 ? "outline" : "destructive"}
                  className={`text-xs px-1 py-0 ${metrics.successRate >= 0.9 ? "bg-green-100 text-green-800" : ""}`}
                >
                  {formatPercentage(metrics.successRate)}
                </Badge>
              </div>
              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                <div className="text-center">
                  <div className="font-medium text-foreground text-sm">{metrics.count}</div>
                  <div>Queries</div>
                </div>
                <div className="text-center">
                  <div className="font-medium text-foreground text-sm">{formatDuration(metrics.avg)}</div>
                  <div>Zeit</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}