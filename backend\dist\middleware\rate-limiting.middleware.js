"use strict";
/**
 * Rate-Limiting-Middleware
 *
 * Schützt die API vor DoS-Angriffen und übermäßiger Nutzung
 * durch IP-basierte Ratenbegrenzung.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimitConfig = exports.unauthenticatedRateLimit = exports.metricsEndpointRateLimit = exports.healthCheckRateLimit = exports.writeOperationRateLimit = exports.dataEndpointRateLimit = exports.generalRateLimit = void 0;
exports.createAdaptiveRateLimit = createAdaptiveRateLimit;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
/**
 * Erstellt eine benutzerdefinierte Fehlerantwort für Rate-Limiting
 */
function createRateLimitResponse(req, res) {
    console.warn(`[RATE-LIMIT] IP ${req.ip} hat das Rate-Limit für ${req.originalUrl} überschritten`);
    res.status(429).json({
        success: false,
        error: 'Zu viele Anfragen',
        message: 'Sie haben zu viele Anfragen in kurzer Zeit gesendet. Bitte versuchen Sie es später erneut.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(res.getHeader('Retry-After') || 60)
    });
}
/**
 * Basis-Rate-Limiter für allgemeine API-Endpunkte
 * Development: 1000 Anfragen pro 15 Minuten, Production: 100 Anfragen pro 15 Minuten pro IP
 */
exports.generalRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    limit: process.env.NODE_ENV === 'development' ? 1000 : 100, // Development: 1000, Production: 100
    handler: createRateLimitResponse,
    standardHeaders: 'draft-7', // Rate-Limit-Informationen in `RateLimit-*` Headers
    legacyHeaders: false, // Deaktiviere `X-RateLimit-*` Headers
    // Erweiterte Konfiguration
    keyGenerator: (req) => {
        // Verwende X-Forwarded-For falls verfügbar (für Proxy-Umgebungen)
        return req.headers['x-forwarded-for'] || req.ip || 'anonymous';
    },
    skip: (req) => {
        // Überspringe Rate-Limiting für Health-Checks in der Entwicklung
        if (process.env.NODE_ENV === 'development' && req.path === '/health') {
            return true;
        }
        return false;
    }
});
/**
 * Rate-Limiter für Daten-intensive Endpunkte
 * Development: 500 Anfragen pro 15 Minuten, Production: 30 Anfragen pro 15 Minuten pro IP
 */
exports.dataEndpointRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    limit: process.env.NODE_ENV === 'development' ? 500 : 30, // Development: 500, Production: 30
    handler: createRateLimitResponse,
    standardHeaders: 'draft-7',
    legacyHeaders: false,
    keyGenerator: (req) => {
        return req.headers['x-forwarded-for'] || req.ip || 'anonymous';
    }
});
/**
 * Sehr strenger Rate-Limiter für Import-/Schreiboperationen
 * 5 Anfragen pro 15 Minuten pro IP
 */
exports.writeOperationRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    limit: 5, // Maximum 5 Anfragen pro Fenster
    handler: createRateLimitResponse,
    standardHeaders: 'draft-7',
    legacyHeaders: false,
    keyGenerator: (req) => {
        return req.headers['x-forwarded-for'] || req.ip || 'anonymous';
    }
});
/**
 * Lockerer Rate-Limiter für Health-Checks
 * 1000 Anfragen pro 15 Minuten pro IP
 */
exports.healthCheckRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    limit: 1000, // Maximum 1000 Anfragen pro Fenster
    handler: createRateLimitResponse,
    standardHeaders: 'draft-7',
    legacyHeaders: false,
    keyGenerator: (req) => {
        return req.headers['x-forwarded-for'] || req.ip || 'anonymous';
    }
});
/**
 * Rate-Limiter für Performance-Metriken-Endpunkte
 * Development: 200 Anfragen pro 15 Minuten, Production: 50 Anfragen pro 15 Minuten pro IP
 */
exports.metricsEndpointRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    limit: process.env.NODE_ENV === 'development' ? 200 : 50, // Development: 200, Production: 50
    handler: createRateLimitResponse,
    standardHeaders: 'draft-7',
    legacyHeaders: false,
    keyGenerator: (req) => {
        return req.headers['x-forwarded-for'] || req.ip || 'anonymous';
    }
});
/**
 * Sehr strenger Rate-Limiter für nicht-authentifizierte Anfragen
 * 10 Anfragen pro 15 Minuten pro IP
 */
exports.unauthenticatedRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    limit: 10, // Maximum 10 Anfragen pro Fenster
    handler: createRateLimitResponse,
    standardHeaders: 'draft-7',
    legacyHeaders: false,
    keyGenerator: (req) => {
        return req.headers['x-forwarded-for'] || req.ip || 'anonymous';
    }
});
/**
 * Adaptive Rate-Limiting basierend auf Authentifizierungsstatus
 * Authentifizierte Benutzer erhalten höhere Limits
 */
function createAdaptiveRateLimit() {
    return (req, res, next) => {
        var _a;
        // Überprüfe Authentifizierungsstatus
        const isAuthenticated = !!((_a = req.user) === null || _a === void 0 ? void 0 : _a.authenticated);
        if (isAuthenticated) {
            // Verwende allgemeines Rate-Limit für authentifizierte Benutzer
            return (0, exports.generalRateLimit)(req, res, next);
        }
        else {
            // Verwende strengeres Limit für nicht-authentifizierte Benutzer
            return (0, exports.unauthenticatedRateLimit)(req, res, next);
        }
    };
}
/**
 * Rate-Limiting-Konfiguration für verschiedene Endpunkt-Typen
 */
exports.rateLimitConfig = {
    general: exports.generalRateLimit,
    dataEndpoint: exports.dataEndpointRateLimit,
    writeOperation: exports.writeOperationRateLimit,
    healthCheck: exports.healthCheckRateLimit,
    metricsEndpoint: exports.metricsEndpointRateLimit,
    unauthenticated: exports.unauthenticatedRateLimit,
    adaptive: createAdaptiveRateLimit()
};
