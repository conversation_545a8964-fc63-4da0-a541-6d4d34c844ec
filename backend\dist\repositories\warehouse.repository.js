"use strict";
/**
 * Warehouse Repository Implementation
 *
 * Implementiert die WarehouseRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Lagerdaten.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseRepositoryImpl = void 0;
const cache_service_1 = require("../services/cache.service");
/**
 * Cache-TTL für Warehouse-Daten (verschiedene Datentypen)
 */
const WAREHOUSE_CACHE_TTL = {
    CAPACITY: 2 * 60 * 1000, // 2 Minuten - Auslastungsdaten
    ARIL_ATRL: 1 * 60 * 1000, // 1 Minute - Hochfrequente Lagerdaten
    OVERVIEW: 5 * 60 * 1000, // 5 Minuten - Übersichtsdaten
    TRENDS: 10 * 60 * 1000, // 10 Minuten - Trend-Analysen
    ALERTS: 3 * 60 * 1000 // 3 Minuten - Kritische Warnungen
};
class WarehouseRepositoryImpl {
    constructor(prisma) {
        this.cache = (0, cache_service_1.getBackendCache)();
        this.stats = {
            totalQueries: 0,
            cacheHits: 0,
            cacheMisses: 0,
            hitRate: 0,
            avgQueryTime: 0,
            lastAccessed: new Date()
        };
        this.prisma = prisma;
    }
    /**
     * Lagerauslastung 200 Daten abrufen
     */
    async getLagerauslastung200Data(dateRange) {
        const startTime = Date.now();
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.warehouse.lagerauslastung200(dateRange);
        this.stats.totalQueries++;
        try {
            const data = await this.cache.cachedQuery(cacheKey, async () => {
                const whereClause = dateRange ? {
                    aufnahmeDatum: {
                        gte: dateRange.startDate,
                        lte: dateRange.endDate
                    }
                } : {};
                return await this.prisma.auslastung200.findMany({
                    where: whereClause,
                    orderBy: { aufnahmeDatum: 'desc' },
                    take: 1000
                });
            }, WAREHOUSE_CACHE_TTL.CAPACITY);
            const formattedData = data.map(item => ({
                date: item.aufnahmeDatum || '',
                auslastungA: parseFloat(item.auslastungA || '0'),
                auslastungB: parseFloat(item.auslastungB || '0'),
                auslastungC: parseFloat(item.auslastungC || '0'),
                gesamt: parseFloat(item.auslastungA || '0') + parseFloat(item.auslastungB || '0') + parseFloat(item.auslastungC || '0')
            }));
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return formattedData;
        }
        catch (error) {
            console.error('Error fetching Lagerauslastung200Data:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * Lagerauslastung 240 Daten abrufen
     */
    async getLagerauslastung240Data(dateRange) {
        const startTime = Date.now();
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.warehouse.lagerauslastung240(dateRange);
        this.stats.totalQueries++;
        try {
            const data = await this.cache.cachedQuery(cacheKey, async () => {
                const whereClause = dateRange ? {
                    aufnahmeDatum: {
                        gte: dateRange.startDate,
                        lte: dateRange.endDate
                    }
                } : {};
                return await this.prisma.auslastung240.findMany({
                    where: whereClause,
                    orderBy: { aufnahmeDatum: 'desc' },
                    take: 1000
                });
            }, WAREHOUSE_CACHE_TTL.CAPACITY);
            const formattedData = data.map(item => ({
                date: item.aufnahmeDatum || '',
                auslastungA: parseFloat(item.auslastungA || '0'),
                auslastungB: parseFloat(item.auslastungB || '0'),
                auslastungC: parseFloat(item.auslastungC || '0'),
                gesamt: parseFloat(item.auslastungA || '0') + parseFloat(item.auslastungB || '0') + parseFloat(item.auslastungC || '0')
            }));
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return formattedData;
        }
        catch (error) {
            console.error('Error fetching Lagerauslastung240Data:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * ARiL Lagerdaten abrufen
     */
    async getArilData(dateRange) {
        const startTime = Date.now();
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.warehouse.aril(dateRange);
        this.stats.totalQueries++;
        try {
            const data = await this.cache.cachedQuery(cacheKey, async () => {
                const whereClause = dateRange ? {
                    Datum: {
                        gte: dateRange.startDate,
                        lte: dateRange.endDate
                    }
                } : {};
                return await this.prisma.aRiL.findMany({
                    where: whereClause,
                    orderBy: { Datum: 'desc' },
                    take: 1000
                });
            }, WAREHOUSE_CACHE_TTL.ARIL_ATRL);
            const formattedData = data.map(item => ({
                id: item.id,
                Datum: item.Datum || '',
                waTaPositionen: item.waTaPositionen || 0,
                cuttingLagerKunde: item.cuttingLagerKunde || 0,
                cuttingLagerRest: item.cuttingLagerRest || 0,
                Umlagerungen: item.Umlagerungen || 0,
                lagerCutting: item.lagerCutting || 0
            }));
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return formattedData;
        }
        catch (error) {
            console.error('Error fetching ArilData:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * ATrL Lagerdaten abrufen
     */
    async getAtrlData(dateRange) {
        const startTime = Date.now();
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.warehouse.atrl(dateRange);
        this.stats.totalQueries++;
        try {
            const data = await this.cache.cachedQuery(cacheKey, async () => {
                const whereClause = dateRange ? {
                    Datum: {
                        gte: dateRange.startDate,
                        lte: dateRange.endDate
                    }
                } : {};
                return await this.prisma.aTrL.findMany({
                    where: whereClause,
                    orderBy: { Datum: 'desc' },
                    take: 1000
                });
            }, WAREHOUSE_CACHE_TTL.ARIL_ATRL);
            const formattedData = data.map(item => ({
                id: item.id,
                Datum: item.Datum || '',
                weAtrl: item.weAtrl,
                EinlagerungAblKunde: item.EinlagerungAblKunde,
                EinlagerungAblRest: item.EinlagerungAblRest,
                umlagerungen: item.umlagerungen,
                waTaPositionen: item.waTaPositionen,
                AuslagerungAbl: item.AuslagerungAbl
            }));
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return formattedData;
        }
        catch (error) {
            console.error('Error fetching AtrlData:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * Bestandsübersicht für alle Lager
     */
    async getWarehouseOverview() {
        const startTime = Date.now();
        const cacheKey = 'warehouse:overview';
        this.stats.totalQueries++;
        try {
            const overview = await this.cache.cachedQuery(cacheKey, async () => {
                const [latest200, latest240, latestAril, latestAtrl] = await Promise.all([
                    this.prisma.auslastung200.findFirst({ orderBy: { aufnahmeDatum: 'desc' } }),
                    this.prisma.auslastung240.findFirst({ orderBy: { aufnahmeDatum: 'desc' } }),
                    this.prisma.aRiL.findFirst({ orderBy: { Datum: 'desc' } }),
                    this.prisma.aTrL.findFirst({ orderBy: { Datum: 'desc' } })
                ]);
                const total200 = (parseFloat((latest200 === null || latest200 === void 0 ? void 0 : latest200.auslastungA) || '0') +
                    parseFloat((latest200 === null || latest200 === void 0 ? void 0 : latest200.auslastungB) || '0') +
                    parseFloat((latest200 === null || latest200 === void 0 ? void 0 : latest200.auslastungC) || '0'));
                const total240 = (parseFloat((latest240 === null || latest240 === void 0 ? void 0 : latest240.auslastungA) || '0') +
                    parseFloat((latest240 === null || latest240 === void 0 ? void 0 : latest240.auslastungB) || '0') +
                    parseFloat((latest240 === null || latest240 === void 0 ? void 0 : latest240.auslastungC) || '0'));
                return {
                    warehouse200: {
                        currentCapacity: total200,
                        maxCapacity: 1000,
                        utilizationRate: total200 / 1000,
                        trend: 'stable'
                    },
                    warehouse240: {
                        currentCapacity: total240,
                        maxCapacity: 1200,
                        utilizationRate: total240 / 1200,
                        trend: 'stable'
                    },
                    aril: {
                        totalPositions: 2000,
                        filledPositions: (latestAril === null || latestAril === void 0 ? void 0 : latestAril.waTaPositionen) || 0,
                        fillRate: ((latestAril === null || latestAril === void 0 ? void 0 : latestAril.waTaPositionen) || 0) / 2000,
                        lastUpdate: (latestAril === null || latestAril === void 0 ? void 0 : latestAril.Datum) || new Date().toISOString()
                    },
                    atrl: {
                        totalPositions: 1500,
                        filledPositions: (latestAtrl === null || latestAtrl === void 0 ? void 0 : latestAtrl.waTaPositionen) || 0,
                        fillRate: ((latestAtrl === null || latestAtrl === void 0 ? void 0 : latestAtrl.waTaPositionen) || 0) / 1500,
                        lastUpdate: (latestAtrl === null || latestAtrl === void 0 ? void 0 : latestAtrl.Datum) || new Date().toISOString()
                    }
                };
            }, WAREHOUSE_CACHE_TTL.OVERVIEW);
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return overview;
        }
        catch (error) {
            console.error('Error fetching warehouse overview:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * Auslastungstrends über Zeit
     */
    async getCapacityTrends(warehouseType, dateRange) {
        const startTime = Date.now();
        const cacheKey = `warehouse:trends:${warehouseType}:${dateRange.startDate}:${dateRange.endDate}`;
        this.stats.totalQueries++;
        try {
            const trends = await this.cache.cachedQuery(cacheKey, async () => {
                const maxCapacity = warehouseType === '200' ? 1000 : 1200;
                let data;
                if (warehouseType === '200') {
                    data = await this.prisma.auslastung200.findMany({
                        where: {
                            aufnahmeDatum: {
                                gte: dateRange.startDate,
                                lte: dateRange.endDate
                            }
                        },
                        orderBy: { aufnahmeDatum: 'asc' }
                    });
                }
                else {
                    data = await this.prisma.auslastung240.findMany({
                        where: {
                            aufnahmeDatum: {
                                gte: dateRange.startDate,
                                lte: dateRange.endDate
                            }
                        },
                        orderBy: { aufnahmeDatum: 'asc' }
                    });
                }
                return data.map((item, index) => {
                    const totalCapacity = parseFloat(item.auslastungA || '0') +
                        parseFloat(item.auslastungB || '0') +
                        parseFloat(item.auslastungC || '0');
                    const utilizationRate = totalCapacity / maxCapacity;
                    const previousCapacity = index > 0 ?
                        (parseFloat(data[index - 1].auslastungA || '0') +
                            parseFloat(data[index - 1].auslastungB || '0') +
                            parseFloat(data[index - 1].auslastungC || '0')) :
                        totalCapacity;
                    return {
                        date: item.aufnahmeDatum || '',
                        capacity: totalCapacity,
                        utilizationRate,
                        changeFromPrevious: totalCapacity - previousCapacity
                    };
                });
            }, WAREHOUSE_CACHE_TTL.TRENDS);
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return trends;
        }
        catch (error) {
            console.error(`Error fetching capacity trends for warehouse ${warehouseType}:`, error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * Kritische Auslastungswerte ermitteln
     */
    async getCriticalCapacityAlerts(threshold = 0.9) {
        const startTime = Date.now();
        const cacheKey = `warehouse:alerts:${threshold}`;
        this.stats.totalQueries++;
        try {
            const alerts = await this.cache.cachedQuery(cacheKey, async () => {
                const alerts = [];
                const [latest200, latest240] = await Promise.all([
                    this.prisma.auslastung200.findFirst({ orderBy: { aufnahmeDatum: 'desc' } }),
                    this.prisma.auslastung240.findFirst({ orderBy: { aufnahmeDatum: 'desc' } })
                ]);
                if (latest200) {
                    const total200 = parseFloat(latest200.auslastungA || '0') +
                        parseFloat(latest200.auslastungB || '0') +
                        parseFloat(latest200.auslastungC || '0');
                    const utilization200 = total200 / 1000;
                    if (utilization200 >= threshold) {
                        alerts.push({
                            warehouseType: 'Warehouse 200',
                            currentUtilization: utilization200,
                            threshold,
                            severity: utilization200 >= 0.95 ? 'critical' : 'warning',
                            recommendation: utilization200 >= 0.95 ?
                                'Sofortige Auslagerung erforderlich' :
                                'Auslagerung in den nächsten 24h planen',
                            date: latest200.aufnahmeDatum || ''
                        });
                    }
                }
                if (latest240) {
                    const total240 = parseFloat(latest240.auslastungA || '0') +
                        parseFloat(latest240.auslastungB || '0') +
                        parseFloat(latest240.auslastungC || '0');
                    const utilization240 = total240 / 1200;
                    if (utilization240 >= threshold) {
                        alerts.push({
                            warehouseType: 'Warehouse 240',
                            currentUtilization: utilization240,
                            threshold,
                            severity: utilization240 >= 0.95 ? 'critical' : 'warning',
                            recommendation: utilization240 >= 0.95 ?
                                'Sofortige Auslagerung erforderlich' :
                                'Auslagerung in den nächsten 24h planen',
                            date: latest240.aufnahmeDatum || ''
                        });
                    }
                }
                return alerts;
            }, WAREHOUSE_CACHE_TTL.ALERTS);
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return alerts;
        }
        catch (error) {
            console.error('Error fetching critical capacity alerts:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    /**
     * Repository-Statistiken abrufen
     */
    async getStats() {
        this.stats.hitRate = this.stats.totalQueries > 0 ?
            (this.stats.cacheHits / this.stats.totalQueries) * 100 : 0;
        this.stats.lastAccessed = new Date();
        return { ...this.stats };
    }
    /**
     * Repository-Cache invalidieren
     */
    async invalidateCache(key) {
        if (key) {
            this.cache.invalidateByDataTypes([key]);
        }
        else {
            this.cache.invalidateByDataTypes(['warehouse']);
        }
    }
    /**
     * Statistiken aktualisieren
     */
    updateStats(startTime) {
        const queryTime = Date.now() - startTime;
        this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
        this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
        this.stats.lastAccessed = new Date();
    }
}
exports.WarehouseRepositoryImpl = WarehouseRepositoryImpl;
