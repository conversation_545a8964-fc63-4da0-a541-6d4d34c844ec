"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
const getDateFilter = (startDate, endDate) => {
    const filter = {};
    if (startDate && endDate) {
        filter.datum = {
            gte: startDate,
            lte: endDate
        };
    }
    else if (startDate) {
        filter.datum = {
            gte: startDate
        };
    }
    else if (endDate) {
        filter.datum = {
            lte: endDate
        };
    }
    return filter;
};
router.get('/service-level', async (req, res) => {
    console.log('✅ Service-Level Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const filter = getDateFilter(startDate, endDate);
        const serviceData = await prisma.dispatchData.findMany({
            where: filter,
            take: 30,
            orderBy: { datum: 'desc' },
            select: {
                datum: true,
                servicegrad: true
            }
        });
        const transformedData = serviceData.map((item) => ({
            datum: item.datum,
            csr: item.servicegrad ? Math.round(item.servicegrad * 100) : 0
        }));
        console.log(`✅ Erfolgreich ${transformedData.length} Service-Level Einträge abgerufen`);
        res.json({
            success: true,
            data: transformedData
        });
    }
    catch (error) {
        console.error('❌ Fehler beim Service-Level Abruf:', error);
        res.status(500).json({
            success: false,
            error: 'Service-Level Fehler'
        });
    }
});
router.get('/atrl', async (req, res) => {
    console.log('✅ ATrL Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const filter = getDateFilter(startDate, endDate);
        const atrlData = await prisma.dispatchData.findMany({
            where: filter,
            take: 30,
            orderBy: { datum: 'desc' },
            select: {
                datum: true,
                atrl: true
            }
        });
        const transformedData = atrlData.map((item) => ({
            Datum: item.datum,
            weAtrl: item.atrl || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim ATrL Abruf:', error);
        res.status(500).json({ success: false, error: 'ATrL Fehler' });
    }
});
router.get('/aril', async (req, res) => {
    console.log('✅ ARiL Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const filter = getDateFilter(startDate, endDate);
        const arilData = await prisma.dispatchData.findMany({
            where: filter,
            take: 30,
            orderBy: { datum: 'desc' },
            select: {
                datum: true,
                aril: true,
                fuellgrad_aril: true
            }
        });
        const transformedData = arilData.map((item) => ({
            Datum: item.datum,
            aril: item.aril || 0,
            fuellgrad: item.fuellgrad_aril || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim ARiL Abruf:', error);
        res.status(500).json({ success: false, error: 'ARiL Fehler' });
    }
});
router.get('/we', async (req, res) => {
    console.log('✅ WE (Wareneingang) Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const filter = getDateFilter(startDate, endDate);
        const weData = await prisma.wE.findMany({
            where: filter,
            take: 30,
            orderBy: { Datum: 'desc' },
            select: {
                Datum: true,
                weAtrl: true,
                weManl: true
            }
        });
        const transformedData = weData.map((item) => ({
            id: Math.random(), // Temporäre ID
            datum: item.Datum,
            weAtrl: item.weAtrl || 0,
            weManl: item.weManl || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim WE Abruf:', error);
        res.status(500).json({ success: false, error: 'WE Fehler' });
    }
});
router.get('/lagerauslastung200', async (req, res) => {
    console.log('✅ Lagerauslastung 200 Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        let whereClause = {};
        if (startDate || endDate) {
            whereClause.aufnahmeDatum = {};
            if (startDate) {
                whereClause.aufnahmeDatum.gte = startDate;
            }
            if (endDate) {
                whereClause.aufnahmeDatum.lte = endDate;
            }
        }
        const lagerData = await prisma.bestand200.findMany({
            where: whereClause,
            take: 100,
            orderBy: { aufnahmeDatum: 'desc' },
            select: {
                aufnahmeDatum: true,
                auslastung: true,
                auslastungA: true,
                auslastungB: true,
                auslastungC: true,
                maxPlaetze: true
            }
        });
        const transformedData = lagerData.map((item) => ({
            aufnahmeDatum: item.aufnahmeDatum,
            gesamt: parseFloat(item.auslastung) || 0,
            maxPlaetze: parseInt(item.maxPlaetze) || 0,
            auslastungA: parseFloat(item.auslastungA) || 0,
            auslastungB: parseFloat(item.auslastungB) || 0,
            auslastungC: parseFloat(item.auslastungC) || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Lagerauslastung 200 Abruf:', error);
        res.status(500).json({ success: false, error: 'Lagerauslastung 200 Fehler' });
    }
});
router.get('/daily-performance', async (req, res) => {
    console.log('✅ Daily-Performance Route aufgerufen');
    try {
        const performanceData = await prisma.dispatchData.findMany({
            take: 30,
            orderBy: { datum: 'desc' },
            select: {
                datum: true,
                produzierte_tonnagen: true
            }
        });
        const transformedData = performanceData.map((item) => ({
            datum: item.datum,
            value: item.produzierte_tonnagen || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Daily-Performance Abruf:', error);
        res.status(500).json({ success: false, error: 'Daily-Performance Fehler' });
    }
});
router.get('/picking', async (req, res) => {
    console.log('✅ Picking Route aufgerufen');
    try {
        const pickingData = [
            { name: 'Kategorie A', value: 45 },
            { name: 'Kategorie B', value: 30 },
            { name: 'Kategorie C', value: 25 }
        ];
        res.json({ success: true, data: pickingData });
    }
    catch (error) {
        console.error('❌ Fehler beim Picking Abruf:', error);
        res.status(500).json({ success: false, error: 'Picking Fehler' });
    }
});
router.get('/returns', async (req, res) => {
    console.log('✅ Returns Route aufgerufen');
    try {
        const returnsData = [
            { name: 'Grund A', value: 15 },
            { name: 'Grund B', value: 10 },
            { name: 'Grund C', value: 5 }
        ];
        res.json({ success: true, data: returnsData });
    }
    catch (error) {
        console.error('❌ Fehler beim Returns Abruf:', error);
        res.status(500).json({ success: false, error: 'Returns Fehler' });
    }
});
router.get('/delivery-positions', async (req, res) => {
    console.log('✅ Delivery-Positions Route aufgerufen');
    try {
        const deliveryData = await prisma.dispatchData.findMany({
            take: 10,
            orderBy: { datum: 'desc' },
            select: {
                datum: true,
                ausgeliefert_lup: true,
                rueckstaendig: true
            }
        });
        const transformedData = deliveryData.map((item) => ({
            date: item.datum,
            ausgeliefert_lup: item.ausgeliefert_lup || 0,
            rueckstaendig: item.rueckstaendig || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Delivery-Positions Abruf:', error);
        res.status(500).json({ success: false, error: 'Delivery-Positions Fehler' });
    }
});
router.get('/tagesleistung', async (req, res) => {
    console.log('✅ Tagesleistung Route aufgerufen');
    try {
        const tagesleistungData = await prisma.dispatchData.findMany({
            take: 10,
            orderBy: { datum: 'desc' },
            select: {
                datum: true,
                produzierte_tonnagen: true,
                direktverladung_kiaa: true,
                umschlag: true,
                kg_pro_colli: true,
                elefanten: true
            }
        });
        const transformedData = tagesleistungData.map((item) => ({
            date: item.datum,
            produzierte_tonnagen: item.produzierte_tonnagen || 0,
            direktverladung_kiaa: item.direktverladung_kiaa || 0,
            umschlag: item.umschlag || 0,
            kg_pro_colli: item.kg_pro_colli || 0,
            elefanten: item.elefanten || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Tagesleistung Abruf:', error);
        res.status(500).json({ success: false, error: 'Tagesleistung Fehler' });
    }
});
exports.default = router;
