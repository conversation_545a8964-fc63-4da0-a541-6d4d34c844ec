import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useMaterialData } from '../hooks/useMaterialData';
import SmoothTab from '@/components/Animation/kokonutui/smooth-tab';
import { ChartErrorBoundary } from '@/components/ErrorBoundary';
import { CuttingPlanVisualization } from '../components/cutting/CuttingPlanVisualization';
import { CuttingAlternativesChart } from '../components/cutting/CuttingAlternativesChart';
import { WasteAnalysisChart } from '../components/cutting/WasteAnalysisChart';
import { TrommelrechnungComponent } from '../components/cutting/TrommelrechnungComponent';
import { CuttingOptimizerService } from '../services/cutting/CuttingOptimizerService';
import TrommelrechnungService from '../services/cutting/TrommelrechnungService';
import {
    CuttingRequest,
    CuttingPlan,
    CuttingAlternative,
    WasteAnalysis,
    CuttingOrder,
    DrumInventory,
    CuttingConstraints,
    Delivery,
    DeliveryItem
} from '../services/types';
import { Scissors, Download, RefreshCw, AlertTriangle, CheckCircle, Plus, Minus } from 'lucide-react';
import { toast } from 'sonner';
import MaterialSelector from '@/components/MaterialSelector';
import TrommelSelector from '@/components/TrommelSelector';

/**
 * Cutting Optimization Page
 * 
 * Provides AI-powered cutting optimization with visualization and export functionality.
 * Integrates with existing cutting department routes and authentication.
 */
export default function CuttingOptimizationPage() {
    const { t } = useTranslation();
    const { cableTypes, isLoading: isMaterialDataLoading } = useMaterialData();

    // State management
    const [isOptimizing, setIsOptimizing] = useState(false);
    const [currentPlan, setCurrentPlan] = useState<CuttingPlan | null>(null);
    const [alternatives, setAlternatives] = useState<CuttingAlternative[]>([]);
    const [wasteAnalysis, setWasteAnalysis] = useState<WasteAnalysis | null>(null);
    const [selectedAlgorithm, setSelectedAlgorithm] = useState<'first-fit' | 'best-fit' | 'genetic'>('best-fit');
    const [activeTab, setActiveTab] = useState('trommelrechnung');
    const [currentOrders, setCurrentOrders] = useState<CuttingOrder[]>([]);

    // Form state for cutting request - Neue Lieferungslogik
    const [delivery, setDelivery] = useState<Delivery>({
        id: 'delivery-1',
        items: [
            {
                id: 'item-1',
                cableType: '0010000', // MATNR aus Materialdaten
                requiredLength: 1.5, // 150cm = 1.5m
                quantity: 5,
                priority: 'medium'
            }
        ]
    });

    const [drums, setDrums] = useState<DrumInventory[]>([
        {
            id: 'drum-1',
            cableType: '0010000', // MATNR aus Materialdaten
            totalLength: 10.0, // 1000cm = 10.0m
            availableLength: 8.0, // 800cm = 8.0m
            quality: 'A'
        }
    ]);

    const [constraints, setConstraints] = useState<CuttingConstraints>({
        maxWasteLength: 1.0, // 1.0m maximaler Verschnitt
        maxWastePercentage: 15,
        allowMixedTypes: false
    });

    // State für die Auswahl des Verschnitt-Typs
    const [wasteType, setWasteType] = useState<'length' | 'percentage'>('length');

    // State für MaterialSelector Funktionalität
    const [kabelMaterialSuchOpen, setKabelMaterialSuchOpen] = useState(false);
    const [kabelMaterialSuchWert, setKabelMaterialSuchWert] = useState('');
    const [verfügbareKabelmaterialien, setVerfügbareKabelmaterialien] = useState<any[]>([]);

    // State für TrommelSelector Funktionalität
    const [selectedTrommel, setSelectedTrommel] = useState('');
    const [trommelSuchOpen, setTrommelSuchOpen] = useState(false);
    const [trommelSuchWert, setTrommelSuchWert] = useState('');
    const [verfügbareTrommeln, setVerfügbareTrommeln] = useState<any[]>([]);

    // Funktion um den ersten ausgewählten Kabeltyp aus den Lieferungen zu ermitteln
    const getSelectedCableTypeFromOrders = useCallback(() => {
        const itemWithCableType = delivery.items.find(item => item.cableType && item.cableType.trim() !== '');
        return itemWithCableType?.cableType || '';
    }, [delivery]);

    // Initialize cutting optimizer service
    const [optimizerService] = useState(() => new CuttingOptimizerService({
        defaultAlgorithm: selectedAlgorithm,
        enableAdvancedOptimization: true,
        maxOptimizationTime: 30000
    }));

    // Initialize service on component mount
    React.useEffect(() => {
        const initService = async () => {
            try {
                await optimizerService.initialize();
            } catch (error) {
                console.error('Failed to initialize cutting optimizer service:', error);
                toast.error('Fehler beim Initialisieren des Optimierungsdienstes');
            }
        };

        initService();
    }, [optimizerService]);

    // Event Handler für MaterialSelector
    const handleKabelMaterialChange = useCallback((value: string) => {
        setKabelMaterialSuchWert(value);
    }, []);

    const handleKabelMaterialSelect = useCallback((material: any) => {
        // Aktualisiere das erste DeliveryItem mit dem ausgewählten Material
        if (delivery.items.length > 0) {
            const updatedDelivery = {
                ...delivery,
                items: delivery.items.map((item, index) => 
                    index === 0 ? { ...item, cableType: material.MATNR } : item
                )
            };
            setDelivery(updatedDelivery);
            
            // Synchronisiere den Kabeltyp auch für alle Trommeln
            const updatedDrums = drums.map(drum => ({
                ...drum,
                cableType: material.MATNR
            }));
            setDrums(updatedDrums);
        }
    }, [delivery, drums]);

    // Funktion für die Materialauswahl pro DeliveryItem
    const handleItemMaterialSelect = useCallback((itemId: string, material: any) => {
        const updatedDelivery = {
            ...delivery,
            items: delivery.items.map(item => 
                item.id === itemId ? { ...item, cableType: material.MATNR } : item
            )
        };
        setDelivery(updatedDelivery);
     }, [delivery]);

    // Handler für Übernehmen-Button: Überträgt Kabeltyp von Lieferung zu verfügbarer Trommel
    const handleÜbernehmenKabeltyp = useCallback((itemId: string) => {
        const item = delivery.items.find(i => i.id === itemId);
        if (!item || !item.cableType) {
            toast.error('Kein Kabeltyp in der Lieferung ausgewählt');
            return;
        }

        // Aktualisiere die erste verfügbare Trommel mit dem Kabeltyp der Lieferung
        if (verfügbareTrommeln.length > 0) {
            const updatedTrommeln = [...verfügbareTrommeln];
            updatedTrommeln[0] = {
                ...updatedTrommeln[0],
                Kabeltyp: item.cableType
            };
            setVerfügbareTrommeln(updatedTrommeln);
            toast.success(`Kabeltyp ${item.cableType} zur Trommel übertragen`);
        } else {
            toast.error('Keine verfügbare Trommel gefunden');
        }
    }, [delivery.items, verfügbareTrommeln]);

    // Lade die ersten 100 Kabelmaterialien beim Komponenten-Mount
    React.useEffect(() => {
        const loadInitialMaterials = async () => {
            try {
                const { materials } = await TrommelrechnungService.searchKabelmaterialien('', 100);
                setVerfügbareKabelmaterialien(materials);
            } catch (error) {
                console.error('Fehler beim Laden der Kabelmaterialien:', error);
                setVerfügbareKabelmaterialien([]);
            }
        };

        loadInitialMaterials();
    }, []);

    // Lade alle verfügbaren Trommeln ohne Filterung
    React.useEffect(() => {
        const loadAllTrommeln = async () => {
            try {
                const allTrommeln = await TrommelrechnungService.getVerfügbareTrommeln();
                setVerfügbareTrommeln(allTrommeln);
            } catch (error) {
                console.error('Fehler beim Laden der Trommeln:', error);
                setVerfügbareTrommeln([]);
            }
        };

        loadAllTrommeln();
    }, []); // Keine Abhängigkeiten - lädt nur einmal beim Mount

    // Handler für TrommelSelector
    const handleTrommelChange = useCallback((value: string) => {
        setSelectedTrommel(value);
    }, []);

    const handleTrommelSelect = useCallback((trommel: any) => {
        // Hier können weitere Aktionen bei Trommelauswahl implementiert werden
        console.log('Trommel ausgewählt:', trommel);
    }, []);

    // Synchronisiere Kabeltyp von Lieferungen zu Trommeln
    React.useEffect(() => {
        const selectedCableType = getSelectedCableTypeFromOrders();
        if (selectedCableType && selectedCableType.trim() !== '') {
            // Aktualisiere alle Trommeln mit dem ausgewählten Kabeltyp
            const updatedDrums = drums.map(drum => ({
                ...drum,
                cableType: selectedCableType
            }));
            setDrums(updatedDrums);
        }
    }, [delivery, getSelectedCableTypeFromOrders, drums]);

    /**
     * Handle cutting optimization
     */
    const handleOptimize = useCallback(async () => {
        if (delivery.items.length === 0 || drums.length === 0) {
            toast.error('Bitte fügen Sie mindestens einen Kabeltyp und eine Trommel hinzu');
            return;
        }

        setIsOptimizing(true);

        try {
            // Konvertiere Delivery Items zu CuttingOrders für die Optimierung
            const orders: CuttingOrder[] = delivery.items.map(item => ({
                id: item.id,
                cableType: item.cableType,
                requiredLength: item.requiredLength,
                quantity: item.quantity,
                priority: item.priority,
                deadline: delivery.deadline
            }));
            
            // Speichere die aktuellen Orders für die Visualisierung
            setCurrentOrders(orders);

            const request: CuttingRequest = {
                orders,
                availableDrums: drums,
                constraints,
                priorities: orders.map((order, index) => ({
                    orderId: order.id,
                    priority: index + 1,
                    weight: delivery.items.find(item => item.id === order.id)?.priority === 'high' ? 3 : 
                           delivery.items.find(item => item.id === order.id)?.priority === 'medium' ? 2 : 1
                }))
            };

            // Update algorithm if changed
            await optimizerService.initialize({ defaultAlgorithm: selectedAlgorithm });

            // Run optimization
            const plan = await optimizerService.optimizeCuttingPlan(request as unknown as import("@/modules/ai/types/cutting").CuttingRequest);
            setCurrentPlan(plan);

            // Calculate waste analysis
            const waste = await optimizerService.calculateWaste(plan);
            setWasteAnalysis(waste);

            // Generate alternatives
            const alts = await optimizerService.simulateAlternatives(request as unknown as import("@/modules/ai/types/cutting").CuttingRequest);
            setAlternatives(alts);

            toast.success(`Optimierung abgeschlossen! Effizienz: ${(plan.efficiency * 100).toFixed(1)}%`);

        } catch (error) {
            console.error('Optimization failed:', error);
            toast.error('Optimierung fehlgeschlagen: ' + (error instanceof Error ? error.message : 'Unbekannter Fehler'));
        } finally {
            setIsOptimizing(false);
        }
    }, [delivery, drums, constraints, selectedAlgorithm, optimizerService]);

    /**
     * Add new delivery item (Kabeltyp zur Lieferung hinzufügen)
     * Fügt automatisch auch eine neue Trommel mit dem gleichen Kabeltyp hinzu
     */
    const addDeliveryItem = useCallback(() => {
        const selectedCableType = cableTypes.length > 0 ? cableTypes[0].value : '0010000';
        
        // Neues Lieferungs-Item hinzufügen
        const newItem: DeliveryItem = {
            id: `item-${delivery.items.length + 1}`,
            cableType: selectedCableType,
            requiredLength: 1.0, // 100cm = 1.0m
            quantity: 1,
            priority: 'medium'
        };
        
        // Neue Trommel mit dem gleichen Kabeltyp hinzufügen
        const newDrum: DrumInventory = {
            id: `drum-${drums.length + 1}`,
            cableType: selectedCableType,
            totalLength: 10.0, // 1000cm = 10.0m
            availableLength: 10.0, // 1000cm = 10.0m
            quality: 'A'
        };
        
        // Beide Updates gleichzeitig durchführen
        setDelivery({
            ...delivery,
            items: [...delivery.items, newItem]
        });
        setDrums([...drums, newDrum]);
        
        toast.success('Kabeltyp zur Lieferung und verfügbaren Trommeln hinzugefügt');
    }, [delivery, drums, cableTypes]);

    /**
     * Remove delivery item (Kabeltyp aus Lieferung entfernen)
     */
    const removeDeliveryItem = useCallback((itemId: string) => {
        // Verhindere das Entfernen des letzten Items
        if (delivery.items.length <= 1) {
            toast.error('Mindestens ein Kabeltyp muss in der Lieferung verbleiben');
            return;
        }
        setDelivery({
            ...delivery,
            items: delivery.items.filter(item => item.id !== itemId)
        });
    }, [delivery]);

    /**
     * Update delivery item (Kabeltyp in Lieferung aktualisieren)
     */
    const updateDeliveryItem = useCallback((itemId: string, updates: Partial<DeliveryItem>) => {
        setDelivery({
            ...delivery,
            items: delivery.items.map(item =>
                item.id === itemId ? { ...item, ...updates } : item
            )
        });
    }, [delivery]);

    /**
     * Add new drum
     */
    const addDrum = useCallback(() => {
        const newDrum: DrumInventory = {
            id: `drum-${drums.length + 1}`,
            cableType: cableTypes.length > 0 ? cableTypes[0].value : '0010000',
            totalLength: 10.0, // 1000cm = 10.0m
             availableLength: 10.0, // 1000cm = 10.0m
            quality: 'A'
        };
        setDrums([...drums, newDrum]);
    }, [drums, cableTypes]);

    /**
     * Remove drum
     */
    const removeDrum = useCallback((drumId: string) => {
        setDrums(drums.filter(drum => drum.id !== drumId));
    }, [drums]);

    /**
     * Update drum
     */
    const updateDrum = useCallback((drumId: string, updates: Partial<DrumInventory>) => {
        setDrums(drums.map(drum =>
            drum.id === drumId ? { ...drum, ...updates } : drum
        ));
    }, [drums]);

    /**
     * Export cutting plan
     */
    const exportPlan = useCallback(async (format: 'pdf' | 'excel') => {
        if (!currentPlan) {
            toast.error('Kein Schnittplan zum Exportieren verfügbar');
            return;
        }

        try {
            // This would integrate with existing export functionality
            const exportData = {
                plan: currentPlan,
                wasteAnalysis,
                alternatives: alternatives.slice(0, 3), // Top 3 alternatives
                timestamp: new Date().toISOString(),
                algorithm: selectedAlgorithm
            };

            // For now, download as JSON (would be replaced with actual PDF/Excel export)
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cutting-plan-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            toast.success(`Schnittplan als ${format.toUpperCase()} exportiert`);
        } catch (error) {
            console.error('Export failed:', error);
            toast.error('Export fehlgeschlagen');
        }
    }, [currentPlan, wasteAnalysis, alternatives, selectedAlgorithm]);

    return (
        <div className="w-full bg-bg min-h-screen p-8">
            {/* Header */}
            <div className="mb-6 flex justify-between items-center">
                <div className="flex items-center">
                    <Scissors className="h-8 w-8 mr-2 text-[#ff7a05]" />
                    <h1 className="text-3xl font-bold text-black">SCHNEID-OPTIMIERUNG</h1>
                </div>
                <div className="flex gap-2">
                    <Button
                        onClick={handleOptimize}
                        disabled={isOptimizing}
                        variant="sfm"
                        className="flex items-center gap-2"
                    >
                        {isOptimizing ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                            <CheckCircle className="h-4 w-4" />
                        )}
                        {isOptimizing ? 'Optimiere...' : 'Optimieren'}
                    </Button>
                    {currentPlan && (
                        <>
                            <Button
                                onClick={() => exportPlan('pdf')}
                                variant="outline"
                                className="flex items-center gap-2"
                            >
                                <Download className="h-4 w-4" />
                                PDF Export
                            </Button>
                            <Button
                                onClick={() => exportPlan('excel')}
                                variant="outline"
                                className="flex items-center gap-2"
                            >
                                <Download className="h-4 w-4" />
                                Excel Export
                            </Button>
                        </>
                    )}
                </div>
            </div>

            <div className="w-full space-y-6">
                <SmoothTab
                    items={[
                        {
                            id: 'trommelrechnung',
                            title: 'Simulation Trommelrechnung',
                            color: 'bg-amber-500 hover:bg-amber-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-amber-500/15 stroke-amber-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="grid grid-cols-3 gap-4">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Maximale Kabellänge auf Trommel
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Berechnung der optimalen Trommelkonfiguration
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'configuration',
                            title: 'Konfiguration',
                            color: 'bg-orange-500 hover:bg-orange-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-orange-500/15 stroke-orange-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Schnitt-Konfiguration
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Algorithmus und Parameter einstellen
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'visualization',
                            title: 'Visualisierung',
                            color: 'bg-blue-500 hover:bg-blue-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-blue-500/15 stroke-blue-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Schnittplan-Visualisierung
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Optimierte Schnittmuster anzeigen
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'alternatives',
                            title: 'Alternativen',
                            color: 'bg-purple-500 hover:bg-purple-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-purple-500/15 stroke-purple-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Alternative Lösungen
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Verschiedene Optimierungsansätze vergleichen
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'analysis',
                            title: 'Analyse',
                            color: 'bg-emerald-500 hover:bg-emerald-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-emerald-500/15 stroke-emerald-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Verschnitt-Analyse
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Effizienz und Materialverschnitt analysieren
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        }
                    ]}
                    defaultTabId="trommelrechnung"
                    onChange={setActiveTab}
                    className="mb-6"
                />

                {/* Tab Content */}
                {activeTab === 'configuration' && (
                    <div className="space-y-6">
                        {/* Einziger großer Container mit drei Spalten und vertikalen Trennlinien */}
                        <Card className="border-slate-800 bg-slate-50 py-6 px-0 transition-all duration-300 hover:border-slate-200">
                            <CardHeader>
                                <div className="flex items-center gap-2">
                                    <div className="flex flex-col">
                                        <CardTitle className="text-2xl">
                                            Konfiguration
                                        </CardTitle>
                                        <CardDescription className="text-gray-600">
                                            Algorithmus, Lieferung und verfügbare Trommeln konfigurieren
                                        </CardDescription>
                                    </div>
                                </div>
                            </CardHeader>
                            
                            <CardContent>
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:divide-x lg:divide-[#ff7a05]/40">
                                    {/* Erste Spalte - Algorithmus-Einstellungen */}
                                    <div className="space-y-4 lg:pr-8">
                                        <div className="flex items-center gap-2">
                                            <h3 className="text-lg font-semibold">Algorithmus-Einstellungen</h3>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Wählen Sie den gewünschten Optimierungsalgorithmus und konfigurieren Sie die Parameter.
                                        </p>
                                <div>
                                    <Label htmlFor="algorithm">Algorithmus</Label>
                                    <Select
                                        value={selectedAlgorithm}
                                        onValueChange={(value: 'first-fit' | 'best-fit' | 'genetic') =>
                                            setSelectedAlgorithm(value)
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="first-fit">First-Fit (Schnell)</SelectItem>
                                            <SelectItem value="best-fit">Best-Fit (Ausgewogen)</SelectItem>
                                            <SelectItem value="genetic">Genetisch (Optimal)</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Verschnitt-Typ und Max Verschnitt nebeneinander */}
                                <div className="grid grid-cols-2 gap-4">
                                    {/* Auswahl des Verschnitt-Typs */}
                                    <div>
                                        <Label htmlFor="wasteType">Verschnitt-Typ</Label>
                                        <Select value={wasteType} onValueChange={(value: 'length' | 'percentage') => setWasteType(value)}>
                                            <SelectTrigger className="bg-white">
                                                <SelectValue placeholder="Verschnitt-Typ wählen" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="length">In (m)</SelectItem>
                                                <SelectItem value="percentage">In (%)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Eingabefeld basierend auf der Auswahl */}
                                    {wasteType === 'length' ? (
                                        <div>
                                            <Label htmlFor="maxWasteLength">Max Verschnitt in (m)</Label>
                                            <Input
                                                id="maxWasteLength"
                                                type="number"
                                                step="0.1"
                                                className='bg-white'
                                                value={constraints.maxWasteLength || 1.0}
                                                onChange={(e) => setConstraints({
                                                    ...constraints,
                                                    maxWasteLength: parseFloat(e.target.value) || 1.0
                                                })}
                                            />
                                        </div>
                                    ) : (
                                        <div>
                                            <Label htmlFor="maxWaste">Max Verschnitt (%)</Label>
                                            <Input
                                                id="maxWaste"
                                                type="number"
                                                className='bg-white'
                                                value={constraints.maxWastePercentage || 15}
                                            onChange={(e) => setConstraints({
                                                ...constraints,
                                                maxWastePercentage: parseInt(e.target.value) || 15
                                            })}
                                        />
                                    </div>
                                    )}
                                </div>
                                
                                {/* Dynamische Algorithmus-Beschreibung */}
                                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                    <p className="text-sm text-blue-800">
                                        {selectedAlgorithm === 'first-fit' && 
                                            'First-Fit Algorithmus: Schnelle Lösung, die Schnitte in der ersten passenden Trommel platziert. Ideal für große Datenmengen mit akzeptabler Verschnittrate.'
                                        }
                                        {selectedAlgorithm === 'best-fit' && 
                                            'Best-Fit Algorithmus: Ausgewogene Lösung, die den besten verfügbaren Platz für jeden Schnitt sucht. Bietet gute Balance zwischen Geschwindigkeit und Optimierung.'
                                        }
                                        {selectedAlgorithm === 'genetic' && 
                                            'Genetischer Algorithmus: Hochoptimierte Lösung durch evolutionäre Optimierung. Liefert beste Ergebnisse bei minimaler Verschnittrate, benötigt mehr Rechenzeit.'
                                        }
                                    </p>
                                </div>
                                    </div>
                                    
                                    {/* Zweite Spalte - Lieferung */}
                                    <div className="space-y-4 lg:px-8">
                                        <div className="flex items-center gap-2">
                                            <h3 className="text-lg font-semibold">Lieferung</h3>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Konfigurieren Sie die Kabeltypen für die Lieferung
                                        </p>
                                        <div className="space-y-4 max-h-80 overflow-y-auto">
                                            {delivery.items.map((item, index) => (
                                                <div key={item.id} className="space-y-3">
                                                    <div className="flex justify-between items-center">
                                                        <span className="font-medium">Artikel {index + 1}</span>
                                                        <div className="flex gap-2">
                                                            <Button
                                                                onClick={addDeliveryItem}
                                                                size="sm"
                                                                variant="ghost"
                                                                className="h-8 w-8 p-0 hover:bg-green-100"
                                                            >
                                                                <Plus className="h-4 w-4 text-green-600" />
                                                            </Button>
                                                            {delivery.items.length > 1 && (
                                                                <Button
                                                                    onClick={() => removeDeliveryItem(item.id)}
                                                                    size="sm"
                                                                    variant="ghost"
                                                                    className="h-8 w-8 p-0 hover:bg-red-100"
                                                                >
                                                                    <Minus className="h-4 w-4 text-red-600" />
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Drei-Spalten-Layout für Kabeltyp, Länge und Anzahl */}
                                                    <div className="grid grid-cols-3 gap-3">
                                                        <div>
                                                            <Label>Kabeltyp</Label>
                                                            <MaterialSelector
                                                                selectedMaterial={item.cableType || ''}
                                                                materialSuchOpen={kabelMaterialSuchOpen}
                                                                materialSuchWert={kabelMaterialSuchWert}
                                                                verfügbareMaterialien={verfügbareKabelmaterialien}
                                                                onMaterialChange={handleKabelMaterialChange}
                                                                onMaterialSelect={(material) => handleItemMaterialSelect(item.id, material)}
                                                                onMaterialSuchOpenChange={setKabelMaterialSuchOpen}
                                                                onMaterialSuchWertChange={setKabelMaterialSuchWert}
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>Länge (m)</Label>
                                                            <Input
                                                                type="number"
                                                                step="0.1"
                                                                className='bg-white'
                                                                value={item.requiredLength}
                                                                onChange={(e) => updateDeliveryItem(item.id, {
                                                                    requiredLength: parseFloat(e.target.value) || 0
                                                                })}
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>Anzahl der Schnitte</Label>
                                                            <Input
                                                                type="number"
                                                                value={item.quantity}
                                                                className='bg-white'
                                                                onChange={(e) => updateDeliveryItem(item.id, {
                                                                    quantity: parseInt(e.target.value) || 1
                                                                })}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    
                                    {/* Dritte Spalte - Verfügbare Trommeln */}
                                    <div className="space-y-4 lg:pl-8">
                                        <div className="flex items-center gap-2">
                                            <h3 className="text-lg font-semibold">Verfügbare Trommeln</h3>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Konfigurieren Sie die verfügbaren Kabeltrommeln aus denen geschnitten werden soll
                                        </p>
                                        <div className="space-y-4 max-h-80 overflow-y-auto">
                                            {drums.map((drum, index) => (
                                                <div key={drum.id} className="space-y-3">
                                                    <div className="flex justify-between items-center">
                                                        <span className="font-medium">
                                                            Trommel für Artikel ({(() => {
                                                                const selectedCableType = getSelectedCableTypeFromOrders();
                                                                return selectedCableType || 'Kein Kabeltyp ausgewählt';
                                                            })()})
                                                        </span>
                                                        <div className="flex gap-2">
                                                            <Button
                                                                onClick={addDrum}
                                                                size="sm"
                                                                variant="ghost"
                                                                className="h-8 w-8 p-0 hover:bg-green-100"
                                                            >
                                                                <Plus className="h-4 w-4 text-green-600" />
                                                            </Button>
                                                            {drums.length > 1 && (
                                                                <Button
                                                                    onClick={() => removeDrum(drum.id)}
                                                                    size="sm"
                                                                    variant="ghost"
                                                                    className="h-8 w-8 p-0 hover:bg-red-100"
                                                                >
                                                                    <Minus className="h-4 w-4 text-red-600" />
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Zwei-Spalten-Layout für Trommeltyp und Verfügbare Länge */}
                                                    <div className="grid grid-cols-2 gap-3">
                                                        <div>
                                                            <Label>Trommeltyp auswählen</Label>
                                                            <TrommelSelector
                                                                verfügbareTrommeln={verfügbareTrommeln}
                                                                selectedTrommel={selectedTrommel}
                                                                onTrommelChange={handleTrommelChange}
                                                                trommelSuchOpen={trommelSuchOpen}
                                                                onTrommelSuchOpenChange={setTrommelSuchOpen}
                                                                trommelSuchWert={trommelSuchWert}
                                                                onTrommelSuchWertChange={setTrommelSuchWert}
                                                                onTrommelSelect={handleTrommelSelect}
                                                                disabled={!getSelectedCableTypeFromOrders()}
                                                                selectedCableType={getSelectedCableTypeFromOrders()}
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>Verfügbare Länge (m)</Label>
                                                            <Input
                                                                type="number"
                                                                step="0.1"
                                                                className='bg-white'
                                                                value={drum.availableLength || drum.totalLength}
                                                                onChange={(e) => updateDrum(drum.id, {
                                                                    availableLength: parseFloat(e.target.value) || 0
                                                                })}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {activeTab === 'visualization' && (
                    <div className="space-y-6">
                    {currentPlan ? (
                        <ChartErrorBoundary>
                            <CuttingPlanVisualization
                                plan={currentPlan}
                                drums={drums}
                                orders={currentOrders}
                            />
                        </ChartErrorBoundary>
                    ) : (
                        <Card className="border-[#ff7a05]">
                            <CardContent className="flex flex-col items-center justify-center h-64">
                                <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
                                <p className="text-gray-600">
                                    Führen Sie eine Optimierung durch, um die Visualisierung zu sehen
                                </p>
                            </CardContent>
                        </Card>
                    )}
                    </div>
                )}

                {activeTab === 'alternatives' && (
                    <div className="space-y-6">
                    {alternatives.length > 0 ? (
                        <ChartErrorBoundary>
                            <CuttingAlternativesChart
                                alternatives={alternatives}
                                onSelectAlternative={(alternative) => {
                                    setCurrentPlan(alternative.plan);
                                    toast.success(`Alternative "${alternative.id}" ausgewählt`);
                                }}
                            />
                        </ChartErrorBoundary>
                    ) : (
                        <Card className="border-[#ff7a05]">
                            <CardContent className="flex flex-col items-center justify-center h-64">
                                <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
                                <p className="text-gray-600">
                                    Führen Sie eine Optimierung durch, um Alternativen zu sehen
                                </p>
                            </CardContent>
                        </Card>
                    )}
                    </div>
                )}

                {activeTab === 'analysis' && (
                    <div className="space-y-6">
                    {wasteAnalysis ? (
                        <ChartErrorBoundary>
                            <WasteAnalysisChart
                                wasteAnalysis={wasteAnalysis}
                                plan={currentPlan}
                            />
                        </ChartErrorBoundary>
                    ) : (
                        <Card className="border-[#ff7a05]">
                            <CardContent className="flex flex-col items-center justify-center h-64">
                                <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
                                <p className="text-gray-600">
                                    Führen Sie eine Optimierung durch, um die Analyse zu sehen
                                </p>
                            </CardContent>
                        </Card>
                    )}
                    </div>
                )}

                {activeTab === 'trommelrechnung' && (
                    <div className="space-y-6">
                        <TrommelrechnungComponent />
                    </div>
                )}
            </div>
        </div>
    );
}