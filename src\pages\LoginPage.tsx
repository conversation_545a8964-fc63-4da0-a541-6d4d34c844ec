import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useAuthContext } from '@/contexts/AuthContext';
import { LabelForm } from '@/components/auth';
import BlurText from '@/components/Animation/Text/BlurText';

// Bilder als ES-Module importieren (Vite/Electron-sicher)
import loginBg from '@/assets/LeitstandLogin.png';
import cardBg from '@/assets/drumLogin.png';

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login, isLoading, isAuthenticated } = useAuthContext();

  // Check for success message from registration
  const searchParams = new URLSearchParams(window.location.search);
  const registrationSuccess = searchParams.get('registered') === 'true';

  // Check if user is already authenticated on component mount
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      navigate({ to: '/' });
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Basic validation
    if (!username || !password) {
      setError('Bitte geben Sie Benutzername und Passwort ein.');
      return;
    }

    try {
      console.log('🔄 Attempting login for user:', username);
      const result = await login(username, password);
      console.log('�  Login result:', result);

      if (result.success) {
        console.log('✅ Login successful, showing toast and navigating');

        toast({
          title: 'Erfolgreich angemeldet',
          description: `Willkommen zurück, ${username}!`,
        });

        // Redirect to module selection page (home for now)
        navigate({ to: '/' });
      } else {
        console.log('❌ Login failed with message:', result.message);
        setError(result.message || 'Anmeldung fehlgeschlagen.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Ein unerwarteter Fehler ist aufgetreten.');
    }
  };

  // Handle navigation to registration page
  const handleNavigateToRegister = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate({ to: '/register' });
  };

  // Handle animation complete
  const handleAnimationComplete = () => {
    console.log('Animation completed!');
  };

  return (
    <div className="min-h-screen flex bg-bg relative">
      {/* Left Half - LoginImage Background */}
      <div className="w-1/2 relative overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <img
            src={loginBg}
            alt="Login Background"
            className="w-full h-full object-cover"
            onError={() => {
              console.error('Image failed to load:', loginBg);
            }}
          />
          {/* Optional overlay for better text readability */}
          <div className="absolute inset-0 bg-black/20"></div>
        </div>

        {/* Text overlay */}
        <div className="absolute inset-0 flex items-start justify-start z-10 pt-45 pl-30">
          <div className="flex flex-col items-start gap-4">
            <BlurText
              text="WILLKOMMEN BEI DER"
              delay={500}
              animateBy="words"
              direction="top"
              onAnimationComplete={handleAnimationComplete}
              className="text-3xl md:text-5xl font-bold text-white text-left tracking-wider drop-shadow-lg font-mono"
            />
          </div>
        </div>
      </div>

      {/* Right Half - All current content */}
      <div className="w-1/2 relative">
        {/* Image Card - fills entire right half */}
        <div className="absolute inset-0 z-0">
          <img
            src={cardBg}
            alt="Background Card Image"
            className="w-full h-full object-cover rounded"
            onError={(e) => {
              console.error('Image failed to load:', cardBg);
            }}
          />
        </div>
      </div>

      {/* Center transition overlay - positioned exactly in the middle */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 h-full w-24 z-5 pointer-events-none">
        <div className="w-full h-full bg-gradient-to-r from-transparent via-bg/60 to-transparent"></div>
      </div>

      {/* Login Form - im Vordergrund */}
      <div className="absolute top-85 right-46 z-10 w-full max-w-95 p-4 transform rotate-[7.5deg] transition-transform duration-300">
        <LabelForm
          title="ANMELDUNG"
          description="Melde dich mit deinen Zugangsdaten an"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          success={registrationSuccess ? 'Registrierung erfolgreich! Sie können sich jetzt anmelden.' : null}
          styleConfig={{ backgroundColor: 'transparent' }}
        >
          <div className="label-stack">
            <div>
              <Label
                htmlFor="username"
                className="label-form-label mt-2"
              >
                Benutzername
              </Label>
              <Input
                id="username"
                type="text"
                placeholder="Ihr Benutzername"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                variant="ghost"
                className="-mt-2"
                required
                autoComplete="username"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2 mt-4">
                <Label
                  htmlFor="password"
                  className="label-form-label"
                >
                  Passwort
                </Label>
              </div>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                variant="ghost"
                className="-mt-5"
                required
                autoComplete="current-password"
              />
            </div>
            <a
              href="#"
              className="text-sm label-text-muted hover:underline"
              onClick={(e) => e.preventDefault()}
            >
              Passwort vergessen?
            </a>
            <Button
              type="submit"
              variant="link"
              className="text-center font-bold text-3xl mt-4"
              disabled={isLoading}
            >
              {isLoading ? 'Anmeldung läuft...' : 'LOGIN'}
            </Button>
          </div>
        </LabelForm>

        {/* Registration Link */}
        <div className="text-center relative z-20">
          <p className="label-text-secondary text-sm">
            Noch kein Konto?{' '}
            <button
              type="button"
              className="label-text-primary hover:underline font-semibold cursor-pointer bg-transparent border-none p-0 inline"
              onClick={handleNavigateToRegister}
            >
              Registrieren
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;