import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { ArilData<PERSON>hart } from "@/modules/dashboard/components/charts/ArilDataChart";
import { Lagerauslastung240Chart } from "@/modules/dashboard/components/charts/Lagerauslastung240Chart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Shell } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";

/**
 * ARiL-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den ARiL-Bereich an:
 * - ARiL-Daten als kombiniertes Diagramm mit gestapelten Balken
 * - Cutting Lager (Kunde/Rest), WaTa Positionen, Umlagerungen
 * - Lager Cutting-Daten
 */
export default function ArilPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 5, 1), // 15. April 2025
    to: new Date(2025, 5, 30), // 7. Mai 2025 (alle verfügbaren Daten)
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center">
          <Shell className="h-8 w-8 mr-2" />
          <h1 className="text-3xl font-bold text-black">AUTOMATISCHES RINGLAGER</h1>
        </div>
        <DateRangePicker 
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-6">
          {/* ATrL-Daten Chart */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <ArilDataChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Lagerauslastung Chart */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <Lagerauslastung240Chart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
} 