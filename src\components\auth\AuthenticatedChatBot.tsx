import React, { useState, useEffect } from 'react';
import { useLocation } from '@tanstack/react-router';
import { useAuthContext } from '@/contexts/AuthContext';
import { ChatBot } from '@/modules/ai/components/chat';
import { apiService } from '@/services/api.service';

/**
 * AuthenticatedChatBot Component
 * 
 * Renders the ChatBot only when:
 * - User is authenticated
 * - User is not on login/registration pages
 * - Backend is available
 */
export const AuthenticatedChatBot: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuthContext();
  const location = useLocation();
  const [isBackendReady, setIsBackendReady] = useState(false);

  useEffect(() => {
    // Only check backend connection if user is authenticated
    if (isAuthenticated && !isLoading) {
      checkBackendConnection();
    }
  }, [isAuthenticated, isLoading]);

  const checkBackendConnection = async () => {
    try {
      // Use the API service to check backend health
      await apiService.get('/health');
      setIsBackendReady(true);
    } catch (error) {
      console.warn('Backend nicht erreichbar, Chat-Funktionalität deaktiviert', error);
      setIsBackendReady(false);
    }
  };

  // Don't render if still loading authentication status
  if (isLoading) {
    return null;
  }

  // Don't render if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Don't render on login/registration pages
  const currentPath = location.pathname;
  const isOnPublicPage = currentPath === '/login' || currentPath === '/register';
  if (isOnPublicPage) {
    return null;
  }

  // Don't render if backend is not ready
  if (!isBackendReady) {
    return null;
  }

  return <ChatBot />;
};