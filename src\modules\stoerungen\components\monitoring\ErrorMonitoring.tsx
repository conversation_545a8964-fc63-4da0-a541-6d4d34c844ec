import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  AlertTriangle, 
  XCircle, 
  AlertCircle, 
  Info, 
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Clock
} from "lucide-react";
import apiService from "@/services/api.service";

interface ErrorStats {
  total: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  last24h: number;
  trend: "up" | "down" | "stable";
  trendPercentage: number;
}

interface ErrorEntry {
  id: string;
  timestamp: string;
  severity: "critical" | "high" | "medium" | "low";
  message: string;
  type: string;
  count: number;
}

export const ErrorMonitoring: React.FC = () => {
  const [stats, setStats] = useState<ErrorStats | null>(null);
  const [recentErrors, setRecentErrors] = useState<ErrorEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchErrorData = async () => {
    try {
      setLoading(true);
      const [statsResponse, historyResponse] = await Promise.all([
        apiService.get<any>("/errors/stats"),
        apiService.get<any>("/errors/history?limit=10")
      ]);

      // Extract data from API responses
      const statsData = statsResponse.data || statsResponse;
      const historyData = historyResponse.data || historyResponse;

      // Transform backend data to match component interface
      const transformedStats: ErrorStats = {
        total: statsData.totalErrors || 0,
        critical: statsData.errorsBySeverity?.critical || 0,
        high: statsData.errorsBySeverity?.high || 0,
        medium: statsData.errorsBySeverity?.medium || 0,
        low: statsData.errorsBySeverity?.low || 0,
        last24h: statsData.recentErrors?.length || 0,
        trend: "stable", // Default since backend doesn't provide trend
        trendPercentage: 0
      };

      setStats(transformedStats);
      setRecentErrors(historyData.errors || []);
    } catch (err) {
      setError("Fehler beim Laden der Error-Daten");
      console.error("Error monitoring fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchErrorData();
    // Longer interval when backend may be offline
    const interval = setInterval(fetchErrorData, 120000); // Update every 2 minutes
    return () => clearInterval(interval);
  }, []);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case "critical":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "medium":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case "low":
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    const variants = {
      critical: "bg-red-100 text-red-800 border-red-200",
      high: "bg-orange-100 text-orange-800 border-orange-200",
      medium: "bg-yellow-100 text-yellow-800 border-yellow-200",
      low: "bg-blue-100 text-blue-800 border-blue-200"
    };
    
    return (
      <Badge className={variants[severity as keyof typeof variants] || "bg-gray-100 text-gray-800"}>
        {severity.toUpperCase()}
      </Badge>
    );
  };

  const getTrendIcon = (trend: string) => {
    if (trend === "up") return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (trend === "down") return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <div className="h-4 w-4" />; // Stable - no icon
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString("de-DE", {
      hour: "2-digit",
      minute: "2-digit",
      day: "2-digit",
      month: "2-digit"
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card className="animate-pulse">
          <CardHeader className="h-20 bg-gray-200 rounded-t-lg"></CardHeader>
          <CardContent className="h-32 bg-gray-100"></CardContent>
        </Card>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card className="border-red-200">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>{error || "Error-Monitoring-Daten nicht verfügbar"}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Statistics Overview */}
      <Card className="border-l-4 border-l-red-500">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Error Monitoring
              </CardTitle>
              <CardDescription>System-Fehler und Trends (letzte 24h)</CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchErrorData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{stats.critical}</div>
              <div className="text-sm text-gray-600">Critical</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">{stats.high}</div>
              <div className="text-sm text-gray-600">High</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{stats.medium}</div>
              <div className="text-sm text-gray-600">Medium</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{stats.low}</div>
              <div className="text-sm text-gray-600">Low</div>
            </div>
          </div>
          
          {/* 24h Trend */}
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Last 24h:</span>
                <span className="font-bold">{stats.last24h} errors</span>
              </div>
              <div className="flex items-center gap-1">
                {getTrendIcon(stats.trend)}
                <span className={`text-sm font-medium ${
                  stats.trend === "up" ? "text-red-500" : 
                  stats.trend === "down" ? "text-green-500" : "text-gray-500"
                }`}>
                  {stats.trend === "stable" ? "Stable" : `${(stats.trendPercentage || 0).toFixed(1)}%`}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Errors */}
      <Card>
        <CardHeader>
          <CardTitle>Kürliche Errors</CardTitle>
          <CardDescription>Letzte Systemfehler und Warnungen</CardDescription>
        </CardHeader>
        <CardContent>
          {recentErrors.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No recent errors found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentErrors.map((errorEntry) => (
                <div 
                  key={errorEntry.id} 
                  className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-shrink-0 mt-0.5">
                    {getSeverityIcon(errorEntry.severity)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {errorEntry.message}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Type: {errorEntry.type}
                        </p>
                      </div>
                      <div className="flex-shrink-0 text-right">
                        {getSeverityBadge(errorEntry.severity)}
                        <p className="text-xs text-gray-500 mt-1">
                          {formatTimestamp(errorEntry.timestamp)}
                        </p>
                      </div>
                    </div>
                    {errorEntry.count > 1 && (
                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          {errorEntry.count}x occurred
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};