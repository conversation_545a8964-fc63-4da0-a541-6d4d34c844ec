import React, { useEffect, useState } from 'react';
import { Activity, CheckCircle, AlertTriangle, Clock, Database } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { apiService } from '@/services/api.service';

interface WorkflowStatusProps {
  className?: string;
}

interface WorkflowHealth {
  totalWorkflows: number;
  activeWorkflows: number;
  runningWorkflows: number;
  failedWorkflows: number;
  overallHealth: 'healthy' | 'warning' | 'critical';
}

export function WorkflowStatus({ className }: WorkflowStatusProps) {
  const [health, setHealth] = useState<WorkflowHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHealth = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const result = await apiService.get('/workflows/health');
        setHealth(result as WorkflowHealth);
      } catch (err) {
        console.error('Failed to fetch workflow health:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchHealth();
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthIcon = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-400" />;
    }
  };

  const getHealthBadge = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Gesund</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warnung</Badge>;
      case 'critical':
        return <Badge variant="destructive">Kritisch</Badge>;
      default:
        return <Badge variant="outline">Unbekannt</Badge>;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Clock className="h-6 w-6 animate-spin text-blue-600" />
          <span className="ml-2">Lade Workflow-Status...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <AlertTriangle className="h-6 w-6 text-red-600" />
          <span className="ml-2 text-red-600">Fehler: {error}</span>
        </CardContent>
      </Card>
    );
  }

  if (!health) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <span className="text-gray-500">Keine Workflow-Daten verfügbar</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {getHealthIcon(health.overallHealth)}
            Workflow-Status
          </CardTitle>
          {getHealthBadge(health.overallHealth)}
        </div>
        <CardDescription>
          Übersicht über alle SAP-Workflow-Prozesse
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Database className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{health.totalWorkflows}</div>
            <div className="text-sm text-gray-600">Gesamt</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{health.activeWorkflows}</div>
            <div className="text-sm text-gray-600">Aktiv</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{health.runningWorkflows}</div>
            <div className="text-sm text-gray-600">Laufend</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{health.failedWorkflows}</div>
            <div className="text-sm text-gray-600">Fehler</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}